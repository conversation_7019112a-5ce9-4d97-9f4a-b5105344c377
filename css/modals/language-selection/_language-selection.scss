
.language-dropdown-menu {
    max-width: 200px;
    background-color: transparent;
    cursor: pointer;

    .language-selector-trigger-container{
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 4px;

        span {
            color: #fff;
            font-size: 15px;
            font-weight: 500;
        }
    }

    .selected-option{
        background-color: #333666 !important;
        color: #ffffff !important;
    }

    // Modifier class for light backgrounds (where text should be dark)
    &.light-background {
        .language-selector-trigger-container {
            span {
                color: #333666;
            }
        }
    }

    // Modifier class for dark backgrounds (where text should be white) - explicit for clarity
    &.dark-background {
        .language-selector-trigger-container {
            span {
                color: #fff;
            }
        }
    }
}
