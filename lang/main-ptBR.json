{"addPeople": {"add": "<PERSON><PERSON><PERSON>", "addContacts": "Convide seus contatos", "contacts": "contatos", "copyInvite": "Copiar convite de reunião", "copyLink": "Copiar link da reunião", "copyStream": "Copiar link de transmissão ao vivo", "countryNotSupported": "Ainda não apoiamos este destino.", "countryReminder": "Ligando para fora dos EUA? ", "defaultEmail": "Seu e-mail padrão", "disabled": "Você não pode convidar pessoas.", "doYouWantToRemoveThisPerson": "Você deseja remover esta pessoa", "failedToAdd": "Falha ao adicionar participantes", "footerText": "A discagem externa está desativada.", "googleCalendar": "Google Agenda", "googleEmail": "E-mail do Google", "inviteMoreHeader": "Você é o único na reunião", "inviteMoreMailSubject": "Juntar {{appName}} reunião", "inviteMorePrompt": "Convide mais pessoas", "linkCopied": "Link copiado para a área de transferência", "loading": "Procurando pessoas e números de telefone", "loadingNumber": "Validando número de telefone", "loadingPeople": "Procurando pessoas para convidar", "loadingText": "Carregando...", "noResults": "Nenhum resultado de pesquisa correspondente", "noValidNumbers": "Por favor insira um número de telefone", "outlookEmail": "E-mail do Outlook", "phoneNumbers": "números de telefone", "searching": "Procurando...", "searchNumbers": "Adicionar números de telefone", "searchPeople": "Procure por pessoas", "searchPeopleAndNumbers": "Pesquise pessoas ou adicione seus números de telefone", "sendWhatsa[pp": "WhatsApp", "shareInvite": "Compartilhar convite de reunião", "shareInviteP": "Compartilhar convite de reunião com senha", "shareLink": "Compartilhe o link da reunião para convidar outras pessoas", "shareStream": "Compartilhe o link da transmissão ao vivo", "sipAddresses": "endereços sip", "telephone": "Telefone: {{number}}", "title": "Convidar pessoas para esta reunião", "yahooEmail": "E-mail do Yahoo"}, "audioDevices": {"bluetooth": "Bluetooth", "headphones": "Fones de ouvido", "none": "Nenhum dispositivo de áudio disponível", "phone": "Telefone", "speaker": "<PERSON><PERSON><PERSON><PERSON>"}, "audioOnly": {"audioOnly": "Largura de banda baixa"}, "breakoutRooms": {"actions": {"add": "Adicionar sala simultân<PERSON> (Beta)", "autoAssign": "Atribuir automaticamente às salas simultâneas", "close": "<PERSON><PERSON><PERSON>", "join": "Entrar", "leaveBreakoutRoom": "<PERSON><PERSON> da sala simultânea", "more": "<PERSON><PERSON>", "remove": "Remover", "rename": "Renomear", "renameBreakoutRoom": "Renomear sala simultânea", "sendToBreakoutRoom": "Enviar participante para:"}, "breakoutList": "Lista de salas simultâneas", "buttonLabel": "Salas simultâneas", "defaultName": "Sala simultânea #{{index}}", "hideParticipantList": "Ocultar lista de participantes", "mainRoom": "Sala principal", "notifications": {"joined": "Entrando na sala simultânea \"{{name}}\"", "joinedMainRoom": "Entrando na sala principal", "joinedTitle": "Salas Simultâneas"}, "showParticipantList": "Mostrar lista de participantes", "title": "Salas Simultâneas"}, "calendarSync": {"addMeetingURL": "Adicionar um link de reunião", "confirmAddLink": "Deseja adicionar um link Meet Hour a este evento?", "error": {"appConfiguration": "A integração do calendário não está configurada corretamente.", "generic": "Ocorreu um erro. ", "notSignedIn": "Ocorreu um erro ao autenticar para ver os eventos do calendário. "}, "join": "Juntar", "joinTooltip": "Participe da reunião", "nextMeeting": "próxima reunião", "noEvents": "Não há eventos programados.", "ongoingMeeting": "reunião em andamento", "permissionButton": "<PERSON><PERSON>r configuraç<PERSON><PERSON>", "permissionMessage": "A permissão do Calendário é necessária para ver suas reuniões no aplicativo.", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "today": "Hoje"}, "carmode": {"actions": {"selectSoundDevice": "Selecione o dispositivo de som"}, "labels": {"buttonLabel": "Modo carro", "title": "Modo carro", "videoStopped": "Seu vídeo está parado"}}, "chat": {"enter": "Entre na sala de bate-papo", "error": "Erro: sua mensagem não foi enviada.  {{error}}", "fieldPlaceHolder": "Digite sua mensagem aqui", "message": "Mensagem", "messageAccessibleTitle": "{{user}} diz:", "messageAccessibleTitleMe": "eu diz:", "messagebox": "Digite uma mensagem", "messageTo": "Mensagem privada para {{recipient}}", "nickname": {"popover": "Escolha um apelido", "title": "Digite um apelido para usar o chat"}, "noMessagesMessage": "Ainda não há mensagens na reunião. ", "privateNotice": "Mensagem privada para {{recipient}}", "smileysPanel": "<PERSON><PERSON> de emojis", "tabs": {"chat": "<PERSON><PERSON> papo", "polls": "<PERSON><PERSON><PERSON>"}, "title": "Bate-papo e enquetes", "titleWithPolls": "Bate-papo e enquetes", "you": "você"}, "chromeExtensionBanner": {"buttonText": "Instale a extensão do Chrome", "close": "<PERSON><PERSON><PERSON>", "dontShowAgain": "<PERSON>ão me mostre isso de novo", "installExtensionText": "Instale a extensão para integração do Google Agenda e do Office 365"}, "clickandpledge": {"errorDesc": "Insira o GUID válido do Click and Pledge Connect. ", "errorNotification": "GUID de clique e promessa inválidos", "title": "Configurações de doação do C&P Connect", "titlenative": "Clique e comprometa-se"}, "connectingOverlay": {"joiningRoom": "Conectando você à sua reunião..."}, "connection": {"ATTACHED": "<PERSON><PERSON><PERSON>", "AUTHENTICATING": "Autenticando", "AUTHFAIL": "Falha na autenticação", "CONNECTED": "Conectado", "CONNECTING": "<PERSON><PERSON><PERSON><PERSON>", "CONNFAIL": "Falha na conexão", "DISCONNECTED": "Desconectado", "DISCONNECTING": "Desconectando", "ERROR": "Erro", "FETCH_SESSION_ID": "Obtendo ID de sessão...", "GET_SESSION_ID_ERROR": "Obtenha erro de <PERSON> de sessão: {{code}}", "GOT_SESSION_ID": "Obtendo o ID da sessão... Concluído", "LOW_BANDWIDTH": "Vídeo para {{displayName}} foi desativado para economizar largura de banda"}, "connectionindicator": {"address": "Endereço:", "audio_ssrc": "SSRC de áudio:", "bandwidth": "Largura de banda estimada:", "bitrate": "Taxa de bits:", "bridgeCount": "Contagem de servidores: ", "codecs": "Codecs (A/V): ", "connectedTo": "Conectado a:", "e2e_rtt": "E2E RTT:", "framerate": "Taxa de quadros:", "less": "<PERSON><PERSON> menos", "localaddress": "Endereço local:", "localaddress_plural": "Endereços locais:", "localport": "Porto local:", "localport_plural": "Portas locais:", "maxEnabledResolution": "enviar máximo", "more": "<PERSON><PERSON> mais", "packetloss": "<PERSON><PERSON> de paco<PERSON>:", "participant_id": "ID do participante:", "quality": {"good": "Bo<PERSON>", "inactive": "Inativo", "lost": "<PERSON><PERSON><PERSON>", "nonoptimal": "Não ideal", "poor": "Pobre"}, "remoteaddress": "<PERSON><PERSON><PERSON><PERSON> remoto:", "remoteaddress_plural": "Endereços remotos:", "remoteport": "Porta remota:", "remoteport_plural": "Portas remotas:", "resolution": "Resolução:", "savelogs": "<PERSON>var registros", "status": "Conexão:", "transport": "Transporte:", "transport_plural": "Transportes:", "video_ssrc": "SSRC de vídeo:"}, "dateUtils": {"earlier": "<PERSON><PERSON> cedo", "today": "Hoje", "yesterday": "Ontem"}, "deepLinking": {"appNotInstalled": "Use nosso {{app}} aplicativo móvel para participar desta reunião em seu telefone.", "continueWithBrowser": "Continuar com o navegador", "description": "Nada aconteceu?  {{app}} aplicativo de desktop.  {{app}} aplicativo da web.", "descriptionWithoutWeb": "Nada aconteceu?  {{app}} aplicativo de desktop.", "downloadApp": "Baixe o aplicativo", "ifDoNotHaveApp": "Se você ainda não tem o aplicativo:", "ifHaveApp": "Se você já possui o aplicativo:", "ifYouDontHaveTheAppYet": "Se você ainda não tem o aplicativo", "joinInApp": "Participe desta reunião usando o aplicativo", "joinMeetingWithDesktopApp": "Participe da reunião com o aplicativo para desktop", "launchMeetingInDesktopApp": "Iniciar reunião no aplicativo de desktop", "launchWebButton": "Lançar na web", "title": "Iniciando sua reunião em {{app}}...", "tryAgainButton": "Tente novamente no desktop"}, "defaultLink": "por exemplo {{url}}", "defaultNickname": "ex. ", "deviceError": {"cameraError": "Falha ao acessar sua câmera", "cameraPermission": "Erro ao obter permissão da câmera", "microphoneError": "Falha ao acessar seu microfone", "microphonePermission": "Erro ao obter permissão do microfone"}, "deviceSelection": {"noPermission": "Permissão não concedida", "previewUnavailable": "Visualização indisponível", "selectADevice": "Selecione um dispositivo", "testAudio": "Reproduzir um som de teste"}, "dialog": {"accessibilityLabel": {"liveStreaming": "Transmissão ao vivo"}, "add": "<PERSON><PERSON><PERSON><PERSON>", "allow": "<PERSON><PERSON><PERSON>", "alreadySharedVideoMsg": "Outro participante já está compartilhando um vídeo. ", "alreadySharedVideoTitle": "Apenas um vídeo compartilhado é permitido por vez", "applicationWindow": "Janela do aplicativo", "authenticationRequired": "Autenticação necessária", "Back": "Voltar", "cameraConstraintFailedError": "Sua câmera não atende a algumas das restrições exigidas.", "cameraNotFoundError": "A câmera não foi encontrada.", "cameraNotSendingData": "Não conseguimos acessar sua câmera. ", "cameraNotSendingDataTitle": "Não foi possível acessar a câmera", "cameraPermissionDeniedError": "Você não concedeu permissão para usar sua câmera. ", "cameraTimeoutError": "Não foi possível iniciar a fonte de vídeo. ", "cameraUnknownError": "Não é possível usar a câmera por motivo desconhecido.", "cameraUnsupportedResolutionError": "Sua câmera não suporta a resolução de vídeo necessária.", "Cancel": "<PERSON><PERSON><PERSON>", "cannotToggleScreenSharingNotSupported": "Não é possível alternar o compartilhamento de tela: não suportado.", "close": "<PERSON><PERSON><PERSON>", "closingAllTerminals": "<PERSON><PERSON><PERSON> todos os terminais", "conferenceDisconnectMsg": "Você pode querer verificar sua conexão de rede.  {{seconds}} segundo...", "conferenceDisconnectTitle": "Você foi desconectado.", "conferenceReloadMsg": "Estamos tentando consertar isso.  {{seconds}} segundo...", "conferenceReloadTitle": "Infelizmente, algo deu errado.", "confirm": "Confirmar", "confirmNo": "Não", "confirmYes": "<PERSON>m", "connectError": "Ops! ", "connectErrorWithMsg": "Ops!  {{msg}}", "connecting": "<PERSON><PERSON><PERSON><PERSON>", "contactSupport": "Contate o suporte", "copied": "Copiado", "copy": "Cópia", "customAwsRecording": "Gravação personalizada do AWS", "deleteCache": "Excluir cache ", "dismiss": "Liberar", "displayNameRequired": "Oi! ", "displayUserName": "", "donationCNotificationTitle": "<PERSON><PERSON> via Click and Pledge", "donationCNPLabel": "Insira o URL do formulário do Connect ou o URL do widget", "donationLabel": "Insira o URL da campanha de doação", "donationNotificationDescription": "Doe-nos para apoiar nossa causa ", "donationNotificationTitle": "Doe via Donorbox", "done": "<PERSON><PERSON>", "e2eeDescription": "A criptografia ponta a ponta é atualmente EXPERIMENTAL. ", "e2eeLabel": "Habilite a criptografia ponta a ponta", "e2eeWarning": "AVISO: <PERSON>em todos os participantes desta reunião parecem ter suporte para criptografia ponta a ponta. ", "embedMeeting": "Incorporar reunião", "enterCdonation": "Exemplo: https://connect.clickandpledge.com/w/Form/283ah-0069-441a-ba0f-d42a9dec9821", "enterDisplayEmail": "ID de e-mail", "enterDisplayName": "Nome completo", "enterDisplayNameToJoin": "Por favor digite seu nome para participar", "enterDonation": "Exemplo: https://donorbox.org/donate-an-organisation", "enterMeetingId": "Insira o ID da reunião", "enterMeetingPassword": "Digite a senha da reunião", "error": "Erro", "errorMeetingID": "Adicionar ID da reunião", "errorMeetingPassword": "Adicionar <PERSON> da <PERSON>ão", "forceMuteEveryoneDialog": "Tem certeza de que deseja bloquear o microfone de todos, exceto dos moderadores? ", "forceMuteEveryoneElseDialog": "Silencie-os e desative o microfone", "forceMuteEveryoneElsesVideoDialog": "Depois que a câmera for desativada, eles não poderão ativar a câmera", "forceMuteEveryoneElsesVideoTitle": "Forçar o silenciamento da câmera de todos, exceto {{whom}}?", "forceMuteEveryoneElseTitle": "Forçar o silenciamento de todos, exceto {{whom}}?", "forceMuteEveryoneSelf": "você mesmo", "forceMuteEveryoneStartMuted": "Todo mundo começa a forçar o silenciamento a partir de agora", "forceMuteEveryonesVideoDialog": "Tem certeza de que deseja bloquear o vídeo deste participante? ", "forceMuteEveryonesVideoTitle": "Forçar o silenciamento do vídeo de todos?", "forceMuteEveryoneTitle": "Forçar o silenciamento de todos?", "forceMuteParticipantBody": "Forçar mudo do participante.", "forceMuteParticipantButton": "<PERSON><PERSON><PERSON>", "forceMuteParticipantDialog": "Tem certeza de que deseja bloquear o microfone deste participante? ", "forceMuteParticipantsVideoBody": "O vídeo dos participantes será desativado e eles não poderão ligá-lo novamente", "forceMuteParticipantsVideoButton": "Desativar câmer<PERSON>", "forceMuteParticipantsVideoTitle": "Desativar a câmera deste participante?", "forceMuteParticipantTitle": "Forçar o silenciamento deste participante?", "gracefulShutdown": "Nosso serviço está atualmente fora do ar para manutenção. ", "grantModeratorDialog": "Tem certeza de que deseja tornar este participante um moderador?", "grantModeratorTitle": "Moderador de concessão", "hangUpLeaveReason": "Esta reunião foi encerrada pelo moderador", "hideShareAudioHelper": "Não mostrar esta caixa de diálogo novamente", "IamHost": "Eu sou o anfitrião", "incorrectPassword": "Nome de usuário ou senha incorretos", "incorrectRoomLockPassword": "Senha incorreta", "internalError": "Ops!  {{error}}", "internalErrorTitle": "<PERSON>rro <PERSON>no", "kickMessage": "Ai! ", "kickParticipantButton": "Remover usuário", "kickParticipantDialog": "Tem certeza de que deseja remover este participante?", "kickParticipantTitle": "Remover este participante?", "kickTitle": "Ai! ", "liveStreaming": "Transmissão ao vivo", "liveStreamingDisabledBecauseOfActiveRecordingTooltip": "Não é possível enquanto a gravação está ativa", "liveStreamingDisabledForGuestTooltip": "Os convidados não podem iniciar a transmissão ao vivo.", "liveStreamingDisabledTooltip": "Iniciar transmissão ao vivo desabilitada.", "localUserControls": "Controles de usuário locais", "lockMessage": "Falha ao bloquear a conferência.", "lockRoom": "Adicionar reunião $t(lockRoomPasswordUppercase)", "lockTitle": "Falha no bloqueio", "login": "Conecte-se", "logoutQuestion": "Tem certeza de que deseja sair e interromper a conferência?", "logoutTitle": "<PERSON><PERSON>", "maxUsersLimitReached": "O limite máximo de participantes foi atingido. ", "maxUsersLimitReachedTitle": "Limite máximo de participantes atingido", "meetHourRecording": "Gravação de horas de encontro", "meetingID": "ID da reunião", "meetingIDandPassword": "Insira o ID da reunião e a senha", "meetingPassword": "Senha da reunião", "messageErrorApi": "opa! ", "messageErrorInvalid": "Credenciais inválidas", "messageErrorNotModerator": "Ops! ", "messageErrorNull": "Nome de usuário ou senha está vazio", "micConstraintFailedError": "Seu microfone não atende a algumas das restrições exigidas.", "micNotFoundError": "O microfone não foi encontrado.", "micNotSendingData": "Vá para as configurações do seu computador para ativar o som do microfone e ajustar seu nível", "micNotSendingDataTitle": "Seu microfone está silenciado pelas configurações do sistema", "micPermissionDeniedError": "Você não concedeu permissão para usar seu microfone. ", "micTimeoutError": "Não foi possível iniciar a fonte de áudio. ", "micUnknownError": "Não é possível usar o microfone por motivo desconhecido.", "muteEveryoneDialog": "Tem certeza de que deseja silenciar todos? ", "muteEveryoneElseDialog": "De<PERSON><PERSON> de silenciados, você não poderá reativar o som deles, mas eles poderão reativar o som a qualquer momento.", "muteEveryoneElsesVideoDialog": "Depois que a câmera for desativada, você não poderá ligá-la novamente, mas eles poderão ligá-la novamente a qualquer momento.", "muteEveryoneElsesVideoTitle": "Desative a câmera de todos, exceto {{whom}}?", "muteEveryoneElseTitle": "<PERSON>lenciar todos, exceto {{whom}}?", "muteEveryoneSelf": "você mesmo", "muteEveryoneStartMuted": "Todo mundo começa silenciado a partir de agora", "muteEveryonesVideoDialog": "Tem certeza de que deseja desativar a câmera de todos? ", "muteEveryonesVideoDialogOk": "Desativar", "muteEveryonesVideoTitle": "Desativar a câmera de todos?", "muteEveryoneTitle": "Silenciar todos?", "muteParticipantBody": "Você não poderá reativar o som deles, mas eles poderão reativar o som a qualquer momento.", "muteParticipantButton": "<PERSON><PERSON>", "muteParticipantDialog": "Tem certeza de que deseja silenciar este participante? ", "muteParticipantsVideoBody": "Você não poderá ligar a câmera novamente, mas eles poderão ligá-la novamente a qualquer momento.", "muteParticipantsVideoButton": "Desativar câmer<PERSON>", "muteParticipantsVideoDialog": "Tem certeza de que deseja desligar a câmera deste participante? ", "muteParticipantsVideoTitle": "Desativar a câmera deste participante?", "muteParticipantTitle": "Silenciar este participante?", "noDropboxToken": "Nenhum token válido do Dropbox", "noScreensharingInAudioOnlyMode": "Sem compartilhamento de tela no modo somente áudio", "Ok": "OK", "password": "<PERSON><PERSON>", "passwordLabel": "A reunião foi bloqueada por um moderador.  $t(lockRoomPassword) para participar.", "passwordNotSupported": "Marcando uma reunião $t(lockRoomPassword) não é suportado.", "passwordNotSupportedTitle": "$t(lockRoomPasswordUppercase) não suportado", "passwordRequired": "$t(lockRoomPasswordUppercase) obrigatório", "permissionCameraRequiredError": "É necessária permissão da câmera para participar de conferências com vídeo. ", "permissionErrorTitle": "Permissão necessária", "permissionMicRequiredError": "É necessária permissão de microfone para participar de conferências com áudio. ", "popupError": "Seu navegador está bloqueando janelas pop-up deste site. ", "popupErrorTitle": "Pop-up bloqueado", "readMore": "mais", "recording": "Gravação", "recordingDisabledBecauseOfActiveLiveStreamingTooltip": "Não é possível enquanto uma transmissão ao vivo estiver ativa", "recordingDisabledForGuestTooltip": "Os convidados não podem iniciar gravações.", "recordingDisabledTooltip": "Iniciar gravação desabilitada.", "rejoinNow": "Junte-se novamente agora", "remoteControlAllowedMessage": "{{user}} aceitou seu pedido de controle remoto!", "remoteControlDeniedMessage": "{{user}} rejeitou seu pedido de controle remoto!", "remoteControlErrorMessage": "Ocorreu um erro ao tentar solicitar permissões de controle remoto de {{user}}!", "remoteControlRequestMessage": "Você vai permitir {{user}} controlar remotamente sua área de trabalho?", "remoteControlShareScreenWarning": "Observe que se você pressionar \"Permitir\" você compartilhará sua tela!", "remoteControlStopMessage": "A sessão de controle remoto terminou!", "remoteControlTitle": "Controle remoto da área de trabalho", "remoteUserControls": "Controles de usuário remoto de {{username}}", "Remove": "Remover", "removeCDonation": "Guia de clique e promessa removido", "removeCDonationD": "O link de doação foi removido com sucesso", "removeDonation": "Link de doação Donorbox removido", "removeDonationD": "O link de doação foi removido com sucesso", "removePassword": "Remover $t(lockRoomPassword)", "removeSharedVideoMsg": "Tem certeza de que deseja remover seu vídeo compartilhado?", "removeSharedVideoTitle": "Remover vídeo compartilhado", "reservationError": "Erro no sistema de reservas", "reservationErrorMsg": "<PERSON><PERSON><PERSON> de erro: {{code}}, mensagem: {{msg}}", "restartInitiatedBecauseOfBridgeFailure": "Reinicialização iniciada devido a uma falha na ponte", "retry": "Tentar novamente", "revokeModeration": "Revogar o usuário como moderador?", "revokeModerationTitle": "<PERSON><PERSON><PERSON>", "screenSharingAudio": "<PERSON>mp<PERSON><PERSON><PERSON>", "screenSharingFailed": "Ops! ", "screenSharingFailedTitle": "Falha no compartilhamento de tela!", "screenSharingPermissionDeniedError": "Ops! ", "screenSharingUser": "{{displayName}} está atualmente compartilhando a tela", "sendPrivateMessage": "Você recebeu recentemente uma mensagem privada. ", "sendPrivateMessageCancel": "Enviar para o grupo", "sendPrivateMessageOk": "Enviar em particular", "sendPrivateMessageTitle": "Enviar em particular?", "serviceUnavailable": "Serviço não disponível", "sessionRestarted": "Chamada reiniciada pela ponte", "sessTerminated": "<PERSON><PERSON> encerrada", "Share": "Compartilhar", "shareAudio": "<PERSON><PERSON><PERSON><PERSON>", "shareAudioTitle": "Como compartilhar áudio", "shareAudioWarningD1": "você precisa interromper o compartilhamento de tela antes de compartilhar seu áudio.", "shareAudioWarningD2": "você precisa reiniciar o compartilhamento de tela e marcar a opção \"compartilhar áudio\".", "shareAudioWarningH1": "Se você quiser compartilhar apenas áudio:", "shareAudioWarningTitle": "Você precisa interromper o compartilhamento de tela antes de compartilhar <PERSON>o", "shareMediaWarningGenericH2": "Se você deseja compartilhar sua tela e áudio", "shareScreenWarningD1": "você precisa interromper o compartilhamento de áudio antes de compartilhar sua tela.", "shareScreenWarningD2": "você precisa interromper o compartilhamento de áudio, iniciar o compartilhamento de tela e marcar a opção \"compartilhar áudio\".", "shareScreenWarningH1": "Se você deseja compartilhar apenas sua tela:", "shareScreenWarningTitle": "Você precisa interromper o compartilhamento de áudio antes de compartilhar sua tela", "shareVideoLinkError": "Forneça um link correto do YouTube.", "shareVideoTitle": "Compartilhe Youtube", "shareYourScreen": "Compartilhe sua tela", "shareYourScreenDisabled": "Compartilhamento de tela desativado.", "shareYourScreenDisabledForGuest": "Os convidados não podem compartilhar a tela.", "startLiveStreaming": "Transmissão ao vivo + gravação", "startRecording": "Comece a gravar", "startRemoteControlErrorMessage": "Ocorreu um erro ao tentar iniciar a sessão de controle remoto!", "stopLiveStreaming": "<PERSON>re de transmit<PERSON>", "stopRecording": "<PERSON><PERSON> de gra<PERSON>", "stopRecordingWarning": "Tem certeza de que deseja interromper a gravação?", "stopStreamingWarning": "Tem certeza de que deseja interromper a transmissão ao vivo?", "streamKey": "Chave de transmissão ao vivo", "Submit": "Enviar", "switchInProgress": "Mudança em andamento.", "thankYou": "<PERSON><PERSON><PERSON> por usar {{appName}}!", "token": "ficha", "tokenAuthFailed": "<PERSON><PERSON><PERSON><PERSON>, você não tem permissão para participar desta chamada.", "tokenAuthFailedTitle": "Falha na autenticação", "transcribing": "Transcrever", "unforceMuteEveryoneDialog": "Tem certeza de que deseja desbloquear o microfone de todos? ", "unforceMuteEveryoneElseDialog": "Desfaça o silêncio deles e deixe-os ativar o microfone", "unforceMuteEveryoneElsesVideoDialog": "Assim que a câmera estiver habilitada, eles poderão habilitar sua câmera", "unforceMuteEveryoneElsesVideoTitle": "Ative a câmera de todos, exceto {{whom}}?", "unforceMuteEveryoneElseTitle": "<PERSON><PERSON><PERSON> Forçar silenciamento de todos, exceto {{whom}}?", "unforceMuteEveryoneSelf": "você mesmo", "unforceMuteEveryonesVideoDialog": "Tem certeza de que deseja desbloquear o vídeo de todos?", "unforceMuteEveryonesVideoTitle": "Ativar a câmera de todos?", "unforceMuteEveryoneTitle": "Desfazer Forçar silenciamento do microfone de todos?", "unforceMuteParticipantBody": "Desfazer silenciar participante.", "unforceMuteParticipantButton": "<PERSON><PERSON><PERSON> for<PERSON><PERSON>", "unforceMuteParticipantDialog": "Tem certeza de que deseja desbloquear o vídeo deste participante?.", "unforceMuteParticipantsVideoBody": "O vídeo dos participantes será ativado e eles poderão ativá-lo novamente", "unforceMuteParticipantsVideoButton": "Ativar câmera", "unforceMuteParticipantsVideoTitle": "Desbloquear o vídeo deste participante?", "unforceMuteParticipantTitle": "Desfazer Forçar silenciamento deste participante?", "unlockRoom": "Remover reunião $t(lockRoomPassword)", "user": "<PERSON><PERSON><PERSON><PERSON>", "userIdentifier": "Identificador do usuário", "userPassword": "Senha do usuário", "videoLink": "Link do vídeo", "viewUpgradeOptions": "Ver opções de atualização", "viewUpgradeOptionsContent": "Para obter acesso ilimitado a recursos premium, como gravação, transcrições, streaming RTMP e muito mais, você precisará atualizar seu plano.", "viewUpgradeOptionsTitle": "Você descobriu um recurso premium!", "WaitForHostMsg": "A conferência <b>{{room}}</b> ainda não começou. ", "WaitForHostMsgWOk": "A conferência <b>{{room}}</b> ainda não começou. ", "WaitforModerator": "Aguarde a chegada do moderador", "WaitforModeratorOk": "Volte", "WaitingForHost": "<PERSON><PERSON><PERSON><PERSON> pelo an<PERSON>...", "WaitingForHostTitle": "<PERSON><PERSON><PERSON><PERSON> pelo an<PERSON>...", "Yes": "<PERSON>m", "yourEntireScreen": "Sua tela inteira"}, "dialOut": {"statusMessage": "é agora {{status}}"}, "documentSharing": {"title": "LivePad"}, "donorbox": {"errorDesc": "Insira um URL válido da campanha Donorbox. ", "errorNotification": "URL da caixa de doadores inválida", "title": "Adicionar URL da campanha DonorBox", "titlenative": "CAIXA DE DOADOR"}, "e2ee": {"labelToolTip": "A comunicação de áudio e vídeo nesta chamada é criptografada de ponta a ponta"}, "embedMeeting": {"title": "Incorporar esta reunião"}, "feedback": {"average": "Média", "bad": "<PERSON><PERSON><PERSON>", "detailsLabel": "Conte-nos mais sobre isso.", "good": "Bo<PERSON>", "rateExperience": "Avalie sua experiência de reunião", "star": "Estrela", "veryBad": "<PERSON><PERSON> ruim", "veryGood": "<PERSON><PERSON> bom"}, "giphy": {"giphy": "GIPHY", "noResults": "Nenhum resultado encontrado :(", "search": "Pesquisar GIPHY"}, "helpView": {"header": "Central de ajuda"}, "incomingCall": {"answer": "<PERSON><PERSON><PERSON><PERSON>", "audioCallTitle": "<PERSON><PERSON> recebida", "decline": "Liberar", "productLabel": "da hora do encontro", "videoCallTitle": "Chamada de vídeo recebida"}, "info": {"accessibilityLabel": "Mostrar informações", "addPassword": "Adicionar $t(lockRoomPassword)", "cancelPassword": "Cancelar $t(lockRoomPassword)", "conferenceURL": "Link:", "copyNumber": "Copiar nú<PERSON>o", "country": "<PERSON><PERSON>", "dialANumber": "Para ingressar na reunião, disque um desses números e insira o PIN.", "dialInConferenceID": "ALFINETE:", "dialInNotSupported": "<PERSON><PERSON><PERSON><PERSON>, a discagem não é suportada no momento.", "dialInNumber": "Discagem:", "dialInSummaryError": "Erro ao buscar informações de discagem agora. ", "dialInTollFree": "Ligação gratuita", "genericError": "<PERSON><PERSON>, algo deu errado.", "inviteLiveStream": "Para assistir a transmissão ao vivo desta reunião, clique neste link: {{url}}", "invitePhone": "Para participar por telefone, toque aqui: {{number}},,{{conferenceID}}#\n", "invitePhoneAlternatives": "Procurando um número de discagem diferente?\n {{url}}\n\n\nSe também estiver discando pelo telefone da sala, entre sem conectar ao áudio: {{silentUrl}}", "inviteSipEndpoint": "Para ingressar usando o endereço SIP, digite isto: {{sipUri}}", "inviteTextiOSInviteUrl": "Clique no link a seguir para participar: {{inviteUrl}}.", "inviteTextiOSJoinSilent": "Se você estiver discando pelo telefone da sala, use este link para entrar sem conectar ao áudio: {{silentUrl}}.", "inviteTextiOSPersonal": "{{name}} está convidando você para uma reunião.", "inviteTextiOSPhone": "Para participar por telefone, use este número: {{number}},,{{conferenceID}}#.  {{didUrl}}.", "inviteURLFirstPartGeneral": "Você está convidado a participar de uma reunião.", "inviteURLFirstPartPersonal": "{{name}} está convidando você para uma reunião.\n", "inviteURLSecondPart": "\nParticipe da reunião:\n{{url}}\n", "label": "Informações de discagem", "liveStreamURL": "Transmissão ao vivo:", "moreNumbers": "<PERSON><PERSON>", "noNumbers": "Sem números de discagem.", "noPassword": "<PERSON><PERSON><PERSON>", "noRoom": "Nenhuma sala foi especificada para discagem.", "numbers": "Números de discagem", "password": "$t(lockRoomPasswordUppercase):", "sip": "Endereço SIP", "title": "Compartilhar", "tooltip": "Compartilhe o link e as informações de discagem desta reunião"}, "inlineDialogFailure": {"msg": "Tropeçamos um pouco.", "retry": "Tente novamente", "support": "<PERSON><PERSON><PERSON>", "supportMsg": "Se isso continuar acontecendo, entre em contato com"}, "inviteDialog": {"alertText": "Falha ao convidar alguns participantes.", "header": "<PERSON><PERSON><PERSON>", "searchCallOnlyPlaceholder": "Digite o número de telefone", "searchPeopleOnlyPlaceholder": "Procure por participantes", "searchPlaceholder": "Participante ou número de telefone", "send": "Enviar"}, "jitsiHome": "{{logo}} Logotip<PERSON>, links para a página inicial", "keyboardShortcuts": {"focusLocal": "Concentre-se no seu vídeo", "focusRemote": "Concentre-se no vídeo de outra pessoa", "fullScreen": "<PERSON>er ou sair da tela inteira", "keyboardShortcuts": "Atalhos de teclado", "localRecording": "Mostrar ou ocultar controles de gravação locais", "mute": "Ative ou desative o som do seu microfone", "pushToTalk": "Empurre para falar", "raiseHand": "Levante ou abaixe a mão", "showSpeakerStats": "Mostrar estatísticas do palestrante", "toggleChat": "<PERSON>bra ou feche o bate-papo", "toggleFilmstrip": "Mostrar ou ocultar miniaturas de vídeos", "toggleParticipantsPane": "Mostrar ou ocultar o painel de participantes", "toggleScreensharing": "Alternar entre câmera e compartilhamento de tela", "toggleShortcuts": "Mostrar ou ocultar atalhos de teclado", "videoMute": "Inicie ou pare sua câmera", "videoQuality": "Gerencie a qualidade da chamada"}, "liveChatView": {"header": "Suporte ao vivo 24 horas por dia, 7 dias por semana"}, "liveStreaming": {"addStream": "<PERSON><PERSON><PERSON><PERSON>", "busy": "Estamos trabalhando para liberar recursos de streaming. ", "busyTitle": "Todos os streamers estão ocupados no momento", "changeSignIn": "Troque de conta.", "choose": "Escolha uma transmissão ao vivo", "chooseCTA": "Escolha uma opção de streaming.  {{email}}.", "enterLinkedInUrlWithTheKey": "Digite o URL do LinkedIn com a chave", "enterStreamKey": "Insira sua chave de transmissão ao vivo do YouTube aqui.", "enterStreamKeyFacebook": "Insira sua chave de transmissão ao vivo do Facebook aqui.", "enterStreamKeyInstagram": "Insira sua chave de transmissão ao vivo do Instagram aqui.", "enterStreamKeyYouTube": "Digite seu {{youtube}} chave da transmissão ao vivo aqui.", "error": "A transmissão ao vivo falhou. ", "errorAPI": "Ocorreu um erro ao acessar suas transmissões do YouTube. ", "errorLiveStreamNotEnabled": "A transmissão ao vivo não está ativada {{email}}. ", "expandedOff": "A transmissão ao vivo foi interrompida", "expandedOn": "A reunião está sendo transmitida no YouTube.", "expandedPending": "A transmissão ao vivo está sendo iniciada...", "failedToStart": "Falha ao iniciar a transmissão ao vivo", "failToStartAutoLiveStreaming": "Falha ao iniciar a transmissão automática ao vivo", "failToStartAutoRecording": "Falha ao iniciar a gravação automática", "getStreamKeyManually": "Não foi possível buscar nenhuma transmissão ao vivo. ", "googlePrivacyPolicy": "Política de privacidade do Google", "invalidStreamKey": "A chave da transmissão ao vivo pode estar incorreta.", "limitNotificationDescriptionNative": "Sua transmissão será limitada a {{limit}} min.  {{app}}.", "limitNotificationDescriptionWeb": "<PERSON><PERSON> à alta demanda, seu streaming será limitado a {{limit}} min.  <a href={{url}} rel='noopener noreferrer' target='_blank'>{{app}}</a>.", "makeSureYouHaveEnoughStorageAvailableOnYourAccount": "Certifique-se de ter armazenamento suficiente disponível em sua conta.", "note": "Observação", "off": "A transmissão ao vivo foi interrompida", "offBy": "{{name}} interrompeu a transmissão ao vivo", "on": "A transmissão ao vivo começou", "onBy": "{{name}} iniciou a transmissão ao vivo", "pending": "Iniciando transmissão ao vivo...", "pleaseContactSupportForAssistance": "Entre em contato com o suporte para obter assistência.", "serviceName": "Serviço de transmissão ao vivo", "signedInAs": "Você está atualmente conectado como:", "signIn": "Faça login com o Google", "signInCTA": "<PERSON><PERSON>ça login ou insira sua chave de transmissão ao vivo do YouTube.", "signOut": "sair", "start": "Gravação + transmissão ao vivo", "startService": "In<PERSON>ar <PERSON><PERSON>", "streamIdHelp": "O que é isso?", "unavailableTitle": "Transmissão ao vivo indisponível", "youtubeTerms": "Termos de serviço do YouTube"}, "lobby": {"admit": "<PERSON><PERSON><PERSON>", "admitAll": "<PERSON><PERSON><PERSON> tudo", "allow": "<PERSON><PERSON><PERSON>", "backToKnockModeButton": "<PERSON><PERSON> senha, peça para entrar", "dialogTitle": "Modo lobby", "disableDialogContent": "O modo lobby está ativado no momento. ", "disableDialogSubmit": "Desativar", "emailField": "Digite seu endereço de e-mail", "enableDialogPasswordField": "<PERSON><PERSON><PERSON> (opcional)", "enableDialogSubmit": "Habilitar", "enableDialogText": "O modo Lobby permite que você proteja sua reunião, permitindo a entrada de pessoas somente após a aprovação formal de um moderador.", "enterPasswordButton": "Digite a senha da reunião", "enterPasswordTitle": "Digite a senha para entrar na reunião", "invalidPassword": "<PERSON><PERSON>", "joiningMessage": "Você participará da reunião assim que alguém aceitar sua solicitação", "joiningTitle": "Solicitando para participar da reunião...", "joiningWithPasswordTitle": "Entrando com senha...", "joinRejectedMessage": "Sua solicitação de adesão foi rejeitada por um moderador.", "joinTitle": "Participar da reunião", "joinWithPasswordMessage": "Tentando entrar com senha, aguarde...", "knockButton": "Peça para participar", "knockingParticipantList": "Lista de participantes batendo", "knockTitle": "Alguém quer participar da reunião", "nameField": "Digite seu nome", "notificationLobbyAccessDenied": "{{targetParticipantName}} foi rejeitado para aderir por {{originParticipantName}}", "notificationLobbyAccessGranted": "{{targetParticipantName}} foi autorizado a aderir por {{originParticipantName}}", "notificationLobbyDisabled": "O lobby foi desativado por {{originParticipantName}}", "notificationLobbyEnabled": "O lobby foi ativado por {{originParticipantName}}", "notificationTitle": "Salão", "passwordField": "Digite a senha da reunião", "passwordJoinButton": "Juntar", "reject": "<PERSON><PERSON><PERSON><PERSON>", "rejectAll": "<PERSON><PERSON><PERSON><PERSON> tudo", "toggleLabel": "Ativar lobby"}, "localRecording": {"clientState": {"off": "Des<PERSON><PERSON>", "on": "Sobre", "unknown": "Desconhecido"}, "dialogTitle": "Controles de gravação local", "duration": "Duração", "durationNA": "N / D", "encoding": "Codificação", "label": "LOR", "labelToolTip": "A gravação local está ativada", "localRecording": "Gravação local", "me": "<PERSON><PERSON>", "messages": {"engaged": "Gravação local ativada.", "finished": "Sessão de gravação {{token}} finalizado. ", "finishedModerator": "Sessão de gravação {{token}} finalizado. ", "notModerator": "Você não é o moderador. "}, "moderator": "Moderador", "no": "Não", "participant": "Participante", "participantStats": "Estatísticas dos participantes", "sessionToken": "Token de sessão", "start": "Comece a gravar", "stop": "<PERSON><PERSON> de gra<PERSON>", "yes": "<PERSON>m"}, "lockRoomPassword": "<PERSON><PERSON>a", "lockRoomPasswordUppercase": "<PERSON><PERSON>", "lonelyMeetingExperience": {"button": "Convide outras pessoas", "youAreAlone": "Você é o único na reunião"}, "me": "meu", "notify": {"connectedOneMember": "{{name}} entrou na reunião", "connectedThreePlusMembers": "{{name}} e {{count}} outros participaram da reunião", "connectedTwoMembers": "{{first}} e {{second}} entrou na reunião", "disconnected": "desconectado", "focus": "Foco da conferência", "focusFail": "{{component}} não disponível - tente novamente {{ms}} segundo", "grantedTo": "Direitos de moderador concedidos a {{to}}!", "groupTitle": "Notificações", "hostAskedUnmute": "O anfitrião gostaria que você ativasse o som", "invitedOneMember": "{{name}} foi convidado", "invitedThreePlusMembers": "{{name}} e {{count}} outros foram convidados", "invitedTwoMembers": "{{first}} e {{second}} foram convidados", "kickParticipant": "{{kicked}} foi removido por {{kicker}}", "me": "<PERSON><PERSON>", "moderationInEffectCSDescription": "Levante a mão se quiser compartilhar seu vídeo", "moderationInEffectCSTitle": "O compartilhamento de conteúdo foi desativado pelo moderador", "moderationInEffectDescription": "Por favor, levante a mão se quiser falar", "moderationInEffectTitle": "O microfone é silenciado pelo moderador", "moderationInEffectVideoDescription": "Levante a mão se quiser que seu vídeo fique visível", "moderationInEffectVideoTitle": "O vídeo foi silenciado pelo moderador", "moderationRequestFromModerator": "O anfitrião gostaria que você ativasse o som", "moderationRequestFromParticipant": "Quer falar", "moderationStartedTitle": "Moderação iniciada", "moderationStoppedTitle": "Moderação interrompida", "moderationToggleDescription": "por {{participantDisplayName}}", "moderator": "Direitos de moderador concedidos!", "muted": "Você iniciou a conversa sem som.", "mutedRemotelyDescription": "Você sempre pode ativar o som quando estiver pronto para falar. ", "mutedRemotelyTitle": "Você foi silenciado por {{participantDisplayName}}!", "mutedTitle": "Você está mudo!", "newDeviceAction": "<PERSON>ar", "newDeviceAudioTitle": "Novo dispositivo de áudio detectado", "newDeviceCameraTitle": "Nova câmera detectada", "OldElectronAPPTitle": "Vulnerabilidade de segurança!", "oldElectronClientDescription1": "Parece que você está usando uma versão antiga do cliente Meet Hour que apresenta vulnerabilidades de segurança conhecidas.  ", "oldElectronClientDescription2": "última compilação", "oldElectronClientDescription3": " agora!", "passwordRemovedRemotely": "$t(lockRoomPasswordUppercase) removido por outro participante", "passwordSetRemotely": "$t(lockRoomPasswordUppercase) definido por outro participante", "raisedHand": "{{name}} gostaria de fala<PERSON>.", "raiseHandAction": "Levante a mão", "reactionSounds": "<PERSON><PERSON><PERSON> sons", "reactionSoundsForAll": "Desative sons para todos", "screenShareNoAudio": " A caixa Compartilhar áudio não foi marcada na tela de seleção de janela.", "screenShareNoAudioTitle": "Não foi possível compartilhar o áudio do sistema!", "somebody": "Alguém", "startSilentDescription": "Volte à reunião para ativar o áudio", "startSilentTitle": "Você entrou sem saída de áudio!", "suboptimalBrowserWarning": "Tememos que sua experiência de reunião não seja tão boa aqui.  <a href='{{recommendedBrowserPageLink}}' target='_blank'>navegadores totalmente suportados</a>.", "suboptimalExperienceTitle": "Aviso do navegador", "unmute": "Ativar som", "videoMutedRemotelyDescription": "Você sempre pode ligá-lo novamente.", "videoMutedRemotelyTitle": "Sua câmera foi desativada por {{participantDisplayName}}!"}, "participantsPane": {"actions": {"allow": "<PERSON><PERSON><PERSON> que os participantes:", "askUnmute": "Peça para ativar o som", "blockEveryoneMicCamera": "Bloqueie o microfone e a câmera de todos", "forceMute": "Forçar silenciamento de áudio", "forceMuteAll": "Forçar silenciamento de tudo", "forceMuteAllVideo": "Forçar vídeo mudo tudo", "forceMuteEveryoneElse": "Forçar o silenciamento de todos os outros", "forceMuteEveryoneElseVideo": "Forçar o silenciamento de todos os outros vídeos", "forceMuteVideo": "Forçar vídeo mudo", "invite": "<PERSON><PERSON>", "mute": "<PERSON><PERSON>", "muteAll": "Silenciar tudo", "muteEveryoneElse": "Silenciar todos os outros", "startModeration": "Ative o som ou inicie o vídeo", "stopEveryonesVideo": "Pare o vídeo de todos", "stopVideo": "Parar vídeo", "unblockEveryoneMicCamera": "Desbloqueie o microfone e a câmera de todos", "unForceMute": "desfazer forçar silenciamento de áudio", "unForceMuteAll": "<PERSON><PERSON><PERSON> forçar silenciamento de tudo", "unforceMuteAllVideo": "<PERSON><PERSON><PERSON> forçar vídeo silenciar tudo", "unforceMuteEveryoneElse": "Desfazer forçar o silenciamento de todos os outros", "unforceMuteEveryoneElseVideo": "Desfazer forçar o silenciamento de todos os outros vídeos", "unForceMuteVideo": "Desfazer forçar silenciamento de vídeo"}, "close": "<PERSON><PERSON><PERSON>", "header": "Participantes", "headings": {"lobby": "Salão ({{count}})", "participantsList": "Participantes da reunião ({{count}})", "waitingLobby": "<PERSON><PERSON>and<PERSON> no saguão ({{count}})"}, "search": "Pesquisar participantes"}, "passwordDigitsOnly": "Até {{number}} d<PERSON><PERSON><PERSON>", "passwordSetRemotely": "definido por outro participante", "polls": {"answer": {"skip": "<PERSON><PERSON>", "submit": "Enviar"}, "by": "Por {{ name }}", "create": {"addOption": "Adicionar <PERSON>", "answerPlaceholder": "Opção {{index}}", "cancel": "<PERSON><PERSON><PERSON>", "create": "<PERSON>rie uma enquete", "pollOption": "Opção de enquete {{index}}", "pollQuestion": "<PERSON><PERSON><PERSON>", "questionPlaceholder": "Faça uma pergunta", "removeOption": "Opção de remoção", "send": "Enviar"}, "notification": {"description": "Abra a guia de enquetes para votar", "title": "Uma nova enquete foi adicionada a esta reunião"}, "results": {"changeVote": "Alterar voto", "empty": "Ainda não há pesquisas na reunião. ", "hideDetailedResults": "<PERSON><PERSON><PERSON><PERSON> de<PERSON>", "showDetailedResults": "<PERSON><PERSON> de<PERSON>", "vote": "Voto"}}, "poweredby": "© Meet Hour LLC", "prejoin": {"alreadyOneConferenceIsRunningInBackground": "Já há uma conferência em execução em segundo plano.", "audioAndVideoError": "Erro de áudio e vídeo:", "audioDeviceProblem": "Há um problema com seu dispositivo de áudio", "audioOnlyError": "Erro de áudio:", "audioTrackError": "Não foi possível criar a faixa de áudio.", "calling": "<PERSON><PERSON><PERSON>", "callMe": "Liga para mim", "callMeAtNumber": "Ligue-me neste número:", "configuringDevices": "Configurando dispositivos...", "connectedWithAudioQ": "Você está conectado com áudio?", "connection": {"good": "Sua conexão com a Internet parece boa!", "nonOptimal": "Sua conexão com a Internet não é a ideal", "poor": "Você tem uma conexão de internet ruim"}, "connectionDetails": {"audioClipping": "Esperamos que seu áudio seja cortado.", "audioHighQuality": "Esperamos que seu áudio tenha excelente qualidade.", "audioLowNoVideo": "Esperamos que a qualidade do seu áudio seja baixa e sem vídeo.", "goodQuality": "Incrível! ", "noMediaConnectivity": "Não foi possível encontrar uma maneira de estabelecer conectividade de mídia para este teste. ", "noVideo": "Esperamos que seu vídeo seja terrível.", "undetectable": "Se você ainda não conseguir fazer chamadas no navegador, recomendamos que você verifique se os alto-falantes, o microfone e a câmera estão configurados corretamente, se você concedeu ao seu navegador direitos para usar o microfone e a câmera e se a versão do seu navegador está atualizada. ", "veryPoorConnection": "Esperamos que a qualidade da sua chamada seja realmente terrível.", "videoFreezing": "Esperamos que seu vídeo congele, fique preto e pixelizado.", "videoHighQuality": "Esperamos que seu vídeo tenha boa qualidade.", "videoLowQuality": "Esperamos que seu vídeo tenha baixa qualidade em termos de taxa de quadros e resolução.", "videoTearing": "Esperamos que seu vídeo seja pixelizado ou contenha artefatos visuais."}, "copyAndShare": "Copiar e compartilhar o link da reunião", "dashboard": "<PERSON><PERSON>", "daysAgo": "{{daysCount}} dias atrás", "dialing": "Discagem", "dialInMeeting": "Ligue para a reunião", "dialInPin": "Ligue para a reunião e digite o código PIN:", "doNotShow": "Não mostrar esta tela novamente", "enterMeetingIdOrLink": "Insira o ID ou link da reunião", "errorDialOut": "Não foi possível discar", "errorDialOutDisconnected": "<PERSON>ão foi possível discar. ", "errorDialOutFailed": "<PERSON>ão foi possível discar. ", "errorDialOutStatus": "Erro ao obter o status de discagem", "errorMissingEmail": "Por favor insira seu e-mail para participar da reunião", "errorMissingName": "Por favor, digite seu nome para participar da reunião", "errorNameLength": "Por favor insira pelo menos 3 letras em seu nome", "errorStatusCode": "Erro ao discar, código de status: {{status}}", "errorValidation": "Falha na validação do número", "features": "Características", "guestNotAllowedMsg": "Convidados não têm permissão para participar desta reunião", "initiated": "Chamada iniciada", "invalidEmail": "E-mail inválido", "iWantToDialIn": "Eu quero discar", "joinAMeeting": "Participe de uma reunião", "joinAudioByPhone": "Junte-se com áudio do telefone", "joinMeeting": "Participar da reunião", "joinMeetingGuest": "Participar da reunião como convidado", "joinWithoutAudio": "Participe sem áudio", "keyboardShortcuts": "Habilitar atalhos de teclado", "linkCopied": "Link copiado para a área de transferência", "logout": "<PERSON><PERSON>", "lookGood": "Parece que seu microfone está funcionando corretamente", "maximumAllowedParticipantsErr": "O máximo permitido de participantes foi atingido nestas reuniões. ", "meetingReminder": "A reunião começará {{time}}. ", "multipleConferenceInitiation": "Iniciação de múltiplas conferências", "oops": "Ops!", "oppsMaximumAllowedParticipantsErr": "Ops! ", "or": "ou", "parallelMeetingsLicencesErr": "Não foi possível iniciar a reunião. ", "peopleInTheCall": "Pessoas na chamada", "pleaseEnterEmail": "Por favor insira o e-mail", "pleaseEnterFullName": "Por favor insira o nome completo", "premeeting": "Pré-reunião", "profile": "Perfil", "readyToJoin": "Pronto para aderir?", "recentMeetings": "Reuniões recentes", "screenSharingError": "Erro de compartilhamento de tela:", "showScreen": "Ativar tela pré-reunião", "signinsignup": "Entrar / Inscrever-se", "startWithPhone": "Comece com o áudio do telefone", "subScriptionInactiveErr": "Sua assinatura está inativa. ", "systemUpgradedInformation": "Atualizamos nosso sistema para a versão 2.0. ", "userNotAllowedToJoin": "Usuário não tem permissão para ingressar", "videoOnlyError": "Erro de vídeo:", "videoTrackError": "Não foi possível criar a faixa de vídeo.", "viewAllNumbers": "ver todos os números", "waitForModeratorMsg": "Aguarde enquanto o moderador entra na chamada.", "waitForModeratorMsgDynamic": "Por favor, espere um pouco {{Moderator}} junta-se à chamada.", "youAreNotAllowed": "Você não tem permissão"}, "presenceStatus": {"busy": "Ocupado", "calling": "Chamand<PERSON>...", "connected": "Conectado", "connecting": "Conectando...", "connecting2": "Conectando*...", "disconnected": "Desconectado", "expired": "<PERSON><PERSON><PERSON>", "ignored": "<PERSON><PERSON><PERSON>", "initializingCall": "In<PERSON><PERSON><PERSON><PERSON>...", "invalidToken": "To<PERSON> in<PERSON>lid<PERSON>", "invited": "Convidado", "rejected": "<PERSON><PERSON><PERSON><PERSON>", "ringing": "Toque...", "signInAsHost": "Faça login como anfitrião"}, "profile": {"avatar": "avatar", "setDisplayNameLabel": "Seu nome de exibição", "setEmailInput": "Digite o e-mail", "setEmailLabel": "Seu e-mail", "title": "Perfil"}, "raisedHand": "Gostaria de falar", "recording": {"authDropboxText": "Carregar para o Dropbox", "availableS3Space": "Espaço utilizado: {{s3_used_space}} de {{s3_free_space}}", "availableSpace": "Espaço disponível: {{spaceLeft}} MB (aproximadamente {{duration}} minutos de gravação)", "beta": "BETA", "busy": "Estamos trabalhando para liberar recursos de gravação. ", "busyTitle": "Todos os gravadores estão ocupados no momento", "copyLink": "Copiar link", "error": "A gravação falhou. ", "errorFetchingLink": "Erro ao buscar link de gravação.", "expandedOff": "A gravação parou", "expandedOn": "A reunião está sendo gravada.", "expandedPending": "A gravação está sendo iniciada...", "failedToStart": "A gravação não pôde ser iniciada", "fileSharingdescription": "Compartilhe a gravação com os participantes da reunião", "limitNotificationDescriptionNative": "Devido à alta demanda sua gravação será limitada a {{limit}} min.  <3>{{app}}</3>.", "limitNotificationDescriptionWeb": "Devido à alta demanda sua gravação será limitada a {{limit}} min.  <a href={{url}} rel='noopener noreferrer' target='_blank'>{{app}}</a>.", "linkGenerated": "Geramos um link para sua gravação.", "live": "AO VIVO", "loggedIn": "<PERSON><PERSON> como {{userName}}", "off": "Gravação interrompida", "offBy": "{{name}} parou a gravação", "on": "Gravação iniciada", "onBy": "{{name}} começou a gravação", "pending": "Preparando para gravar a reunião...", "rec": "REC", "recLive": "AO VIVO + GRAVAÇÃO", "serviceDescription": "Sua gravação será salva pelo serviço de gravação", "serviceDescriptionCloud": "Gravação em nuvem", "serviceName": "Serviço de gravação", "signIn": "Entrar", "signOut": "sair", "unavailable": "Ops!  {{serviceName}} está indisponível no momento. ", "unavailableTitle": "Gravação indisponível", "uploadToCloud": "Carregar para a nuvem"}, "sectionList": {"pullToRefresh": "Puxe para atualizar"}, "security": {"about": "Você pode adicionar um $t(lockRoomPassword) para sua reunião.  $t(lockRoomPassword) antes de serem autorizados a participar da reunião.", "aboutReadOnly": "Os participantes moderadores podem adicionar um $t(lockRoomPassword) para a reunião.  $t(lockRoomPassword) antes de serem autorizados a participar da reunião.", "insecureRoomNameWarning": "O nome do quarto não é seguro. ", "securityOptions": "Opções de segurança"}, "settings": {"calendar": {"about": "O {{appName}} a integração do calendário é usada para acessar com segurança o seu calendário para que ele possa ler os próximos eventos.", "disconnect": "Desconectar", "microsoftSignIn": "Faça login com a Microsoft", "signedIn": "Atualmente acessando eventos da agenda para {{email}}. ", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "desktopShareFramerate": "Taxa de quadros de compartilhamento de área de trabalho", "desktopShareHighFpsWarning": "Uma taxa de quadros mais alta para compartilhamento de área de trabalho pode afetar sua largura de banda. ", "desktopShareWarning": "Você precisa reiniciar o compartilhamento de tela para que as novas configurações tenham efeito.", "devices": "Dispositivos", "followMe": "Todo mundo me segue", "framesPerSecond": "quadros por segundo", "incomingMessage": "Mensagem recebida", "language": "Linguagem", "languageSettings": "Configurações de idioma", "loggedIn": "<PERSON><PERSON> como {{name}}", "microphones": "Microfones", "moderator": "Moderador", "more": "<PERSON><PERSON>", "name": "Nome", "noDevice": "<PERSON><PERSON><PERSON>", "noLanguagesAvailable": "Nenhum idioma disponível", "participantJoined": "Participante ingressou", "participantLeft": "Participante à esquerda", "playSounds": "Reproduzir som ativado", "sameAsSystem": "O mesmo que sistema ({{label}})", "selectAudioOutput": "<PERSON><PERSON><PERSON>o", "selectCamera": "Câmera", "selectLanguage": "Selecione o idioma", "selectMic": "Microfone", "sounds": "Sons", "speakers": "Alto-falantes", "startAudioMuted": "Todo mundo começa mudo", "startVideoMuted": "Todo mundo começa escondido", "talkWhileMuted": "Fale enquanto estiver mudo", "title": "Configurações"}, "settingsView": {"advanced": "Avançado", "alertCancel": "<PERSON><PERSON><PERSON>", "alertOk": "OK", "alertTitle": "Aviso", "alertURLText": "O URL do servidor inserido é inválido", "buildInfoSection": "Informações de construção", "conferenceSection": "Conferência", "disableCallIntegration": "Desativar integração de chamada nativa", "disableCrashReporting": "Desativar relatório de falhas", "disableCrashReportingWarning": "Tem certeza de que deseja desativar o relatório de falhas? ", "disableP2P": "Desative o modo ponto a ponto", "displayName": "Nome de exibição", "email": "E-mail", "header": "Configurações", "profileSection": "Perfil", "serverURL": "URL do servidor", "showAdvanced": "Mostrar configurações avançadas", "startWithAudioMuted": "Comece com o áudio silenciado", "startWithVideoMuted": "Comece com o vídeo silenciado", "version": "Vers<PERSON>"}, "share": {"dialInfoText": "\n\n=====\n\n\n\n{{defaultDialInNumber}}Clique neste link para ver os números de telefone de discagem para esta reunião\n{{dialInfoPageUrl}}", "mainText": "Clique no link a seguir para participar da reunião:\n{{roomUrl}}"}, "speaker": "<PERSON><PERSON><PERSON><PERSON>", "speakerStats": {"hours": "{{count}}h", "minutes": "{{count}}eu", "name": "Nome", "seconds": "{{count}}é", "speakerStats": "Estatísticas do palestrante", "speakerTime": "Hora do palestrante"}, "startupoverlay": {"genericTitle": "A reunião precisa usar seu microfone e câmera.", "policyText": " ", "title": "{{app}} precisa usar seu microfone e câmera."}, "suspendedoverlay": {"rejoinKeyTitle": "Junte-se novamente", "text": "Pressione o <i>Junte-se novamente</i> botão para reconectar.", "title": "Sua videochamada foi interrompida porque este computador entrou em suspensão."}, "toolbar": {"accessibilityLabel": {"audioOnly": "Alternar apenas á<PERSON>o", "audioRoute": "Gerenciar dispositivo de som", "boo": "Vaia", "callQuality": "Gerenciar qualidade de vídeo", "carmode": "<PERSON><PERSON>", "cc": "<PERSON>ern<PERSON> legendas", "chat": "Abri<PERSON>/<PERSON><PERSON>r bate-papo", "clap": "<PERSON><PERSON><PERSON><PERSON>", "collapse": "Colapso", "document": "Alternar documento compartilhado", "donationCLP": "Configurações de conexão C&P", "donationLink": "Configurações da Caixa Doadora", "download": "Baixe nossos aplicativos", "embedMeeting": "Incorporar reunião", "expand": "Expandir", "feedback": "Deixar feedback", "fullScreen": "Alternar tela cheia", "genericIFrame": "Alternar aplicativo compartilhado", "giphy": "Alternar menu GIPHY", "grantModerator": "Moderador de concessão", "hangup": "<PERSON><PERSON> <PERSON>", "help": "<PERSON><PERSON><PERSON>", "invite": "<PERSON><PERSON> pessoas", "kick": "Expulsar participante", "laugh": "Rir", "leaveConference": "<PERSON><PERSON> <PERSON>", "like": "Afirmativo", "lobbyButton": "Ativar/desativar modo lobby", "localRecording": "Alternar controles de gravação local", "lockRoom": "Alternar senha da reunião", "moreActions": "<PERSON><PERSON>", "moreActionsMenu": "<PERSON>u mais açõ<PERSON>", "moreOptions": "Mostrar mais opç<PERSON>", "mute": "Ativar/desativar som", "muteEveryone": "<PERSON>len<PERSON><PERSON> todos", "muteEveryoneElse": "Silenciar todos os outros", "muteEveryoneElsesVideo": "Desative a câmera de todos os outros", "muteEveryonesVideo": "Desative a câmera de todos", "participants": "Participantes", "party": "Popper de festa", "pip": "Alternar modo Picture-in-Picture", "privateMessage": "Enviar mensagem privada", "profile": "Edite seu perfil", "raiseHand": "Levante/Abaixe sua mão", "reactionsMenu": "Abrir/Fechar menu de reações", "recording": "Alternar gravação", "remoteMute": "Silenciar participante", "remoteVideoMute": "Desativar câmera do participante", "removeDonation": "Remover DonorBox", "rmoveCDonation": "Remover C&P", "security": "Opções de segurança", "selectBackground": "Selecione o plano de fundo", "Settings": "Alternar configurações", "shareaudio": "<PERSON>mp<PERSON><PERSON><PERSON>", "sharedvideo": "Alternar compartilhamento de vídeo do YouTube", "shareRoom": "<PERSON><PERSON>", "shareYourScreen": "Iniciar/parar de compartilhar sua tela", "shortcuts": "Alternar atalhos", "show": "Mostrar no palco", "speakerStats": "Alternar estatísticas do palestrante", "surprised": "Surpreso", "tileView": "Alternar visualização de blocos", "toggleCamera": "<PERSON><PERSON><PERSON> c<PERSON>", "toggleFilmstrip": "Alternar tira de filme", "toggleReactions": "Alternar reações", "videoblur": "Alternar desfoque de vídeo", "videomute": "Iniciar/<PERSON>r c<PERSON>a"}, "addPeople": "Adicione pessoas à sua chamada", "audioOnlyOff": "Desative o modo de largura de banda baixa", "audioOnlyOn": "Ativar modo de largura de banda baixa", "audioRoute": "Gerenciar dispositivo de som", "audioSettings": "Configurações de áudio", "authenticate": "Autenticar", "boo": "Vaia", "callQuality": "Gerenciar qualidade de vídeo", "chat": "Abri<PERSON>/<PERSON><PERSON>r bate-papo", "clap": "<PERSON><PERSON><PERSON><PERSON>", "closeChat": "<PERSON><PERSON><PERSON> bate-papo", "closeParticipantsPane": "<PERSON><PERSON><PERSON>", "closeReactionsMenu": "Fechar menu de reações", "disableNoiseSuppression": "Desativar supressão de ruído", "disableReactionSounds": "Você pode desativar sons de reação para esta reunião", "documentClose": "<PERSON><PERSON><PERSON> o LivePad", "documentOpen": "Compartilhar LivePad", "donationCLP": "Configurações de conexão C&P", "donationLink": "Configurações da Caixa Doadora", "download": "Baixe nossos aplicativos", "e2ee": "Criptografia ponta a ponta", "embedMeeting": "Incorporar reunião", "enterFullScreen": "Ver tela inteira", "enterTileView": "Entrar na visualização lado a lado", "exitFullScreen": "<PERSON><PERSON> da tela inteira", "exitTileView": "Sair da visualização lado a lado", "feedback": "Deixar feedback", "genericIFrameClose": "Parar quadro branco", "genericIFrameOpen": "Compartilhar quadro branco", "genericIFrameWeb": "Quadro Branco", "hangup": "<PERSON><PERSON>ar", "hangUpforEveryOne": "<PERSON><PERSON><PERSON> para todos", "hangUpforMe": "Desligar só para mim", "hangUpText": "Tem certeza de que deseja desligar?", "help": "<PERSON><PERSON><PERSON>", "hideReactions": "Ocultar re<PERSON>ção", "invite": "<PERSON><PERSON> pessoas", "inviteViaCalendar": "Convidar via calendário", "iOSStopScreenShareAlertMessage": "Interrompa o compartilhamento de tela antes de desligar.", "iOSStopScreenShareAlertTitle": "Parar o compartilhamento de tela", "laugh": "Rir", "leaveConference": "<PERSON><PERSON> <PERSON>", "like": "Afirmativo", "lobbyButtonDisable": "Desativar modo lobby", "lobbyButtonEnable": "Ativar modo lobby", "login": "Conecte-se", "logout": "<PERSON><PERSON>", "lowerYourHand": "Abaixe sua mão", "moreActions": "<PERSON><PERSON>", "moreOptions": "<PERSON><PERSON>", "mute": "Ativar/desativar som", "muteEveryone": "<PERSON>len<PERSON><PERSON> todos", "muteEveryonesVideo": "Desative a câmera de todos", "noAudioSignalDesc": "Se você não o desativou propositalmente nas configurações do sistema ou no hardware, considere trocar o dispositivo.", "noAudioSignalDescSuggestion": "Se você não o desativou propositalmente nas configurações do sistema ou no hardware, considere mudar para o dispositivo sugerido.", "noAudioSignalDialInDesc": "Você também pode discar usando:", "noAudioSignalDialInLinkDesc": "Números de discagem", "noAudioSignalTitle": "Não há entrada vinda do seu microfone!", "noisyAudioInputDesc": "Parece que seu microfone está fazendo barulho. Considere silenciar ou alterar o dispositivo.", "noisyAudioInputTitle": "Seu microfone parece estar barulhento!", "openChat": "<PERSON><PERSON><PERSON> bate-papo", "openReactionsMenu": "Abrir menu de reações", "participants": "Participantes", "party": "Celebração", "pip": "Modo imagem em imagem", "privateMessage": "Enviar mensagem privada", "profile": "Edite seu perfil", "raiseHand": "Levante/Abaixe sua mão", "raiseYourHand": "Levante sua mão", "reactionBoo": "Enviar reação boo", "reactionClap": "Enviar reação de palmas", "reactionLaugh": "Enviar reação de risada", "reactionLike": "Enviar reação positiva", "reactionParty": "Envie a reação do popper da festa", "reactionSurprised": "Envie reação de surpresa", "removeDonation": "Remover DonorBox", "rmoveCDonation": "Remover C&P", "security": "Opções de segurança", "selectBackground": "Selecione o plano de fundo", "Settings": "Configurações", "Share": "Compartilhar", "shareaudio": "<PERSON>mp<PERSON><PERSON><PERSON>", "sharedvideo": "Compartilhe Youtube", "shareRoom": "<PERSON><PERSON>", "shortcuts": "Ver atal<PERSON>", "showReactions": "Mostrar reação", "speakerStats": "Estatísticas do palestrante", "startScreenSharing": "Iniciar compartilhamento de tela", "startSubtitles": "Iniciar legendas", "stopAudioSharing": "Interromper o compartilhamento de áudio", "stopScreenSharing": "Interromper o compartilhamento de tela", "stopSharedVideo": "Pare o vídeo do YouTube", "stopSubtitles": "<PERSON><PERSON> legendas", "surprised": "Surpreso", "talkWhileMutedPopup": "<PERSON><PERSON><PERSON> falar? ", "tileViewToggle": "Alternar visualização de blocos", "toggleCamera": "<PERSON><PERSON><PERSON> c<PERSON>", "videomute": "Iniciar/<PERSON>r c<PERSON>a", "videoSettings": "Configurações de vídeo", "voiceCommand": "Abrir comando de voz", "whiteBoardOpen": "Compartilhar quadro branco", "zoomin": "Ampliar", "zoomout": "Diminuir zoom"}, "transcribing": {"ccButtonTooltip": "Iniciar/<PERSON>r legendas", "error": "A transcrição falhou. ", "expandedLabel": "A transcrição está ativada no momento", "failedToStart": "A transcrição não pôde ser iniciada", "labelToolTip": "A reunião está sendo transcrita", "off": "A transcrição foi interrompida", "pending": "Preparando para transcrever a reunião...", "start": "Comece a mostrar legendas", "stop": "<PERSON><PERSON> de <PERSON> legendas", "tr": "TR", "sourceLanguageDesc": "Atualmente, o idioma da reunião está definido como {{sourceLanguage}}", "subtitlesTitle": "<PERSON><PERSON>", "sourceLanguageHere": "Você pode alterá-lo aqui", "subtitlesOff": "Desativado", "NoCaptionsAvailable": "Nenhuma legenda disponível", "Translate": "Traduzir", "LiveCaptions": "Legendas ao vivo", "Transcriptions": "Transcrições", "TranslateTo": "Traduzir para", "transcriptionQuotaExceeded": "Cota de transcrição excedida para este mês", "transcriptionQuotaExceededTitle": "Cota de transcrição excedida"}, "userMedia": {"androidGrantPermissions": "<PERSON><PERSON><PERSON><PERSON> <b><i><PERSON><PERSON><PERSON></i></b> quando seu navegador pede permissões.", "chromeGrantPermissions": "<PERSON><PERSON><PERSON><PERSON> <b><i><PERSON><PERSON><PERSON></i></b> quando seu navegador pede permissões.", "edgeGrantPermissions": "<PERSON><PERSON><PERSON><PERSON> <b><i>Sim</i></b> quando seu navegador pede permissões.", "electronGrantPermissions": "Conceda permissões para usar sua câmera e microfone", "firefoxGrantPermissions": "Selecione <b><i>Compartilhar dispositivo selecionado</i></b> quando seu navegador pede permissões.", "iexplorerGrantPermissions": "<PERSON><PERSON><PERSON><PERSON> <b><i>OK</i></b> quando seu navegador pede permissões.", "nwjsGrantPermissions": "Conceda permissões para usar sua câmera e microfone", "operaGrantPermissions": "<PERSON><PERSON><PERSON><PERSON> <b><i><PERSON><PERSON><PERSON></i></b> quando seu navegador pede permissões.", "react-nativeGrantPermissions": "<PERSON><PERSON><PERSON><PERSON> <b><i><PERSON><PERSON><PERSON></i></b> quando seu navegador pede permissões.", "safariGrantPermissions": "<PERSON><PERSON><PERSON><PERSON> <b><i>OK</i></b> quando seu navegador pede permissões."}, "videoSIPGW": {"busy": "Estamos trabalhando para liberar recursos. ", "busyTitle": "O serviço de quarto está ocupado no momento", "errorAlreadyInvited": "{{displayName}} j<PERSON> convidado", "errorInvite": "Conferência ainda não estabelecida. ", "errorInviteFailed": "Estamos trabalhando para resolver o problema. ", "errorInviteFailedTitle": "Convidativo {{displayName}} fracassado", "errorInviteTitle": "Erro ao convidar a sala", "pending": "{{displayName}} foi convidado"}, "videoStatus": {"audioOnly": "AUD", "audioOnlyExpanded": "Você está no modo de largura de banda baixa. ", "callQuality": "Qualidade de vídeo", "hd": "alta definição", "hdTooltip": "Visualizando vídeo em alta definição", "highDefinition": "Alta definição", "labelTooiltipNoVideo": "Nenhum vídeo", "labelTooltipAudioOnly": "Modo de baixa largura de banda ativado", "ld": "LD", "ldTooltip": "Visualizando vídeo em baixa definição", "lowDefinition": "Baixa definição", "onlyAudioAvailable": "Somente áudio está disponível", "onlyAudioSupported": "Oferecemos suporte apenas para áudio neste navegador.", "sd": "SD", "sdTooltip": "Visualizando vídeo em definição padrão", "standardDefinition": "Definição padrão", "uhd": "Ultra HD", "uhdTooltip": "Visualizando vídeo em ultra alta definição", "uhighDefinition": "Ultra alta definição"}, "videothumbnail": {"connectionInfo": "Informações de conexão", "domute": "<PERSON><PERSON>", "domuteOthers": "Silenciar todos os outros", "domuteVideo": "Desativar câmer<PERSON>", "domuteVideoOfOthers": "Desative a câmera de todos os outros", "flip": "<PERSON><PERSON><PERSON>", "grantModerator": "Moderador de concessão", "kick": "Remover usuário", "moderator": "Moderador", "mute": "O participante está silenciado", "muted": "<PERSON><PERSON><PERSON><PERSON>", "remoteControl": "Iniciar/parar controle remoto", "show": "Mostrar no palco", "videomute": "O participante parou a câmera", "videoMuted": "Câmera desativada"}, "virtualBackground": {"addBackground": "Adicionar plano de fundo", "appliedCustomImageTitle": "Imagem personalizada carregada", "apply": "Aplicar", "blur": "<PERSON><PERSON><PERSON>", "customImg": "Imagem personalizada", "deleteImage": "Excluir imagem", "desktopShare": "Compartilhamento de área de trabalho", "desktopShareError": "Não foi possível criar o compartilhamento da área de trabalho", "enableBlur": "Ativar des<PERSON>", "image1": "Praia", "image2": "<PERSON><PERSON><PERSON> neutra branca", "image3": "Quarto vazio branco", "image4": "Candeeiro de pé preto", "image5": "<PERSON><PERSON><PERSON>", "image6": "Flores<PERSON> ", "image7": "Nascer do sol", "none": "<PERSON><PERSON><PERSON>", "pleaseWait": "Por favor, aguarde...", "removeBackground": "Remover fundo", "slightBlur": "Ligeiro desfoque", "switchBackgroundTitle": "Mudar plano de fundo", "title": "Fundos Virtuais", "uploadedImage": "Imagem enviada {{index}}", "virtualImagesTitle": "Imagens virtuais integradas", "webAssemblyWarning": "WebAssembly não suportado"}, "voicecommand": {"activePIPLabel": "PIP ativo", "clickOnMic": "Clique no comando Mic e Voice a", "hints": {"closeLivePad": "<PERSON><PERSON><PERSON> o LivePad", "closeWhiteboard": "<PERSON><PERSON><PERSON> quadro branco", "closeYoutube": "<PERSON><PERSON><PERSON>", "hints": "Dicas", "invitePeople": "<PERSON><PERSON> pessoas", "lowerHand": "Mão Inferior", "openChatBox": "<PERSON><PERSON><PERSON> caixa de bate-papo", "openClickAndPledge": "Abra clique e prometa", "openDonorbox": "Abrir caixa de <PERSON>", "openFullScreen": "<PERSON><PERSON>r tela inteira", "openLivePad": "<PERSON>bra o LivePad", "openLivestream": "Abrir transmissão ao vivo", "openParticipantPane": "Abrir painel Participante", "openRecording": "Gravação aberta", "openSettings": "<PERSON><PERSON>r configuraç<PERSON><PERSON>", "openSpeakerStats": "Abrir estatísticas do palestrante", "openVideoQualityDialog": "Abrir caixa de diálogo de qualidade de vídeo", "openVirtualBackground": "Abrir fundo virtual", "openWhiteboard": "<PERSON><PERSON>r quadro branco", "openYoutube": "<PERSON><PERSON> o <PERSON>", "raiseHand": "Levante a mão", "removeClickAndPledge": "Remover clique e promessa", "removeDonorbox": "Remover caixa de doadores", "startScreenSharing": "Iniciar compartilhamento de tela", "StopScreenSharing": "Parar o compartilhamento de tela"}, "inActivePIPLabel": "Em PIP ativo", "pleaseWaitWeAreRecording": "A<PERSON>e, estamos gravando", "vcLabel": "Comand<PERSON> de voz", "voiceCommandForMeethour": "Comando de voz para horário de encontro"}, "volumeSlider": "Controle deslizante de volume", "welcomepage": {"accessibilityLabel": {"join": "Toque para participar", "roomname": "Insira o ID da reunião"}, "addMeetingName": "Adicionar nome da reunião", "appDescription": "Vá em frente, converse por vídeo com toda a equipe.  {{app}} é uma solução de videoconferência totalmente criptografada e 100% de código aberto que você pode usar o dia todo, todos os dias, gratuitamente - sem necessidade de conta.", "audioVideoSwitch": {"audio": "Voz", "video": "Vídeo"}, "calendar": "<PERSON><PERSON><PERSON><PERSON>", "connectCalendarButton": "Conecte seu calendário", "connectCalendarText": "Conecte seu calendário para ver todas as suas reuniões em {{app}}.  {{provider}} reuniões para o seu calendário e iniciá-las com um clique.", "developerPlan": "Plano de desenvolvedor", "enterprisePlan": "Plano Empresarial", "enterpriseSelfHostPlan": "Plano de auto-hospedagem empresarial", "enterRoomTitle": "Inicie uma nova reunião ou insira o nome da sala existente", "features": "Características", "footer": {"allRightsReserved": "Todos os direitos reservados", "androidAppDownload": "Baixar aplicativo Android", "apiDocumentation": "Documentação da API", "app": "Aplicativo", "blog": "Blogue", "company": "Empresa", "contact": "Contato", "copyright": "Direitos autorais", "copyrightText": "Direitos autorais 2020 - 2024 Meet Hour LLC. ", "developers": "<PERSON><PERSON><PERSON><PERSON>", "disclaimer": "Isenção de responsabilidade", "download": "Download", "email": "E-mail", "faqs": "<PERSON><PERSON><PERSON> frequentes", "followUs": "Siga-nos", "helpDesk": "Suporte Técnico", "home": "<PERSON>r", "integrations": "Integrações", "inTheNews": "<PERSON><PERSON> <PERSON><PERSON>", "iOSAppDownload": "Baixar aplicativo iOS", "knowledgeBase": "Base de conhecimento", "meethour": "Hora do encontro", "meethourLLC": "Conheça a Hora LLC", "officeAddress": "8825 Stanford, <PERSON><PERSON><PERSON> 205 Columbia, MD 21045", "phone": "Telefone", "privacyPolicy": "política de Privacidade", "productPresentation": "Apresentação do Produto", "refundCancellationPolicy": "Política de reembolso e cancelamento", "termsConditions": "Termos e Condições", "testimonials": "<PERSON><PERSON><PERSON><PERSON>", "webMobileSDK": "SDK para Web e dispositivos móveis", "whoAreYou": "Quem é você"}, "forHospitals": "Para hospitais", "freeBannerDescription": "Videoconferência gratuita e ilimitada com qualidade HD como nunca antes. ", "freePlan": "Plano Gratuito", "getHelp": "<PERSON><PERSON><PERSON> frequentes", "go": "C<PERSON>r ou participar de uma reunião", "goSmall": "C<PERSON>r ou participar de uma reunião", "header": {"accelerateDevelopmentWithPrebuiltSDKs": "Acelere o desenvolvimento com SDKs pré-construídos.", "apiStatus": "Status da API", "appointmentSchedulingVideoConference": "Agendamento de consultas e videoconferência.", "blog": "Blogue", "customIntegrationDedicatedSupport": "Integração personalizada e suporte dedicado", "customTailoredVideoMeetings": "Videoconferências personalizadas.", "developer": "<PERSON><PERSON><PERSON><PERSON>", "developers": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "Documentação", "edTech": "Tecnologia Educacional", "eMail": "E-mail", "engagingOnlineLearningForEducators": "Envolvendo a aprendizagem on-line para educadores.", "engagingVirtualEventExperiences": "Experiências envolventes de eventos virtuais.", "enterprise": "Empresa", "enterpriseSelfHost": "Auto-hospedeiro corporativo", "features": "Características", "fitness": "Fitness", "free": "Livre", "fundraiseEffortlesslyWithinVideoConferences": "Arrecade fundos sem esforço em videoconferências.", "fundraisingDonate": "Arrecadação de fundos / Doação", "fundraisingDonateOnline": "Arrecadação de fundos / Doação on-line", "getStarted": "Comece", "hdQualityVideoConferenceApp": "Aplicativo de videoconferência com qualidade HD", "help": "<PERSON><PERSON><PERSON>", "helpDesk": "Suporte Técnico", "highQualityLiveEventStreaming": "Transmissão de eventos ao vivo de alta qualidade.", "hostVideoConferenceOnYourServers": "Hospede videoconferências em seus servidores.", "industries": "Indústrias", "integrateVideoCallWithinYourWebsiteApp": "Integre a videochamada em seu site/aplicativo.", "interactiveVirtualLearningSolutions": "Soluções interativas de aprendizagem virtual.", "joinAMeeting": "Participe de uma reunião", "knowledgeBase": "Base de conhecimento", "liveStreaming": "Transmissão ao vivo", "meethour": "Hora do encontro", "mycaly": "MyCaly", "myCaly": "<PERSON><PERSON>", "myCalyPricing": "Preços MyCaly.", "noAdsRecordingLiveStreaming": "Sem anúncios + gravação + transmissão ao vivo.", "noTimeLimitGroupCalls": "Sem limite de tempo para chamadas individuais e em grupo.", "preBuiltSDKs": "SDKs pré-construídos", "pricing": "Preços", "pro": "<PERSON><PERSON><PERSON>", "products": "<PERSON><PERSON><PERSON>", "resources": "Recursos", "scheduleADemo": "Agende uma demonstração", "simplifiedAPIReferences": "Referências de API simplificadas", "smoothVideoOnboardingExperience": "Experiência suave de integração de vídeo.", "solutions": "Soluções", "stayuptodateWithOurBlog": "Mantenha-se atualizado com nosso blog", "systemHealthStatusandUpdates": "Status e atualizações de integridade do sistema", "tailoredSolutionsForYourHealthcareNeeds": "Soluções sob medida para suas necessidades de saúde.", "telehealth": "Telessaúde", "useCases": "Casos de uso", "videoConference": "Videoconferência", "videoConferencePlans": "Planos de videoconferência", "videoConferencePricing": "Preços de videoconferência.", "videoConferencing": "Videoconferência", "videoKYC": "Vídeo KYC", "virtualClassrooms": "Salas de aula virtuais", "virtualEvents": "Eventos Virtuais", "virtualSolutionForHomeFitness": "Solução Virtual para Home Fitness.", "webinars": "Seminários on-line", "webinarSessionsWithIndustryLeaders": " Sessões de webinar com líderes do setor."}, "headerSubtitle": "Reuniões seguras e com qualidade HD", "headerTitle": "Hora do encontro", "info": "Informações de discagem", "invalidMeetingID": "ID de reunião inválido", "jitsiOnMobile": "Meet Hour no celular – baixe nossos aplicativos e inicie uma reunião de qualquer lugar", "join": "CRIAR / PARTICIPAR", "joinAMeeting": "Participe de uma reunião", "logo": {"calendar": "Logotipo do calendário", "desktopPreviewThumbnail": "Miniatura de visualização na área de trabalho", "googleLogo": "Logotipo do Google", "logoDeepLinking": "Logotipo do Jitsi Meet", "microsoftLogo": "Logot<PERSON><PERSON> da Microsoft", "policyLogo": "Logotipo da política"}, "meetingDate": "Data da Reunião", "meetingDetails": "Detalhes da reunião", "meetingIsReady": "A reunião está pronta", "mobileDownLoadLinkAndroid": "Baixe o aplicativo móvel para Android", "mobileDownLoadLinkFDroid": "Baixe o aplicativo móvel para F-Droid", "mobileDownLoadLinkIos": "Baixe o aplicativo móvel para iOS", "moderatedMessage": "Ou <a href=\"{{url}}\" rel=\"noopener noreferrer\" target=\"_blank\">reservar um URL de reunião</a> com antecedência onde você é o único moderador.", "oopsDeviceClockorTimezoneErr": "Ops! ", "oopsThereSeemsToBeProblem": "Ops, parece haver um problema", "pleaseWaitForTheStartMeeting": "Por favor, aguarde o {{moderator}} para iniciar esta reunião.", "preRegistrationMsg": "Este encontro requer pré-inscrição. ", "pricing": "Preços", "privacy": "Privacidade", "privateMeetingErr": "Você parece estar participando de uma reunião privada. ", "proPlan": "Plano Profissional", "recentList": "<PERSON><PERSON>", "recentListDelete": "Excluir entrada", "recentListEmpty": "Sua lista recente está vazia no momento. ", "reducedUIText": "Bem-vindo ao {{app}}!", "registerNow": "Cadastre-se agora", "roomname": "Insira o ID da reunião", "roomNameAllowedChars": "O nome da reunião não deve conter nenhum destes caracteres: ?, &, :, ', \", %, #.", "roomnameHint": "Digite o nome ou URL da sala na qual deseja ingressar. ", "scheduleAMeeting": "Agende uma reunião", "sendFeedback": "<PERSON><PERSON>r coment<PERSON>", "shiftingVirtualMeetToReality": "Mudando o encontro virtual para a realidade", "solutions": "Soluções", "startMeeting": "<PERSON><PERSON><PERSON> re<PERSON>", "terms": "Termos", "timezone": "<PERSON><PERSON>", "title": "Solução de videoconferência 100% gratuita, ilimitada, criptografada de ponta a ponta e com qualidade HD", "tryNowItsFree": "Experimente agora, <PERSON> gr<PERSON>", "waitingInLobby": "E<PERSON>ando no lobby. {{moderator}} vou deixar você entrar em breve.", "youtubeHelpTutorial": "Tutoriais de ajuda do YouTube"}}