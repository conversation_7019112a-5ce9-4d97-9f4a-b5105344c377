import json
import os
import re
from deep_translator import GoogleTranslator # type: ignore

# Directory containing the translated JSON files
translations_dir = '.'  # Assuming the translated files are in the current directory

# The brand name to transliterate (case-insensitive)
brand_names = ["Meethour", "Meet hour"]

# Function to transliterate a string using Google Transliteration (via deep_translator)
def transliterate_string(text, target_language_code):
    try:
        # GoogleTranslator can handle transliteration for some languages
        translator = GoogleTranslator(source='en', target=target_language_code)
        # The translate method in deep_translator often performs transliteration
        # for certain languages when the target script is different.
        # However, direct transliteration is not a guaranteed feature for all language pairs.
        # We'll rely on its best effort.
        transliterated_text = translator.translate(text)
        # A simple fallback if the translation is the same as the input
        if transliterated_text.lower() == text.lower():
            # For languages where direct transliteration isn't supported by the translator,
            # we might need a more specific transliteration library or a manual mapping.
            # For this example, we'll just return the original text as a fallback
            # if the translator didn't change it significantly.
            return text
        return transliterated_text
    except Exception as e:
        print(f"Error during transliteration for '{text}' to '{target_language_code}': {e}")
        return text # Return original text on error

# Recursive function to find and replace translated brand names
def find_and_transliterate(data, lang_code):
    if isinstance(data, dict):
        for key, value in data.items():
            data[key] = find_and_transliterate(value, lang_code)
    elif isinstance(data, list):
        for i in range(len(data)):
            data[i] = find_and_transliterate(data[i], lang_code)
    elif isinstance(data, str):
        processed_string = data
        for brand_name in brand_names:
            # Use regex with ignore case to find the translated brand name
            # We need to be careful not to replace parts of other words.
            # This regex looks for the word "Meethour" or "Meet hour" surrounded by word boundaries
            # or at the beginning/end of the string.
            pattern = r'\b' + re.escape(brand_name) + r'\b'
            
            # Find all occurrences of the brand name (case-insensitive)
            found_matches = re.findall(pattern, processed_string, re.IGNORECASE)

            # If the brand name (case-insensitive) exists in the string,
            # we assume it's a potential translation and should be transliterated.
            if found_matches:
                 # Transliterate each found instance
                for match in found_matches:
                    transliterated_name = transliterate_string(match, lang_code)
                    # Replace the matched string (maintaining original case if possible, or using transliterated)
                    # This simple replace might not be perfect for maintaining original casing,
                    # but it works for the core purpose.
                    processed_string = processed_string.replace(match, transliterated_name)

        return processed_string
    return data

# Iterate through the translated language files
for filename in os.listdir(translations_dir):
    if filename.startswith('main-') and filename.endswith('.json') and filename != 'main.json':
        filepath = os.path.join(translations_dir, filename)
        lang_code = filename.split('-')[1].split('.')[0] # Extract language code from filename
        print(f"Processing {filename} for brand name transliteration...")

        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                translated_data = json.load(f)

            # Process the translated data to find and transliterate brand names
            processed_translated_data = find_and_transliterate(translated_data, lang_code)

            # Save the updated data back to the file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(processed_translated_data, f, ensure_ascii=False, indent=4)

            print(f"Finished processing {filename}")

        except Exception as e:
            print(f"Error processing {filename}: {e}")

print("Brand name transliteration completed for all relevant files.")