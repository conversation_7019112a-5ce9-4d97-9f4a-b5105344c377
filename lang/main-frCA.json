{"addPeople": {"add": "Inviter", "addContacts": "Invitez vos contacts", "contacts": "contacts", "copyInvite": "Copier l'invitation à une réunion", "copyLink": "Copier le lien de la réunion", "copyStream": "Copier le lien de diffusion en direct", "countryNotSupported": "Nous ne prenons pas encore en charge cette destination.", "countryReminder": "Vous appelez en dehors des États-Unis ? ", "defaultEmail": "Votre e-mail par défaut", "disabled": "Vous ne pouvez pas inviter des gens.", "doYouWantToRemoveThisPerson": "Voulez-vous supprimer cette personne", "failedToAdd": "Échec de l'ajout de participants", "footerText": "La numérotation externe est désactivée.", "googleCalendar": "Google Agenda", "googleEmail": "Courriel <PERSON>", "inviteMoreHeader": "Vous êtes le seul à la réunion", "inviteMoreMailSubject": "Rejoindre {{appName}} réunion", "inviteMorePrompt": "Invitez plus de personnes", "linkCopied": "Lien copié dans le presse-papier", "loading": "Recherche de personnes et de numéros de téléphone", "loadingNumber": "Validation du numéro de téléphone", "loadingPeople": "Rechercher des personnes à inviter", "loadingText": "Chargement...", "noResults": "Aucun résultat de recherche correspondant", "noValidNumbers": "Veuillez entrer un numéro de téléphone", "outlookEmail": "<PERSON><PERSON><PERSON> Outlook", "phoneNumbers": "numéros de téléphone", "searching": "Recherche...", "searchNumbers": "Ajouter des numéros de téléphone", "searchPeople": "Rechercher des personnes", "searchPeopleAndNumbers": "Rechercher des personnes ou ajouter leurs numéros de téléphone", "sendWhatsa[pp": "WhatsApp", "shareInvite": "Partager l'invitation à une réunion", "shareInviteP": "Partager une invitation à une réunion avec un mot de passe", "shareLink": "Partagez le lien de la réunion pour inviter d'autres personnes", "shareStream": "Partagez le lien de diffusion en direct", "sipAddresses": "adresses à sir<PERSON>", "telephone": "Téléphone: {{number}}", "title": "Inviter des personnes à cette réunion", "yahooEmail": "<PERSON><PERSON><PERSON>"}, "audioDevices": {"bluetooth": "Bluetooth", "headphones": "Écouteurs", "none": "Aucun périphérique audio disponible", "phone": "Téléphone", "speaker": "Conférencier"}, "audioOnly": {"audioOnly": "Faible bande passante"}, "breakoutRooms": {"actions": {"add": "Ajouter une salle de discussion (Beta)", "autoAssign": "Assigner automatiquement aux salles de discussion", "close": "<PERSON><PERSON><PERSON>", "join": "Rejoindre", "leaveBreakoutRoom": "<PERSON><PERSON><PERSON> la salle de discussion", "more": "Plus", "remove": "<PERSON><PERSON><PERSON><PERSON>", "rename": "<PERSON>mmer", "renameBreakoutRoom": "Renommer la salle de discussion", "sendToBreakoutRoom": "Envoyer un participant à :"}, "breakoutList": "Liste des salles de discussion", "buttonLabel": "Salles de discussion", "defaultName": "Salle de discussion #{{index}}", "hideParticipantList": "Masquer la liste des participants", "mainRoom": "Salle principale", "notifications": {"joined": "Rejoint la salle de discussion \"{{name}}\"", "joinedMainRoom": "Rejoint la salle principale", "joinedTitle": "Salles de discussion"}, "showParticipantList": "Afficher la liste des participants", "title": "Salles de discussion"}, "calendarSync": {"addMeetingURL": "Ajouter un lien de réunion", "confirmAddLink": "Souhaitez-vous ajouter un lien Meet Hour à cet événement ?", "error": {"appConfiguration": "L'intégration du calendrier n'est pas correctement configurée.", "generic": "Une erreur s'est produite. ", "notSignedIn": "Une erreur s'est produite lors de l'authentification pour voir les événements du calendrier. "}, "join": "Rejoindre", "joinTooltip": "Rejoignez la réunion", "nextMeeting": "prochaine réunion", "noEvents": "Aucun événement à venir n'est programmé.", "ongoingMeeting": "réunion en cours", "permissionButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> les paramètres", "permissionMessage": "L'autorisation Calendrier est requise pour voir vos réunions dans l'application.", "refresh": "Actualiser le calendrier", "today": "<PERSON><PERSON><PERSON>'hui"}, "carmode": {"actions": {"selectSoundDevice": "Sélectionnez un périphérique audio"}, "labels": {"buttonLabel": "Mode voiture", "title": "Mode voiture", "videoStopped": "Votre vidéo est arrêtée"}}, "chat": {"enter": "Entrer dans la salle de discussion", "error": "Erreur : votre message n'a pas été envoyé.  {{error}}", "fieldPlaceHolder": "Tapez votre message ici", "message": "Message", "messageAccessibleTitle": "{{user}} dit :", "messageAccessibleTitleMe": "moi dit :", "messagebox": "Tapez un message", "messageTo": "Message privé à {{recipient}}", "nickname": {"popover": "Choisissez un pseudo", "title": "Entrez un pseudo pour utiliser le chat"}, "noMessagesMessage": "Il n'y a pas encore de messages dans la réunion. ", "privateNotice": "Message privé à {{recipient}}", "smileysPanel": "<PERSON><PERSON><PERSON>", "tabs": {"chat": "Cha<PERSON>", "polls": "Sondages"}, "title": "Chat et sondages", "titleWithPolls": "Chat et sondages", "you": "toi"}, "chromeExtensionBanner": {"buttonText": "Installer l'extension Chrome", "close": "<PERSON><PERSON><PERSON>", "dontShowAgain": "Ne me montre plus ça", "installExtensionText": "Installez l'extension pour l'intégration de Google Agenda et d'Office 365"}, "clickandpledge": {"errorDesc": "Veuillez saisir un GUID Click and Pledge Connect valide. ", "errorNotification": "GUID de clic et d'engagement non valide", "title": "Paramètres de don C&P Connect", "titlenative": "Cliquez et engagez-vous"}, "connectingOverlay": {"joiningRoom": "Vous connecter à votre réunion..."}, "connection": {"ATTACHED": "Ci-joint", "AUTHENTICATING": "Authentification", "AUTHFAIL": "L'authentification a échoué", "CONNECTED": "Connecté", "CONNECTING": "De liaison", "CONNFAIL": "La connexion a échoué", "DISCONNECTED": "Déconnecté", "DISCONNECTING": "Déconnexion", "ERROR": "<PERSON><PERSON><PERSON>", "FETCH_SESSION_ID": "Obtention de l'identifiant de session...", "GET_SESSION_ID_ERROR": "Obtenez l'erreur d'ID de session : {{code}}", "GOT_SESSION_ID": "Obtention de l'identifiant de session... Terminé", "LOW_BANDWIDTH": "Vidéo pour {{displayName}} a été désactivé pour économiser la bande passante"}, "connectionindicator": {"address": "<PERSON><PERSON><PERSON>:", "audio_ssrc": "SSRC audio :", "bandwidth": "Bande passante estimée :", "bitrate": "Débit :", "bridgeCount": "Nombre de serveurs : ", "codecs": "Codecs (A/V) : ", "connectedTo": "Connecté à :", "e2e_rtt": "RTT E2E :", "framerate": "Fréquence d'images :", "less": "Affiche<PERSON> moins", "localaddress": "Adresse locale :", "localaddress_plural": "Adresses locales :", "localport": "Port local :", "localport_plural": "Ports locaux :", "maxEnabledResolution": "envoyer un maximum", "more": "Afficher plus", "packetloss": "<PERSON><PERSON> :", "participant_id": "Identifiant du participant :", "quality": {"good": "Bien", "inactive": "Inactif", "lost": "Perdu", "nonoptimal": "Non optimal", "poor": "<PERSON><PERSON><PERSON>"}, "remoteaddress": "<PERSON><PERSON><PERSON> distante :", "remoteaddress_plural": "Adresses distantes :", "remoteport": "Port distant :", "remoteport_plural": "Ports distants :", "resolution": "Résolution:", "savelogs": "Enregistrer les journaux", "status": "Connexion:", "transport": "Transport:", "transport_plural": "Transports :", "video_ssrc": "Vidéo SSRC :"}, "dateUtils": {"earlier": "Plus tôt", "today": "<PERSON><PERSON><PERSON>'hui", "yesterday": "<PERSON>er"}, "deepLinking": {"appNotInstalled": "Utilisez notre {{app}} application mobile pour rejoindre cette réunion sur votre téléphone.", "continueWithBrowser": "Continuer avec le navigateur", "description": "Il ne s'est rien passé ?  {{app}} application de bureau.  {{app}} application Web.", "descriptionWithoutWeb": "Il ne s'est rien passé ?  {{app}} application de bureau.", "downloadApp": "Téléchargez l'application", "ifDoNotHaveApp": "Si vous n'avez pas encore l'application :", "ifHaveApp": "Si vous possédez déjà l'application :", "ifYouDontHaveTheAppYet": "Si vous n'avez pas encore l'application", "joinInApp": "Rejoignez cette réunion en utilisant l'application", "joinMeetingWithDesktopApp": "Rejoindre une réunion avec l'application de bureau", "launchMeetingInDesktopApp": "Lancer la réunion dans l'application de bureau", "launchWebButton": "Lancement sur le Web", "title": "Lancer votre réunion dans {{app}}...", "tryAgainButton": "Réessayez sur le bureau"}, "defaultLink": "par ex. {{url}}", "defaultNickname": "ex. ", "deviceError": {"cameraError": "Échec de l'accès à votre caméra", "cameraPermission": "Erreur lors de l'obtention de l'autorisation de la caméra", "microphoneError": "Impossible d'accéder à votre microphone", "microphonePermission": "Erreur lors de l'obtention de l'autorisation du microphone"}, "deviceSelection": {"noPermission": "Autorisation non accordée", "previewUnavailable": "Aperçu indisponible", "selectADevice": "Sélectionnez un appareil", "testAudio": "Jouer un son de test"}, "dialog": {"accessibilityLabel": {"liveStreaming": "Diffusion en direct"}, "add": "Ajouter", "allow": "Permettre", "alreadySharedVideoMsg": "Un autre participant partage déjà une vidéo. ", "alreadySharedVideoTitle": "Une seule vidéo partagée est autorisée à la fois", "applicationWindow": "Fenêtre d'application", "authenticationRequired": "Authentification requise", "Back": "<PERSON><PERSON>", "cameraConstraintFailedError": "Votre caméra ne satisfait pas certaines des contraintes requises.", "cameraNotFoundError": "La caméra n'a pas été trouvée.", "cameraNotSendingData": "Nous ne parvenons pas à accéder à votre caméra. ", "cameraNotSendingDataTitle": "Impossible d'accéder à la caméra", "cameraPermissionDeniedError": "Vous n'avez pas accordé l'autorisation d'utiliser votre caméra. ", "cameraTimeoutError": "Impossible de démarrer la source vidéo. ", "cameraUnknownError": "Impossible d'utiliser l'appareil photo pour une raison inconnue.", "cameraUnsupportedResolutionError": "Votre caméra ne prend pas en charge la résolution vidéo requise.", "Cancel": "Annuler", "cannotToggleScreenSharingNotSupported": "Impossible de basculer le partage d'écran : non pris en charge.", "close": "<PERSON><PERSON><PERSON>", "closingAllTerminals": "Fermeture de tous les terminaux", "conferenceDisconnectMsg": "Vous souhaiterez peut-être vérifier votre connexion réseau.  {{seconds}} seconde...", "conferenceDisconnectTitle": "Vous avez été déconnecté.", "conferenceReloadMsg": "Nous essayons de résoudre ce problème.  {{seconds}} seconde...", "conferenceReloadTitle": "Malheureusement, quelque chose s'est mal passé.", "confirm": "Confirmer", "confirmNo": "Non", "confirmYes": "O<PERSON>", "connectError": "Oups ! ", "connectErrorWithMsg": "Oups !  {{msg}}", "connecting": "De liaison", "contactSupport": "Contacter l'assistance", "copied": "<PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON>", "customAwsRecording": "Enregistrement AWS personnalisé", "deleteCache": "Supprimer le cache ", "dismiss": "<PERSON><PERSON><PERSON>", "displayNameRequired": "Salut! ", "displayUserName": "", "donationCNotificationTitle": "Faites un don via <PERSON>lick and <PERSON>ledge", "donationCNPLabel": "Entrez l'URL du formulaire de connexion ou l'URL du widget", "donationLabel": "Entrez l'URL de la campagne de don", "donationNotificationDescription": "Faites-nous un don pour soutenir notre cause ", "donationNotificationTitle": "Faire un don via Donorbox", "done": "Fait", "e2eeDescription": "Le chiffrement de bout en bout est actuellement EXPÉRIMENTAL. ", "e2eeLabel": "Activer le chiffrement de bout en bout", "e2eeWarning": "AVERTISSEMENT : tous les participants à cette réunion ne semblent pas prendre en charge le chiffrement de bout en bout. ", "embedMeeting": "Intégrer la réunion", "enterCdonation": "Exemple : https://connect.clickandpledge.com/w/Form/283ah-0069-441a-ba0f-d42a9dec9821", "enterDisplayEmail": "Identifiant de courrier électronique", "enterDisplayName": "Nom et prénom", "enterDisplayNameToJoin": "Veuillez entrer votre nom pour rejoindre", "enterDonation": "Exemple : https://donorbox.org/donate-an-organisation", "enterMeetingId": "Entrez l'ID de la réunion", "enterMeetingPassword": "<PERSON>sir le mot de passe de la réunion", "error": "<PERSON><PERSON><PERSON>", "errorMeetingID": "Ajouter un identifiant de réunion", "errorMeetingPassword": "Ajouter un mot de passe de réunion", "forceMuteEveryoneDialog": "Êtes-vous sûr de vouloir verrouiller le microphone de tout le monde, à l'exception des modérateurs ? ", "forceMuteEveryoneElseDialog": "Mettez-les en sourdine et désactivez leur microphone", "forceMuteEveryoneElsesVideoDialog": "Une fois la caméra désactivée, ils ne pourront plus activer leur caméra.", "forceMuteEveryoneElsesVideoTitle": "Forcer la mise en sourdine de la caméra de tout le monde, sauf {{whom}}?", "forceMuteEveryoneElseTitle": "Forcer la mise en sourdine de tout le monde sauf {{whom}}?", "forceMuteEveryoneSelf": "toi-même", "forceMuteEveryoneStartMuted": "Tout le monde commence à se mettre en sourdine à partir de maintenant", "forceMuteEveryonesVideoDialog": "Etes-vous sûr de vouloir verrouiller la vidéo de ce participant ? ", "forceMuteEveryonesVideoTitle": "Forcer la mise en sourdine de la vidéo de tout le monde ?", "forceMuteEveryoneTitle": "Forcer la mise en sourdine de tout le monde ?", "forceMuteParticipantBody": "Forcer la mise en sourdine du participant.", "forceMuteParticipantButton": "Forcer la sourdine", "forceMuteParticipantDialog": "Etes-vous sûr de vouloir verrouiller le microphone de ce participant ? ", "forceMuteParticipantsVideoBody": "La vidéo des participants sera désactivée et ils ne pourront plus se rallumer", "forceMuteParticipantsVideoButton": "Désactiver la caméra", "forceMuteParticipantsVideoTitle": "Désactiver la caméra de ce participant ?", "forceMuteParticipantTitle": "Forcer la mise en sourdine de ce participant ?", "gracefulShutdown": "Notre service est actuellement en maintenance. ", "grantModeratorDialog": "Êtes-vous sûr de vouloir faire de ce participant un modérateur ?", "grantModeratorTitle": "Modérateur de subventions", "hangUpLeaveReason": "Cette réunion a été clôturée par le modérateur", "hideShareAudioHelper": "Ne plus afficher cette boîte de dialogue", "IamHost": "je suis l'hôte", "incorrectPassword": "Nom d'utilisateur ou mot de passe incorrect", "incorrectRoomLockPassword": "Mot de passe incorrect", "internalError": "Oups !  {{error}}", "internalErrorTitle": "E<PERSON>ur interne", "kickMessage": "Aie! ", "kickParticipantButton": "Supprimer un utilisateur", "kickParticipantDialog": "Êtes-vous sûr de vouloir supprimer ce participant ?", "kickParticipantTitle": "Supprimer ce participant ?", "kickTitle": "Aie! ", "liveStreaming": "Diffusion en direct", "liveStreamingDisabledBecauseOfActiveRecordingTooltip": "Impossible lorsque l'enregistrement est actif", "liveStreamingDisabledForGuestTooltip": "Les invités ne peuvent pas démarrer la diffusion en direct.", "liveStreamingDisabledTooltip": "Démarrer la diffusion en direct désactivée.", "localUserControls": "Contrôles utilisateur locaux", "lockMessage": "Échec du verrouillage de la conférence.", "lockRoom": "Ajouter une réunion $t(lockRoomPasswordUppercase)", "lockTitle": "Le verrouillage a échoué", "login": "Se connecter", "logoutQuestion": "Êtes-vous sûr de vouloir vous déconnecter et arrêter la conférence ?", "logoutTitle": "Déconnexion", "maxUsersLimitReached": "La limite du nombre maximum de participants a <PERSON><PERSON> attein<PERSON>. ", "maxUsersLimitReachedTitle": "Limite maximale de participants atteinte", "meetHourRecording": "Enregistrement des heures de rencontre", "meetingID": "ID de réunion", "meetingIDandPassword": "Entrez l'ID de la réunion et le mot de passe", "meetingPassword": "Mot de passe de la réunion", "messageErrorApi": "oups! ", "messageErrorInvalid": "Identifiants invalides", "messageErrorNotModerator": "Oups ! ", "messageErrorNull": "Le nom d'utilisateur ou le mot de passe est vide", "micConstraintFailedError": "Votre microphone ne satisfait pas à certaines des contraintes requises.", "micNotFoundError": "Le microphone n'a pas été trouvé.", "micNotSendingData": "Accédez aux paramètres de votre ordinateur pour réactiver votre micro et ajuster son niveau", "micNotSendingDataTitle": "Votre micro est coupé par les paramètres de votre système", "micPermissionDeniedError": "Vous n'avez pas accordé l'autorisation d'utiliser votre microphone. ", "micTimeoutError": "Impossible de démarrer la source audio. ", "micUnknownError": "Impossible d'utiliser le microphone pour une raison inconnue.", "muteEveryoneDialog": "Êtes-vous sûr de vouloir couper le son de tout le monde ? ", "muteEveryoneElseDialog": "Une fois mis en sourdine, vous ne pourrez pas les réactiver, mais ils pourront le réactiver à tout moment.", "muteEveryoneElsesVideoDialog": "Une fois la caméra désactivée, vous ne pourrez plus la rallumer, mais ils pourront la rallumer à tout moment.", "muteEveryoneElsesVideoTitle": "<PERSON>és<PERSON>z la caméra de tout le monde, sauf {{whom}}?", "muteEveryoneElseTitle": "Couper le son de tout le monde sauf {{whom}}?", "muteEveryoneSelf": "toi-même", "muteEveryoneStartMuted": "Tout le monde commence à se taire à partir de maintenant", "muteEveryonesVideoDialog": "Êtes-vous sûr de vouloir désactiver la caméra de tout le monde ? ", "muteEveryonesVideoDialogOk": "Désactiver", "muteEveryonesVideoTitle": "Désactiver la caméra de tout le monde ?", "muteEveryoneTitle": "Mettre tout le monde en sourdine ?", "muteParticipantBody": "Vous ne pourrez pas réactiver leur son, mais ils pourront le réactiver à tout moment.", "muteParticipantButton": "<PERSON><PERSON>", "muteParticipantDialog": "Êtes-vous sûr de vouloir dés<PERSON>r ce participant ? ", "muteParticipantsVideoBody": "Vous ne pourrez pas rallumer la caméra, mais ils pourront la rallumer à tout moment.", "muteParticipantsVideoButton": "Désactiver la caméra", "muteParticipantsVideoDialog": "Êtes-vous sûr de vouloir éteindre la caméra de ce participant ? ", "muteParticipantsVideoTitle": "Désactiver la caméra de ce participant ?", "muteParticipantTitle": "Mettre ce participant en sourdine ?", "noDropboxToken": "Aucun jeton Dropbox valide", "noScreensharingInAudioOnlyMode": "Pas de partage d'écran en mode audio uniquement", "Ok": "D'ACCORD", "password": "Mot de passe", "passwordLabel": "La réunion a été verrouillée par un modérateur.  $t(lockRoomPassword) pour rejoindre.", "passwordNotSupported": "Fixer une réunion $t(lockRoomPassword) n’est pas pris en charge.", "passwordNotSupportedTitle": "$t(lockRoomPasswordUppercase) non pris en charge", "passwordRequired": "$t(lockRoomPasswordUppercase) requis", "permissionCameraRequiredError": "L'autorisation de la caméra est requise pour participer à des conférences avec vidéo. ", "permissionErrorTitle": "Autorisation requise", "permissionMicRequiredError": "L’autorisation du microphone est requise pour participer à des conférences avec audio. ", "popupError": "Votre navigateur bloque les fenêtres pop-up de ce site. ", "popupErrorTitle": "<PERSON><PERSON> b<PERSON>", "readMore": "plus", "recording": "Enregistrement", "recordingDisabledBecauseOfActiveLiveStreamingTooltip": "Impossible lorsqu'un flux en direct est actif", "recordingDisabledForGuestTooltip": "Les invités ne peuvent pas démarrer les enregistrements.", "recordingDisabledTooltip": "Démarrer l'enregistrement désactivé.", "rejoinNow": "Rejoignez-nous maintenant", "remoteControlAllowedMessage": "{{user}} accepté votre demande de télécommande !", "remoteControlDeniedMessage": "{{user}} a rejeté votre demande de télécommande !", "remoteControlErrorMessage": "Une erreur s'est produite lors de la tentative de demande d'autorisations de contrôle à distance depuis {{user}}!", "remoteControlRequestMessage": "Per<PERSON><PERSON>-vous {{user}} contrôler à distance votre bureau ?", "remoteControlShareScreenWarning": "Notez que si vous appuyez sur « Autoriser », vous partagerez votre écran !", "remoteControlStopMessage": "La session de contrôle à distance est terminée !", "remoteControlTitle": "Contrôle du bureau à distance", "remoteUserControls": "Contrôles utilisateur à distance de {{username}}", "Remove": "<PERSON><PERSON><PERSON>", "removeCDonation": "Guide Click and Pledge supprimé", "removeCDonationD": "Le lien de don a été supprimé avec succès", "removeDonation": "Lien de don Donorbox supprimé", "removeDonationD": "Le lien de don a été supprimé avec succès", "removePassword": "Retirer $t(lockRoomPassword)", "removeSharedVideoMsg": "Êtes-vous sûr de vouloir supprimer votre vidéo partagée ?", "removeSharedVideoTitle": "Supprimer la vidéo partagée", "reservationError": "Erreur du système de réservation", "reservationErrorMsg": "Code d'erreur : {{code}}, message: {{msg}}", "restartInitiatedBecauseOfBridgeFailure": "Redémarrage initié en raison d'une panne de pont", "retry": "<PERSON><PERSON><PERSON><PERSON>", "revokeModeration": "Révoquer l'utilisateur en tant que modérateur ?", "revokeModerationTitle": "Révoquer la modération", "screenSharingAudio": "Partager l'audio", "screenSharingFailed": "Oups ! ", "screenSharingFailedTitle": "Le partage d'écran a échoué !", "screenSharingPermissionDeniedError": "Oups ! ", "screenSharingUser": "{{displayName}} partage actuellement l'écran", "sendPrivateMessage": "Vous avez récemment reçu un message privé. ", "sendPrivateMessageCancel": "Envoyer au groupe", "sendPrivateMessageOk": "Envoyer en privé", "sendPrivateMessageTitle": "Envoyer en privé ?", "serviceUnavailable": "Service non disponible", "sessionRestarted": "Appel repris par le pont", "sessTerminated": "<PERSON><PERSON> terminé", "Share": "Partager", "shareAudio": "<PERSON><PERSON><PERSON>", "shareAudioTitle": "Comment partager de l'audio", "shareAudioWarningD1": "vous devez arrêter le partage d'écran avant de partager votre audio.", "shareAudioWarningD2": "vous devez redémarrer votre partage d'écran et cocher l'option \"Partager l'audio\".", "shareAudioWarningH1": "Si vous souhaitez partager uniquement de l'audio :", "shareAudioWarningTitle": "<PERSON><PERSON> de<PERSON> arrêter le partage d'écran avant de partager de l'audio", "shareMediaWarningGenericH2": "Si vous souhaitez partager votre écran et votre audio", "shareScreenWarningD1": "vous devez arrêter le partage audio avant de partager votre écran.", "shareScreenWarningD2": "vous devez arrêter le partage audio, d<PERSON><PERSON><PERSON> le partage d'écran et cocher l'option \"Partager l'audio\".", "shareScreenWarningH1": "Si vous souhaitez partager uniquement votre écran :", "shareScreenWarningTitle": "V<PERSON> devez arrêter le partage audio avant de partager votre écran", "shareVideoLinkError": "Veuillez fournir un lien YouTube correct.", "shareVideoTitle": "Partager Youtube", "shareYourScreen": "Partagez votre écran", "shareYourScreenDisabled": "Partage d'écran désactivé.", "shareYourScreenDisabledForGuest": "Les invités ne peuvent pas partager d’écran.", "startLiveStreaming": "Diffusion en direct + enregistrement", "startRecording": "Commencer l'enregistrement", "startRemoteControlErrorMessage": "Une erreur s'est produite lors de la tentative de démarrage de la session de contrôle à distance !", "stopLiveStreaming": "<PERSON><PERSON><PERSON><PERSON>", "stopRecording": "<PERSON><PERSON><PERSON><PERSON> l'enregistrement", "stopRecordingWarning": "Etes-vous sûr de vouloir arrêter l'enregistrement ?", "stopStreamingWarning": "Êtes-vous sûr de vouloir arrêter la diffusion en direct ?", "streamKey": "Clé de diffusion en direct", "Submit": "So<PERSON><PERSON><PERSON>", "switchInProgress": "Changement en cours.", "thankYou": "Merci d'avoir utilisé {{appName}}!", "token": "jeton", "tokenAuthFailed": "<PERSON><PERSON><PERSON><PERSON>, vous n'êtes pas autorisé à rejoindre cet appel.", "tokenAuthFailedTitle": "L'authentification a échoué", "transcribing": "Transcription", "unforceMuteEveryoneDialog": "Êtes-vous sûr de vouloir déverrouiller le microphone de tout le monde ? ", "unforceMuteEveryoneElseDialog": "Annulez-les, désactivez-les et laissez-les activer leur microphone", "unforceMuteEveryoneElsesVideoDialog": "Une fois la caméra activée, ils pourront activer leur caméra", "unforceMuteEveryoneElsesVideoTitle": "Activer la caméra de tout le monde, sauf {{whom}}?", "unforceMuteEveryoneElseTitle": "Annuler Forcer la mise en sourdine de tout le monde sauf {{whom}}?", "unforceMuteEveryoneSelf": "toi-même", "unforceMuteEveryonesVideoDialog": "Êtes-vous sûr de vouloir débloquer la vidéo de tout le monde ?", "unforceMuteEveryonesVideoTitle": "Activer la caméra de tout le monde ?", "unforceMuteEveryoneTitle": "Annuler Forcer la coupure du microphone de tout le monde ?", "unforceMuteParticipantBody": "Ann<PERSON>r le participant muet.", "unforceMuteParticipantButton": "Annuler Forcer la sourdine", "unforceMuteParticipantDialog": "Êtes-vous sûr de vouloir déverrouiller la vidéo de ce participant ?.", "unforceMuteParticipantsVideoBody": "La vidéo des participants sera activée et ils pourront se rallumer", "unforceMuteParticipantsVideoButton": "Activer la caméra", "unforceMuteParticipantsVideoTitle": "Débloquer la vidéo de ce participant ?", "unforceMuteParticipantTitle": "Annuler Forcer la mise en sourdine de ce participant ?", "unlockRoom": "Supprimer la réunion $t(lockRoomPassword)", "user": "Utilisa<PERSON>ur", "userIdentifier": "Identifiant de l'utilisateur", "userPassword": "Mot de passe utilisateur", "videoLink": "<PERSON>n vid<PERSON>o", "viewUpgradeOptions": "Afficher les options de mise à niveau", "viewUpgradeOptionsContent": "Pour obtenir un accès illimité à des fonctionnalités premium telles que l'enregistrement, les transcriptions, le streaming RTMP et plus encore, vous devrez mettre à niveau votre forfait.", "viewUpgradeOptionsTitle": "Vous avez découvert une fonctionnalité premium !", "WaitForHostMsg": "La conférence <b>{{room}}</b> n'a pas encore commencé. ", "WaitForHostMsgWOk": "La conférence <b>{{room}}</b> n'a pas encore commencé. ", "WaitforModerator": "Veuillez attendre l'arrivée du modérateur", "WaitforModeratorOk": "<PERSON><PERSON><PERSON>", "WaitingForHost": "En attendant l'hôte...", "WaitingForHostTitle": "En attendant l'hôte...", "Yes": "O<PERSON>", "yourEntireScreen": "Tout votre écran"}, "dialOut": {"statusMessage": "est maintenant {{status}}"}, "documentSharing": {"title": "LivePad"}, "donorbox": {"errorDesc": "Veuillez saisir une URL de campagne Donorbox valide. ", "errorNotification": "URL de la boîte de dons invalide", "title": "Ajouter l'URL de la campagne DonorBox", "titlenative": "BOÎTE DE DONNEURS"}, "e2ee": {"labelToolTip": "La communication audio et vidéo lors de cet appel est cryptée de bout en bout"}, "embedMeeting": {"title": "Intégrer cette réunion"}, "feedback": {"average": "<PERSON><PERSON><PERSON>", "bad": "<PERSON><PERSON><PERSON><PERSON>", "detailsLabel": "Parlez-nous-en davantage.", "good": "Bien", "rateExperience": "Évaluez votre expérience de réunion", "star": "<PERSON><PERSON><PERSON>", "veryBad": "<PERSON><PERSON><PERSON>", "veryGood": "Très bien"}, "giphy": {"giphy": "GIPHY", "noResults": "Aucun résultat trouvé :(", "search": "<PERSON><PERSON><PERSON>"}, "helpView": {"header": "Centre d'aide"}, "incomingCall": {"answer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "audioCallTitle": "A<PERSON> entrant", "decline": "<PERSON><PERSON><PERSON>", "productLabel": "à partir de l'heure de rencontre", "videoCallTitle": "<PERSON><PERSON> vidéo entrant"}, "info": {"accessibilityLabel": "Afficher les informations", "addPassword": "Ajouter $t(lockRoomPassword)", "cancelPassword": "Annuler $t(lockRoomPassword)", "conferenceURL": "Lien:", "copyNumber": "Numéro de copie", "country": "Pays", "dialANumber": "Pour rejoindre votre réunion, composez l'un de ces numéros, puis saisissez le code PIN.", "dialInConferenceID": "ÉPINGLE:", "dialInNotSupported": "Désolé, la connexion n'est actuellement pas prise en charge.", "dialInNumber": "Appel :", "dialInSummaryError": "Erreur lors de la récupération des informations de connexion maintenant. ", "dialInTollFree": "Sans frais", "genericError": "<PERSON><PERSON>, quel<PERSON> chose s'est mal passé.", "inviteLiveStream": "Pour visionner la diffusion en direct de cette réunion, cliquez sur ce lien : {{url}}", "invitePhone": "Pour rejoindre par téléphone, appuyez sur ceci : {{number}},,{{conferenceID}}#\n", "invitePhoneAlternatives": "Vous recherchez un autre numéro de téléphone ?\n {{url}}\n\n\nSi vous appelez également via un téléphone de chambre, rejoignez sans vous connecter à l'audio : {{silentUrl}}", "inviteSipEndpoint": "Pour vous inscrire en utilisant l'adresse SIP, saisissez ceci : {{sipUri}}", "inviteTextiOSInviteUrl": "Cliquez sur le lien suivant pour vous inscrire : {{inviteUrl}}.", "inviteTextiOSJoinSilent": "Si vous appelez via un téléphone de chambre, utilisez ce lien pour vous connecter sans vous connecter à l'audio : {{silentUrl}}.", "inviteTextiOSPersonal": "{{name}} vous invite à une réunion.", "inviteTextiOSPhone": "Pour nous rejoindre par téléphone, utilisez ce numéro : {{number}},,{{conferenceID}}#.  {{didUrl}}.", "inviteURLFirstPartGeneral": "Vous êtes invité à rejoindre une réunion.", "inviteURLFirstPartPersonal": "{{name}} vous invite à une réunion.\n", "inviteURLSecondPart": "\nRejo<PERSON>ez la réunion :\n{{url}}\n", "label": "Informations de connexion", "liveStreamURL": "Diffusion en direct :", "moreNumbers": "Plus de numéros", "noNumbers": "Aucun numéro à composer.", "noPassword": "Aucun", "noRoom": "Aucune pièce n’a été spécifiée pour se connecter.", "numbers": "Numé<PERSON>'<PERSON>", "password": "$t(lockRoomPasswordUppercase):", "sip": "Adresse SIP", "title": "Partager", "tooltip": "Partager le lien et les informations de connexion pour cette réunion"}, "inlineDialogFailure": {"msg": "Nous avons un peu trébuché.", "retry": "Essayer à nouveau", "support": "<PERSON><PERSON><PERSON>", "supportMsg": "Si cela continue, contactez"}, "inviteDialog": {"alertText": "Échec de l'invitation de certains participants.", "header": "Inviter", "searchCallOnlyPlaceholder": "Entrez le numéro de téléphone", "searchPeopleOnlyPlaceholder": "Rechercher des participants", "searchPlaceholder": "Numéro de téléphone ou du participant", "send": "Envoyer"}, "jitsiHome": "{{logo}} Logo, liens vers la page d'accueil", "keyboardShortcuts": {"focusLocal": "Concentrez-vous sur votre vidéo", "focusRemote": "Concentrez-vous sur la vidéo d'une autre personne", "fullScreen": "<PERSON><PERSON><PERSON><PERSON> ou quitter le mode plein écran", "keyboardShortcuts": "<PERSON><PERSON><PERSON><PERSON> clav<PERSON>", "localRecording": "A<PERSON>iche<PERSON> ou masquer les commandes d'enregistrement locales", "mute": "<PERSON><PERSON> ou ré<PERSON>r votre microphone", "pushToTalk": "<PERSON><PERSON><PERSON> pour parler", "raiseHand": "<PERSON><PERSON> ou baissez la main", "showSpeakerStats": "Afficher les statistiques des intervenants", "toggleChat": "<PERSON><PERSON><PERSON><PERSON><PERSON> ou fermer le chat", "toggleFilmstrip": "Afficher ou masquer les miniatures des vidéos", "toggleParticipantsPane": "A<PERSON><PERSON>r ou masquer le volet des participants", "toggleScreensharing": "Basculer entre l'appareil photo et le partage d'écran", "toggleShortcuts": "A<PERSON><PERSON><PERSON> ou masquer les raccourcis clavier", "videoMute": "<PERSON><PERSON><PERSON><PERSON> ou arrêter votre camé<PERSON>", "videoQuality": "<PERSON><PERSON>rer la qualité des appels"}, "liveChatView": {"header": "Assistance en direct 24h/24 et 7j/7"}, "liveStreaming": {"addStream": "Ajouter une destination", "busy": "Nous travaillons à libérer les ressources de streaming. ", "busyTitle": "Tous les streamers sont actuellement occupés", "changeSignIn": "Changez de compte.", "choose": "Choisissez une diffusion en direct", "chooseCTA": "Choisissez une option de diffusion en continu.  {{email}}.", "enterLinkedInUrlWithTheKey": "Entrez l'URL LinkedIn avec la clé", "enterStreamKey": "Entrez votre clé de diffusion en direct YouTube ici.", "enterStreamKeyFacebook": "Entrez votre clé de diffusion en direct Facebook ici.", "enterStreamKeyInstagram": "Entrez votre clé de diffusion en direct Instagram ici.", "enterStreamKeyYouTube": "Entrez votre {{youtube}} clé de diffusion en direct ici.", "error": "La diffusion en direct a échoué. ", "errorAPI": "Une erreur s'est produite lors de l'accès à vos diffusions YouTube. ", "errorLiveStreamNotEnabled": "La diffusion en direct n'est pas activée sur {{email}}. ", "expandedOff": "La diffusion en direct s'est arrêtée", "expandedOn": "La réunion est actuellement diffusée sur YouTube.", "expandedPending": "La diffusion en direct démarre...", "failedToStart": "La diffusion en direct n'a pas pu dé<PERSON>rer", "failToStartAutoLiveStreaming": "Échec du démarrage de la diffusion automatique en direct", "failToStartAutoRecording": "Impossible de démarrer l'enregistrement automatique", "getStreamKeyManually": "Nous n’avons pas pu récupérer de diffusions en direct. ", "googlePrivacyPolicy": "Politique de confidentialité de Google", "invalidStreamKey": "La clé de diffusion en direct est peut-être incorrecte.", "limitNotificationDescriptionNative": "Votre streaming sera limité à {{limit}} min.  {{app}}.", "limitNotificationDescriptionWeb": "En raison de la forte demande, votre streaming sera limité à {{limit}} min.  <a href={{url}} rel='noopener noreferrer' target='_blank'>{{app}}</a>.", "makeSureYouHaveEnoughStorageAvailableOnYourAccount": "Assurez-vous d'avoir suffisamment d'espace de stockage disponible sur votre compte.", "note": "Note", "off": "Diffusion en direct arrêtée", "offBy": "{{name}} arr<PERSON><PERSON> la <PERSON> en direct", "on": "La diffusion en direct a commencé", "onBy": "{{name}} a commencé la diffusion en direct", "pending": "Démarrage de la diffusion en direct...", "pleaseContactSupportForAssistance": "<PERSON>euillez contacter le support pour obtenir de l'aide.", "serviceName": "Service de diffusion en direct", "signedInAs": "Vous êtes actuellement connecté en tant que :", "signIn": "Connectez-vous avec Google", "signInCTA": "Connectez-vous ou saisissez votre clé de diffusion en direct depuis YouTube.", "signOut": "se déconnecter", "start": "Enregistrement + diffusion en direct", "startService": "Démarrer le service", "streamIdHelp": "Qu'est-ce que c'est ça?", "unavailableTitle": "Diffusion en direct indisponible", "youtubeTerms": "Conditions d'utilisation de YouTube"}, "lobby": {"admit": "Admettre", "admitAll": "<PERSON><PERSON><PERSON> tout", "allow": "Permettre", "backToKnockModeButton": "Pas de mot de passe, demandez à vous inscrire à la place", "dialogTitle": "Mode salon", "disableDialogContent": "Le mode lobby est actuellement activé. ", "disableDialogSubmit": "Désactiver", "emailField": "Entrez votre adresse e-mail", "enableDialogPasswordField": "Définir le mot de passe (facultatif)", "enableDialogSubmit": "Activer", "enableDialogText": "Le mode Hall vous permet de protéger votre réunion en autorisant les personnes à entrer uniquement après l'approbation formelle d'un modérateur.", "enterPasswordButton": "Entrez le mot de passe de la réunion", "enterPasswordTitle": "Entrez le mot de passe pour rejoindre la réunion", "invalidPassword": "Mot de passe invalide", "joiningMessage": "Vous rejoindrez la réunion dès que quelqu'un aura accepté votre demande", "joiningTitle": "Demander à rejoindre la réunion...", "joiningWithPasswordTitle": "Rejoindre avec mot de passe...", "joinRejectedMessage": "Votre demande d'adhésion a été rejetée par un modérateur.", "joinTitle": "Rejoindre la réunion", "joinWithPasswordMessage": "Vous essayez de vous connecter avec un mot de passe, veuillez patienter...", "knockButton": "<PERSON><PERSON><PERSON> à rejoindre", "knockingParticipantList": "Liste des participants à la frappe", "knockTitle": "Quelqu'un veut rejoindre la réunion", "nameField": "Entrez votre nom", "notificationLobbyAccessDenied": "{{targetParticipantName}} a été refusé par {{originParticipantName}}", "notificationLobbyAccessGranted": "{{targetParticipantName}} a été autorisé à rejoindre par {{originParticipantName}}", "notificationLobbyDisabled": "Le lobby a été désactivé par {{originParticipantName}}", "notificationLobbyEnabled": "Le lobby a été activé par {{originParticipantName}}", "notificationTitle": "Hall d'entrée", "passwordField": "Entrez le mot de passe de la réunion", "passwordJoinButton": "Rejoindre", "reject": "<PERSON><PERSON><PERSON>", "rejectAll": "<PERSON><PERSON><PERSON> tout", "toggleLabel": "Activer le <PERSON>"}, "localRecording": {"clientState": {"off": "Désactivé", "on": "Sur", "unknown": "Inconnu"}, "dialogTitle": "Contrôles d'enregistrement local", "duration": "<PERSON><PERSON><PERSON>", "durationNA": "N / A", "encoding": "Codage", "label": "LOR", "labelToolTip": "L'enregistrement local est engagé", "localRecording": "Enregistrement local", "me": "<PERSON><PERSON>", "messages": {"engaged": "Enregistrement local engagé.", "finished": "Séance d'enregistrement {{token}} fini. ", "finishedModerator": "Séance d'enregistrement {{token}} fini. ", "notModerator": "Vous n'êtes pas le modérateur. "}, "moderator": "Mod<PERSON><PERSON>ur", "no": "Non", "participant": "Participant", "participantStats": "Statistiques des participants", "sessionToken": "Jeton de session", "start": "Commencer l'enregistrement", "stop": "<PERSON><PERSON><PERSON><PERSON> l'enregistrement", "yes": "O<PERSON>"}, "lockRoomPassword": "mot de passe", "lockRoomPasswordUppercase": "Mot de passe", "lonelyMeetingExperience": {"button": "Inviter d'autres personnes", "youAreAlone": "Vous êtes le seul à la réunion"}, "me": "moi", "notify": {"connectedOneMember": "{{name}} rejoint la réunion", "connectedThreePlusMembers": "{{name}} et {{count}} d'autres ont rejoint la réunion", "connectedTwoMembers": "{{first}} et {{second}} rejoint la réunion", "disconnected": "déconnecté", "focus": "Thème de la conférence", "focusFail": "{{component}} non disponible - r<PERSON>sayez {{ms}} seconde", "grantedTo": "Droits de modérateur accordés à {{to}}!", "groupTitle": "Notifications", "hostAskedUnmute": "L'hôte souhaite que vous rétablissiez le son", "invitedOneMember": "{{name}} a <PERSON><PERSON> invité", "invitedThreePlusMembers": "{{name}} et {{count}} d'autres ont été invités", "invitedTwoMembers": "{{first}} et {{second}} ont été invités", "kickParticipant": "{{kicked}} a été supprimé par {{kicker}}", "me": "<PERSON><PERSON>", "moderationInEffectCSDescription": "Veuillez lever la main si vous souhaitez partager votre vidéo", "moderationInEffectCSTitle": "Le partage de contenu est désactivé par le modérateur", "moderationInEffectDescription": "<PERSON>'il vous plaît, levez la main si vous voulez parler", "moderationInEffectTitle": "Le micro est coupé par le modérateur", "moderationInEffectVideoDescription": "Veuillez lever la main si vous souhaitez que votre vidéo soit visible", "moderationInEffectVideoTitle": "La vidéo est coupée par le modérateur", "moderationRequestFromModerator": "L'hôte souhaite que vous rétablissiez le son", "moderationRequestFromParticipant": "<PERSON><PERSON><PERSON> parler", "moderationStartedTitle": "La modération a commencé", "moderationStoppedTitle": "Modération arrêtée", "moderationToggleDescription": "par {{participantDisplayName}}", "moderator": "Droits de modérateur accordés !", "muted": "V<PERSON> avez commencé la conversation en mode silencieux.", "mutedRemotelyDescription": "Vous pouvez toujours ré<PERSON>r le son lorsque vous êtes prêt à parler. ", "mutedRemotelyTitle": "Vous avez été mis en sourdine par {{participantDisplayName}}!", "mutedTitle": "Vous êtes en sourdine !", "newDeviceAction": "Utiliser", "newDeviceAudioTitle": "Nouveau périphérique audio détecté", "newDeviceCameraTitle": "Nouvelle caméra détectée", "OldElectronAPPTitle": "Faille de sécurité !", "oldElectronClientDescription1": "Vous semblez utiliser une ancienne version du client Meet Hour qui présente des vulnérabilités de sécurité connues.  ", "oldElectronClientDescription2": "dernière version", "oldElectronClientDescription3": " maintenant!", "passwordRemovedRemotely": "$t(lockRoomPasswordUppercase) supprimé par un autre participant", "passwordSetRemotely": "$t(lockRoomPasswordUppercase) défini par un autre participant", "raisedHand": "{{name}} v<PERSON><PERSON><PERSON> parler.", "raiseHandAction": "Lever la main", "reactionSounds": "D<PERSON><PERSON>r les sons", "reactionSoundsForAll": "<PERSON><PERSON><PERSON><PERSON> les sons pour tous", "screenShareNoAudio": " La case Partager l’audio n’était pas cochée dans l’écran de sélection de la fenêtre.", "screenShareNoAudioTitle": "Impossible de partager l'audio du système !", "somebody": "Quelqu'un", "startSilentDescription": "Rejoindre la réunion pour activer l'audio", "startSilentTitle": "V<PERSON> avez rejoint sans sortie audio !", "suboptimalBrowserWarning": "Nous craignons que votre expérience de réunion ne soit pas aussi bonne ici.  <a href='{{recommendedBrowserPageLink}}' target='_blank'>navigateurs entièrement pris en charge</a>.", "suboptimalExperienceTitle": "Avertissement du navigateur", "unmute": "<PERSON><PERSON> le son", "videoMutedRemotelyDescription": "Vous pouvez toujours le réactiver.", "videoMutedRemotelyTitle": "Votre caméra a été désactivée par {{participantDisplayName}}!"}, "participantsPane": {"actions": {"allow": "Autoriser les participants à :", "askUnmute": "<PERSON><PERSON><PERSON> <PERSON> le son", "blockEveryoneMicCamera": "<PERSON><PERSON>quez le micro et la caméra de tout le monde", "forceMute": "Forcer la mise en sourdine du son", "forceMuteAll": "Forcer tout le silence", "forceMuteAllVideo": "Forcer la vidéo à tout couper", "forceMuteEveryoneElse": "Forcer la mise en sourdine de tout le monde", "forceMuteEveryoneElseVideo": "Forcer la mise en sourdine de tout le monde", "forceMuteVideo": "Forcer la vidéo muette", "invite": "Inviter quelqu'un", "mute": "<PERSON><PERSON>", "muteAll": "<PERSON><PERSON>", "muteEveryoneElse": "Couper le son de tous les autres", "startModeration": "<PERSON><PERSON><PERSON><PERSON> le son ou démarrer la vidéo", "stopEveryonesVideo": "<PERSON><PERSON><PERSON><PERSON>z la vidéo de tout le monde", "stopVideo": "<PERSON><PERSON><PERSON><PERSON> la vidéo", "unblockEveryoneMicCamera": "<PERSON><PERSON><PERSON><PERSON>quez le micro et la caméra de tout le monde", "unForceMute": "annuler forcer la coupure audio", "unForceMuteAll": "Annuler Forcer la mise en sourdine", "unforceMuteAllVideo": "Annuler Forcer la mise en sourdine de la vidéo", "unforceMuteEveryoneElse": "Annuler forcer la mise en sourdine de tout le monde", "unforceMuteEveryoneElseVideo": "Annuler forcer la mise en sourdine de la vidéo pour tous les autres", "unForceMuteVideo": "Annuler la mise en sourdine forcée de la vidéo"}, "close": "<PERSON><PERSON><PERSON>", "header": "Participants", "headings": {"lobby": "Hall d'entrée ({{count}})", "participantsList": "Participants à la réunion ({{count}})", "waitingLobby": "En attente dans le hall ({{count}})"}, "search": "Rechercher des participants"}, "passwordDigitsOnly": "Jus<PERSON><PERSON>'à {{number}} chiffres", "passwordSetRemotely": "défini par un autre participant", "polls": {"answer": {"skip": "Sauter", "submit": "So<PERSON><PERSON><PERSON>"}, "by": "Par {{ name }}", "create": {"addOption": "Ajouter une option", "answerPlaceholder": "Option {{index}}", "cancel": "Annuler", "create": "<PERSON><PERSON><PERSON> un sondage", "pollOption": "Option de sondage {{index}}", "pollQuestion": "Question de sondage", "questionPlaceholder": "Poser une question", "removeOption": "Supprimer l'option", "send": "Envoyer"}, "notification": {"description": "<PERSON><PERSON><PERSON><PERSON>r l'onglet sondages pour voter", "title": "Un nouveau sondage a été ajouté à cette réunion"}, "results": {"changeVote": "Changer de vote", "empty": "Il n'y a pas encore de sondages lors de la réunion. ", "hideDetailedResults": "Masquer les détails", "showDetailedResults": "Aff<PERSON>r les détails", "vote": "Voter"}}, "poweredby": "© Meet Hour LLC", "prejoin": {"alreadyOneConferenceIsRunningInBackground": "Une conférence se déroule déjà en arrière-plan.", "audioAndVideoError": "Erreur audio et vidéo :", "audioDeviceProblem": "Il y a un problème avec votre périphérique audio", "audioOnlyError": "Erreur audio :", "audioTrackError": "Impossible de créer une piste audio.", "calling": "<PERSON><PERSON>", "callMe": "<PERSON><PERSON><PERSON><PERSON>moi", "callMeAtNumber": "Appelez-moi à ce numéro :", "configuringDevices": "Configuration des appareils...", "connectedWithAudioQ": "Vous êtes connecté à l'audio ?", "connection": {"good": "Votre connexion Internet semble bonne !", "nonOptimal": "Votre connexion Internet n'est pas optimale", "poor": "Vous avez une mauvaise connexion Internet"}, "connectionDetails": {"audioClipping": "Nous nous attendons à ce que votre audio soit tronqué.", "audioHighQuality": "Nous attendons que votre audio soit d’excellente qualité.", "audioLowNoVideo": "Nous nous attendons à ce que votre qualité audio soit faible et qu'il n'y ait pas de vidéo.", "goodQuality": "G<PERSON><PERSON>! ", "noMediaConnectivity": "Nous n'avons pas trouvé de moyen d'établir une connectivité multimédia pour ce test. ", "noVideo": "Nous nous attendons à ce que votre vidéo soit terrible.", "undetectable": "Si vous ne parvenez toujours pas à passer des appels dans le navigateur, nous vous recommandons de vous assurer que vos haut-parleurs, votre microphone et votre caméra sont correctement configurés, que vous avez accordé à votre navigateur les droits pour utiliser votre microphone et votre caméra et que la version de votre navigateur est à jour. ", "veryPoorConnection": "Nous nous attendons à ce que la qualité de votre appel soit vraiment épouvantable.", "videoFreezing": "Nous nous attendons à ce que votre vidéo se fige, devienne noire et pixellisée.", "videoHighQuality": "Nous attendons que votre vidéo soit de bonne qualité.", "videoLowQuality": "Nous nous attendons à ce que votre vidéo soit de mauvaise qualité en termes de fréquence d'images et de résolution.", "videoTearing": "Nous nous attendons à ce que votre vidéo soit pixellisée ou comporte des artefacts visuels."}, "copyAndShare": "Copier et partager le lien de la réunion", "dashboard": "Tableau de bord", "daysAgo": "{{daysCount}} il y a quelques jours", "dialing": "Numérotation", "dialInMeeting": "Connectez-vous à la réunion", "dialInPin": "Connectez-vous à la réunion et saisissez le code PIN :", "doNotShow": "Ne plus afficher cet écran", "enterMeetingIdOrLink": "Entrez l'ID de la réunion ou le lien", "errorDialOut": "Impossible de composer le numéro", "errorDialOutDisconnected": "Impossible de composer le numéro. ", "errorDialOutFailed": "Impossible de composer le numéro. ", "errorDialOutStatus": "Erreur lors de l'obtention du statut de numérotation", "errorMissingEmail": "Veuillez entrer votre email pour rejoindre la réunion", "errorMissingName": "Veuillez entrer votre nom pour rejoindre la réunion", "errorNameLength": "Veuillez entrer au moins 3 lettres dans votre nom", "errorStatusCode": "<PERSON><PERSON>ur lors de la composition, code d'état : {{status}}", "errorValidation": "La validation du numéro a échoué", "features": "Caractéristiques", "guestNotAllowedMsg": "Les invités ne sont pas autorisés à rejoindre cette réunion", "initiated": "<PERSON><PERSON>", "invalidEmail": "E-mail invalide", "iWantToDialIn": "Je veux me connecter", "joinAMeeting": "Rejoindre une réunion", "joinAudioByPhone": "Rejoignez-nous avec l'audio du téléphone", "joinMeeting": "Rejoindre la réunion", "joinMeetingGuest": "Rejoindre la réunion en tant qu'invité", "joinWithoutAudio": "Rejoindre sans audio", "keyboardShortcuts": "<PERSON><PERSON> les rac<PERSON> clavier", "linkCopied": "Lien copié dans le presse-papier", "logout": "Déconnexion", "lookGood": "Il semble que votre microphone fonctionne correctement", "maximumAllowedParticipantsErr": "Le nombre maximum de participants autorisés a été atteint lors de ces réunions. ", "meetingReminder": "La réunion va commencer {{time}}. ", "multipleConferenceInitiation": "Initiation à plusieurs conférences", "oops": "Oups !", "oppsMaximumAllowedParticipantsErr": "Oups ! ", "or": "ou", "parallelMeetingsLicencesErr": "Impossible de démarrer la réunion. ", "peopleInTheCall": "Personnes à l'appel", "pleaseEnterEmail": "Veuillez entrer votre e-mail", "pleaseEnterFullName": "Veuillez entrer votre nom complet", "premeeting": "Pré-réunion", "profile": "Profil", "readyToJoin": "<PERSON>r<PERSON><PERSON> à rejoindre ?", "recentMeetings": "Réunions récentes", "screenSharingError": "Erreur de partage d'écran :", "showScreen": "Activer l'écran de pré-réunion", "signinsignup": "Se connecter / S'inscrire", "startWithPhone": "Commencez par l'audio du téléphone", "subScriptionInactiveErr": "Votre abonnement est inactif. ", "systemUpgradedInformation": "Nous avons mis à niveau notre système vers la version 2.0. ", "userNotAllowedToJoin": "L'utilisateur n'est pas autorisé à rejoindre", "videoOnlyError": "Erreur vidéo :", "videoTrackError": "Impossible de créer une piste vidéo.", "viewAllNumbers": "voir tous les numéros", "waitForModeratorMsg": "Veuillez patienter pendant que le modérateur rejoint l'appel.", "waitForModeratorMsgDynamic": "Veuillez patienter pendant {{Moderator}} rejoint l'appel.", "youAreNotAllowed": "Vous n'êtes pas autorisé"}, "presenceStatus": {"busy": "<PERSON><PERSON><PERSON><PERSON>", "calling": "Appel...", "connected": "Connecté", "connecting": "De liaison...", "connecting2": "De liaison*...", "disconnected": "Déconnecté", "expired": "Expiré", "ignored": "Ignoré", "initializingCall": "Initialisation de l'appel...", "invalidToken": "<PERSON><PERSON> invalide", "invited": "Invi<PERSON>", "rejected": "<PERSON><PERSON><PERSON>", "ringing": "Sonnerie...", "signInAsHost": "Connectez-vous en tant qu'hôte"}, "profile": {"avatar": "avatar", "setDisplayNameLabel": "Votre nom d'affichage", "setEmailInput": "Entrez votre adresse e-mail", "setEmailLabel": "Votre email", "title": "Profil"}, "raisedHand": "<PERSON><PERSON><PERSON><PERSON> parler", "recording": {"authDropboxText": "Télécharger sur Dropbox", "availableS3Space": "Espace utilisé : {{s3_used_space}} de {{s3_free_space}}", "availableSpace": "Espace disponible : {{spaceLeft}} Mo (environ {{duration}} minutes d'enregistrement)", "beta": "BÊTA", "busy": "Nous travaillons à libérer les ressources d'enregistrement. ", "busyTitle": "Tous les enregistreurs sont actuellement occupés", "copyLink": "Copier le lien", "error": "L'enregistrement a échoué. ", "errorFetchingLink": "Erreur lors de la récupération du lien d'enregistrement.", "expandedOff": "L'enregistrement s'est arrêté", "expandedOn": "La réunion est actuellement enregistrée.", "expandedPending": "L'enregistrement est en cours de démarrage...", "failedToStart": "L'enregistrement n'a pas pu démarrer", "fileSharingdescription": "Partager l'enregistrement avec les participants à la réunion", "limitNotificationDescriptionNative": "En raison de la forte demande, votre enregistrement sera limité à {{limit}} min.  <3>{{app}}</3>.", "limitNotificationDescriptionWeb": "En raison de la forte demande, votre enregistrement sera limité à {{limit}} min.  <a href={{url}} rel='noopener noreferrer' target='_blank'>{{app}}</a>.", "linkGenerated": "Nous avons généré un lien vers votre enregistrement.", "live": "EN DIRECT", "loggedIn": "Connecté en tant que {{userName}}", "off": "L'enregistrement s'est arrêté", "offBy": "{{name}} arr<PERSON><PERSON> l'enregistrement", "on": "L'enregistrement a commencé", "onBy": "{{name}} commencé l'enregistrement", "pending": "Préparation de l'enregistrement de la réunion...", "rec": "REC", "recLive": "EN DIRECT + ENREGISTREMENT", "serviceDescription": "Votre enregistrement sera sauvegardé par le service d'enregistrement", "serviceDescriptionCloud": "Enregistrement dans le cloud", "serviceName": "Service d'enregistrement", "signIn": "Se connecter", "signOut": "se déconnecter", "unavailable": "Oups !  {{serviceName}} est actuellement indisponible. ", "unavailableTitle": "Enregistrement indisponible", "uploadToCloud": "Télécharger vers le cloud"}, "sectionList": {"pullToRefresh": "Tirer pour rafraîchir"}, "security": {"about": "Vous pouvez ajouter un $t(lockRoomPassword) à votre réunion.  $t(lockRoomPassword) avant qu'ils ne soient autorisés à rejoindre la réunion.", "aboutReadOnly": "Les participants modérateurs peuvent ajouter un $t(lockRoomPassword) à la réunion.  $t(lockRoomPassword) avant qu'ils ne soient autorisés à rejoindre la réunion.", "insecureRoomNameWarning": "Le nom de la pièce n'est pas sûr. ", "securityOptions": "Options de sécurité"}, "settings": {"calendar": {"about": "Le {{appName}} l'intégration du calendrier est utilisée pour accéder en toute sécurité à votre calendrier afin qu'il puisse lire les événements à venir.", "disconnect": "Déconnecter", "microsoftSignIn": "Connectez-vous avec Microsoft", "signedIn": "J'accède actuellement aux événements du calendrier pour {{email}}. ", "title": "<PERSON><PERSON><PERSON>"}, "desktopShareFramerate": "Fréquence d'images du partage de bureau", "desktopShareHighFpsWarning": "Une fréquence d'images plus élevée pour le partage de bureau peut affecter votre bande passante. ", "desktopShareWarning": "V<PERSON> de<PERSON> redémarrer le partage d'écran pour que les nouveaux paramètres prennent effet.", "devices": "Appareils", "followMe": "Tout le monde me suit", "framesPerSecond": "images par seconde", "incomingMessage": "Message entrant", "language": "<PERSON><PERSON>", "languageSettings": "Paramètres de langue", "loggedIn": "Connecté en tant que {{name}}", "microphones": "Micros", "moderator": "Mod<PERSON><PERSON>ur", "more": "Plus", "name": "Nom", "noDevice": "Aucun", "noLanguagesAvailable": "Aucune langue disponible", "participantJoined": "Participant rejoint", "participantLeft": "Participant parti", "playSounds": "<PERSON>uer du son sur", "sameAsSystem": "Identique au système ({{label}})", "selectAudioOutput": "Sortie audio", "selectCamera": "Caméra", "selectLanguage": "Sélectionnez la langue", "selectMic": "Microphone", "sounds": "Des sons", "speakers": "Haut-parleurs", "startAudioMuted": "Tout le monde commence en sourdine", "startVideoMuted": "Tout le monde commence caché", "talkWhileMuted": "Parler en mode sourdine", "title": "Paramètres"}, "settingsView": {"advanced": "<PERSON><PERSON><PERSON>", "alertCancel": "Annuler", "alertOk": "D'ACCORD", "alertTitle": "Avertissement", "alertURLText": "L'URL du serveur saisie n'est pas valide", "buildInfoSection": "Informations sur la construction", "conferenceSection": "Confé<PERSON>ce", "disableCallIntegration": "Désactiver l'intégration des appels natifs", "disableCrashReporting": "Désactiver le rapport d'erreur", "disableCrashReportingWarning": "Êtes-vous sûr de vouloir dés<PERSON>r le rapport d'erreur ? ", "disableP2P": "Désactiver le mode Peer-To-Peer", "displayName": "Nom d'affichage", "email": "E-mail", "header": "Paramètres", "profileSection": "Profil", "serverURL": "URL du serveur", "showAdvanced": "Afficher les paramètres avancés", "startWithAudioMuted": "Commencez avec le son coupé", "startWithVideoMuted": "Commencez par couper la vidéo", "version": "Version"}, "share": {"dialInfoText": "\n\n=====\n\n\n\n{{defaultDialInNumber}}Cliquez sur ce lien pour voir les numéros de téléphone à composer pour cette réunion\n{{dialInfoPageUrl}}", "mainText": "Cliquez sur le lien suivant pour rejoindre la réunion :\n{{roomUrl}}"}, "speaker": "Conférencier", "speakerStats": {"hours": "{{count}}h", "minutes": "{{count}}m", "name": "Nom", "seconds": "{{count}}s", "speakerStats": "Statistiques des intervenants", "speakerTime": "Temps de parole"}, "startupoverlay": {"genericTitle": "La réunion doit utiliser votre microphone et votre caméra.", "policyText": " ", "title": "{{app}} doit utiliser votre microphone et votre caméra."}, "suspendedoverlay": {"rejoinKeyTitle": "Rejoindre", "text": "Appuyez sur le <i>Rejoindre</i> bouton pour se reconnecter.", "title": "Votre appel vidéo a été interrompu car cet ordinateur s'est mis en veille."}, "toolbar": {"accessibilityLabel": {"audioOnly": "Activer/désactiver l'audio uniquement", "audioRoute": "<PERSON><PERSON><PERSON> le périphérique audio", "boo": "<PERSON><PERSON>", "callQuality": "G<PERSON>rer la qualité vidéo", "carmode": "Mode voiture", "cc": "Basculer les sous-titres", "chat": "<PERSON><PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON> le chat", "clap": "Taper", "collapse": "Effondrement", "document": "Basculer le document partagé", "donationCLP": "Paramètres de connexion C&P", "donationLink": "Paramètres de la boîte de donneurs", "download": "Téléchargez nos applications", "embedMeeting": "Intégrer la réunion", "expand": "Développer", "feedback": "Laisser les commentaires", "fullScreen": "Basculer en plein écran", "genericIFrame": "Basculer l'application partagée", "giphy": "Basculer le menu GIPHY", "grantModerator": "Modérateur de subventions", "hangup": "<PERSON><PERSON>ter la réunion", "help": "Aide", "invite": "Inviter des personnes", "kick": "Participant au coup de pied", "laugh": "Rire", "leaveConference": "<PERSON><PERSON>ter la réunion", "like": "Bravo", "lobbyButton": "Activer/désactiver le mode lobby", "localRecording": "Basculer les commandes d'enregistrement local", "lockRoom": "Changer le mot de passe de la réunion", "moreActions": "Plus de propositions", "moreActionsMenu": "Menu Plus d'actions", "moreOptions": "Afficher plus d'options", "mute": "Désactiver/Ré<PERSON>r le son", "muteEveryone": "Couper le son de tout le monde", "muteEveryoneElse": "Couper le son de tous les autres", "muteEveryoneElsesVideo": "Désactivez la caméra de tout le monde", "muteEveryonesVideo": "Désactivez la caméra de tout le monde", "participants": "Participants", "party": "<PERSON><PERSON><PERSON>", "pip": "Activer/désactiver le mode Image dans l'image", "privateMessage": "Envoyer un message privé", "profile": "Modifiez votre profil", "raiseHand": "Levez/baissez la main", "reactionsMenu": "<PERSON><PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON>er le menu des réactions", "recording": "Activer/désactiver l'enregistrement", "remoteMute": "Participant muet", "remoteVideoMute": "Désactiver la caméra du participant", "removeDonation": "Supprimer DonorBox", "rmoveCDonation": "Supprimer C&P", "security": "Options de sécurité", "selectBackground": "Sélectionnez l'arrière-plan", "Settings": "Basculer les paramètres", "shareaudio": "Partager l'audio", "sharedvideo": "Activer le partage de vidéos YouTube", "shareRoom": "Inviter quelqu'un", "shareYourScreen": "<PERSON><PERSON><PERSON><PERSON>/<PERSON>rr<PERSON><PERSON> de partager votre écran", "shortcuts": "Basculer les raccourcis", "show": "Spectacle sur scène", "speakerStats": "Activer/Désactiver les statistiques des intervenants", "surprised": "<PERSON><PERSON><PERSON>", "tileView": "Basculer l'affichage des vignettes", "toggleCamera": "Basculer la caméra", "toggleFilmstrip": "Basculer la pellicule", "toggleReactions": "Basculer les réactions", "videoblur": "<PERSON><PERSON> le flou vidéo", "videomute": "<PERSON><PERSON><PERSON><PERSON> / <PERSON><PERSON><PERSON><PERSON>"}, "addPeople": "Ajouter des personnes à votre appel", "audioOnlyOff": "Désactiver le mode faible bande passante", "audioOnlyOn": "Activer le mode faible bande passante", "audioRoute": "<PERSON><PERSON><PERSON> le périphérique audio", "audioSettings": "Paramètres audio", "authenticate": "Authentifier", "boo": "<PERSON><PERSON>", "callQuality": "G<PERSON>rer la qualité vidéo", "chat": "<PERSON><PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON> le chat", "clap": "Applaudissez", "closeChat": "<PERSON><PERSON><PERSON> le chat", "closeParticipantsPane": "<PERSON><PERSON><PERSON> le volet des participants", "closeReactionsMenu": "<PERSON><PERSON>er le menu des réactions", "disableNoiseSuppression": "Désactiver la suppression du bruit", "disableReactionSounds": "Vous pouvez dés<PERSON>r les sons de réaction pour cette réunion", "documentClose": "Fermer LivePad", "documentOpen": "Partager LivePad", "donationCLP": "Paramètres de connexion C&P", "donationLink": "Paramètres de la boîte de donneurs", "download": "Téléchargez nos applications", "e2ee": "Chiffrement de bout en bout", "embedMeeting": "Intégrer la réunion", "enterFullScreen": "Afficher en plein écran", "enterTileView": "Entrer dans la vue en mosaïque", "exitFullScreen": "<PERSON><PERSON><PERSON> le plein écran", "exitTileView": "<PERSON><PERSON><PERSON> la vue en mosaïque", "feedback": "Laisser les commentaires", "genericIFrameClose": "<PERSON><PERSON><PERSON><PERSON> le <PERSON> blanc", "genericIFrameOpen": "Partager le tableau blanc", "genericIFrameWeb": "Tableau blanc", "hangup": "Partir", "hangUpforEveryOne": "Raccrocher pour tout le monde", "hangUpforMe": "Raccroche seulement pour moi", "hangUpText": "Êtes-vous sûr de vouloir rac<PERSON>cher ?", "help": "Aide", "hideReactions": "Masquer la réaction", "invite": "Inviter des personnes", "inviteViaCalendar": "Inviter via le calendrier", "iOSStopScreenShareAlertMessage": "<PERSON><PERSON><PERSON>z arrêter le partage d'écran avant de raccrocher.", "iOSStopScreenShareAlertTitle": "<PERSON><PERSON><PERSON><PERSON> le partage d'écran", "laugh": "Rire", "leaveConference": "<PERSON><PERSON>ter la réunion", "like": "Bravo", "lobbyButtonDisable": "Désactiver le mode lobby", "lobbyButtonEnable": "Activer le mode lobby", "login": "Se connecter", "logout": "Déconnexion", "lowerYourHand": "Baisse ta main", "moreActions": "Plus de propositions", "moreOptions": "Plus d'options", "mute": "Désactiver/Ré<PERSON>r le son", "muteEveryone": "Couper le son de tout le monde", "muteEveryonesVideo": "Désactivez la caméra de tout le monde", "noAudioSignalDesc": "Si vous ne l'avez pas volontairement désactivé à partir des paramètres système ou du matériel, envisagez de changer d'appareil.", "noAudioSignalDescSuggestion": "Si vous ne l'avez pas volontairement désactivé à partir des paramètres système ou du matériel, envisagez de passer au périphérique suggéré.", "noAudioSignalDialInDesc": "Vous pouvez également vous connecter en utilisant :", "noAudioSignalDialInLinkDesc": "Numé<PERSON>'<PERSON>", "noAudioSignalTitle": "Il n'y a aucune entrée provenant de votre micro !", "noisyAudioInputDesc": "Il semble que votre microphone fasse du bruit, pensez à le désactiver ou à changer d'appareil.", "noisyAudioInputTitle": "Votre microphone semble bruyant !", "openChat": "<PERSON><PERSON><PERSON><PERSON><PERSON> le chat", "openReactionsMenu": "<PERSON><PERSON><PERSON><PERSON><PERSON> le menu des réactions", "participants": "Participants", "party": "Célébration", "pip": "Mode Image dans l'image", "privateMessage": "Envoyer un message privé", "profile": "Modifiez votre profil", "raiseHand": "Levez/baissez la main", "raiseYourHand": "<PERSON>ez la main", "reactionBoo": "Envoyer une réaction huée", "reactionClap": "Envoyer une réaction d'applaudissement", "reactionLaugh": "Envoyer une réaction de rire", "reactionLike": "Envoyer un pouce levé en réaction", "reactionParty": "Envoyer une réaction de fête", "reactionSurprised": "Envoyer une réaction surprise", "removeDonation": "Supprimer DonorBox", "rmoveCDonation": "Supprimer C&P", "security": "Options de sécurité", "selectBackground": "Sélectionnez l'arrière-plan", "Settings": "Paramètres", "Share": "Partager", "shareaudio": "Partager l'audio", "sharedvideo": "Partager YouTube", "shareRoom": "Inviter quelqu'un", "shortcuts": "Aff<PERSON>r les raccourcis", "showReactions": "Afficher la réaction", "speakerStats": "Statistiques des intervenants", "startScreenSharing": "<PERSON><PERSON><PERSON><PERSON> le partage d'écran", "startSubtitles": "<PERSON><PERSON><PERSON><PERSON> les sous-titres", "stopAudioSharing": "Arr<PERSON><PERSON> le partage audio", "stopScreenSharing": "<PERSON><PERSON><PERSON><PERSON> le partage d'écran", "stopSharedVideo": "<PERSON><PERSON><PERSON>ter la vidéo YouTube", "stopSubtitles": "<PERSON><PERSON><PERSON><PERSON> les sous-titres", "surprised": "<PERSON><PERSON><PERSON>", "talkWhileMutedPopup": "Vous essayez de parler ? ", "tileViewToggle": "Basculer l'affichage des vignettes", "toggleCamera": "Basculer la caméra", "videomute": "<PERSON><PERSON><PERSON><PERSON> / <PERSON><PERSON><PERSON><PERSON>", "videoSettings": "Paramètres vidéo", "voiceCommand": "<PERSON><PERSON><PERSON><PERSON>r la commande vocale", "whiteBoardOpen": "Partager le tableau blanc", "zoomin": "<PERSON>mer", "zoomout": "Zoom arri<PERSON>"}, "transcribing": {"ccButtonTooltip": "<PERSON><PERSON><PERSON><PERSON>/<PERSON>rr<PERSON><PERSON> les sous-titres", "error": "La transcription a échoué. ", "expandedLabel": "La transcription est actuellement en cours", "failedToStart": "La transcription n'a pas pu dé<PERSON>rer", "labelToolTip": "La réunion est en cours de transcription", "off": "Transcription arrêtée", "pending": "Préparation de la retranscription de la réunion...", "start": "Commencer à afficher les sous-titres", "stop": "<PERSON><PERSON><PERSON><PERSON> d'afficher les sous-titres", "tr": "TR", "sourceLanguageDesc": "La langue de la réunion est actuellement définie sur {{sourceLanguage}}", "subtitlesTitle": "Sous-titres", "sourceLanguageHere": "Vous pouvez le changer ici", "subtitlesOff": "Désactivé", "NoCaptionsAvailable": "Aucun sous-titre disponible", "Translate": "<PERSON><PERSON><PERSON><PERSON>", "LiveCaptions": "Sous-titres en direct", "Transcriptions": "Transcriptions", "TranslateTo": "Traduire en", "transcriptionQuotaExceeded": "Transkripsjekwota foar dizze moanne oerskreaun", "transcriptionQuotaExceededTitle": "Transkripsjekwota o<PERSON>kreaun"}, "userMedia": {"androidGrantPermissions": "Sélect<PERSON>ner <b><i><PERSON><PERSON><PERSON></i></b> lorsque votre navigateur demande des autorisations.", "chromeGrantPermissions": "Sélect<PERSON>ner <b><i><PERSON><PERSON><PERSON></i></b> lorsque votre navigateur demande des autorisations.", "edgeGrantPermissions": "S<PERSON><PERSON><PERSON><PERSON> <b><i><PERSON><PERSON></i></b> lorsque votre navigateur demande des autorisations.", "electronGrantPermissions": "Veuillez accorder l'autorisation d'utiliser votre caméra et votre microphone", "firefoxGrantPermissions": "Sélectionner <b><i>Partager l'appareil sélectionné</i></b> lorsque votre navigateur demande des autorisations.", "iexplorerGrantPermissions": "Sélectionner <b><i>D'ACCORD</i></b> lorsque votre navigateur demande des autorisations.", "nwjsGrantPermissions": "Veuillez accorder l'autorisation d'utiliser votre caméra et votre microphone", "operaGrantPermissions": "Sélect<PERSON>ner <b><i><PERSON><PERSON><PERSON></i></b> lorsque votre navigateur demande des autorisations.", "react-nativeGrantPermissions": "Sélect<PERSON>ner <b><i><PERSON><PERSON><PERSON></i></b> lorsque votre navigateur demande des autorisations.", "safariGrantPermissions": "Sélectionner <b><i>D'ACCORD</i></b> lorsque votre navigateur demande des autorisations."}, "videoSIPGW": {"busy": "Nous travaillons à libérer des ressources. ", "busyTitle": "Le service en chambre est actuellement occupé", "errorAlreadyInvited": "{{displayName}} d<PERSON><PERSON><PERSON> invité", "errorInvite": "Conférence pas encore établie. ", "errorInviteFailed": "Nous travaillons à résoudre le problème. ", "errorInviteFailedTitle": "Attrayant {{displayName}} <PERSON><PERSON><PERSON>", "errorInviteTitle": "Erreur lors de l'invitation à une salle", "pending": "{{displayName}} a <PERSON><PERSON> invité"}, "videoStatus": {"audioOnly": "EUR", "audioOnlyExpanded": "Vous êtes en mode faible bande passante. ", "callQuality": "Qualité vidéo", "hd": "HD", "hdTooltip": "Visionner une vidéo haute définition", "highDefinition": "Haute définition", "labelTooiltipNoVideo": "Pas de vidéo", "labelTooltipAudioOnly": "Mode faible bande passante activé", "ld": "LD", "ldTooltip": "<PERSON><PERSON> une vidéo basse définition", "lowDefinition": "Basse définition", "onlyAudioAvailable": "Seul l'audio est disponible", "onlyAudioSupported": "Nous ne prenons en charge que l'audio dans ce navigateur.", "sd": "SD", "sdTooltip": "Visualisation d'une vidéo en définition standard", "standardDefinition": "Définition standard", "uhd": "UHD", "uhdTooltip": "Visionnage de vidéos ultra haute définition", "uhighDefinition": "Ultra haute définition"}, "videothumbnail": {"connectionInfo": "Informations de connexion", "domute": "<PERSON><PERSON>", "domuteOthers": "Couper le son de tous les autres", "domuteVideo": "Désactiver la caméra", "domuteVideoOfOthers": "Désactiver la caméra de tout le monde", "flip": "<PERSON><PERSON><PERSON>", "grantModerator": "Modérateur de subventions", "kick": "Supprimer un utilisateur", "moderator": "Mod<PERSON><PERSON>ur", "mute": "Le participant est en sourdine", "muted": "En sourdine", "remoteControl": "<PERSON><PERSON><PERSON><PERSON> / <PERSON><PERSON><PERSON><PERSON>élécommand<PERSON>", "show": "Spectacle sur scène", "videomute": "Le participant a arrêté la caméra", "videoMuted": "Caméra désactivée"}, "virtualBackground": {"addBackground": "Ajouter un arrière-plan", "appliedCustomImageTitle": "Image personnalisée téléchargée", "apply": "Appliquer", "blur": "Se brouiller", "customImg": "Image personnalisée", "deleteImage": "Supprimer l'image", "desktopShare": "Partage de bureau", "desktopShareError": "Impossible de créer un partage de bureau", "enableBlur": "<PERSON><PERSON> le flou", "image1": "Plage", "image2": "<PERSON>r blanc neutre", "image3": "<PERSON><PERSON>ce vide blanche", "image4": "Lampadaire noir", "image5": "<PERSON><PERSON><PERSON>", "image6": "<PERSON><PERSON><PERSON> ", "image7": "Lever du soleil", "none": "Aucun", "pleaseWait": "<PERSON>'il vous plaît, attendez...", "removeBackground": "Supprimer l'arrière-plan", "slightBlur": "Léger flou", "switchBackgroundTitle": "Changer d'arrière-plan", "title": "Arrière-plans virtuels", "uploadedImage": "Image téléchargée {{index}}", "virtualImagesTitle": "Images virtuelles intégrées", "webAssemblyWarning": "WebAssembly non pris en charge"}, "voicecommand": {"activePIPLabel": "PIP actif", "clickOnMic": "Cliquez sur la commande Mic et Voice a", "hints": {"closeLivePad": "Fermer LivePad", "closeWhiteboard": "<PERSON><PERSON><PERSON> le tableau blanc", "closeYoutube": "<PERSON><PERSON><PERSON>", "hints": "Conseils", "invitePeople": "Inviter des personnes", "lowerHand": "Baisser la main", "openChatBox": "<PERSON><PERSON><PERSON><PERSON><PERSON> la boîte de <PERSON>", "openClickAndPledge": "<PERSON><PERSON><PERSON><PERSON>, cliquez et engagez-vous", "openDonorbox": "<PERSON><PERSON><PERSON><PERSON><PERSON> la boîte des donateurs", "openFullScreen": "Ou<PERSON><PERSON>r en plein écran", "openLivePad": "Ouvrir LivePad", "openLivestream": "Ouv<PERSON>r la diffusion en direct", "openParticipantPane": "<PERSON>u<PERSON><PERSON>r le volet Participant", "openRecording": "Ouvrir l'enregistrement", "openSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON> les paramètres", "openSpeakerStats": "Statistiques des enceintes ouvertes", "openVideoQualityDialog": "<PERSON><PERSON><PERSON><PERSON><PERSON> la boîte de dialogue de qualité vidéo", "openVirtualBackground": "<PERSON><PERSON><PERSON><PERSON><PERSON> l'arrière-plan virtuel", "openWhiteboard": "<PERSON>au blanc ouvert", "openYoutube": "Ouvrir YouTube", "raiseHand": "Lever la main", "removeClickAndPledge": "Supp<PERSON>er le clic et l'engagement", "removeDonorbox": "Supprimer la boîte de donneurs", "startScreenSharing": "<PERSON><PERSON><PERSON><PERSON> le partage d'écran", "StopScreenSharing": "<PERSON><PERSON><PERSON><PERSON> le partage d'écran"}, "inActivePIPLabel": "En PIP actif", "pleaseWaitWeAreRecording": "S'il vous plaît, attendez, nous enregistrons", "vcLabel": "Commande vocale", "voiceCommandForMeethour": "Commande vocale pour l'heure de rencontre"}, "volumeSlider": "Curseur de volume", "welcomepage": {"accessibilityLabel": {"join": "Appuyez pour rejoindre", "roomname": "Entrez l'ID de la réunion"}, "addMeetingName": "Ajouter le nom de la réunion", "appDescription": "Allez-y, discutez en vidéo avec toute l’équipe.  {{app}} est une solution de visioconférence entièrement cryptée et 100 % open source que vous pouvez utiliser toute la journée, tous les jours, gratuitement, sans avoir besoin de compte.", "audioVideoSwitch": {"audio": "Voix", "video": "Vidéo"}, "calendar": "<PERSON><PERSON><PERSON>", "connectCalendarButton": "Connectez votre calendrier", "connectCalendarText": "Connectez votre calendrier pour afficher toutes vos réunions dans {{app}}.  {{provider}} réunions à votre calendrier et démarrez-les en un seul clic.", "developerPlan": "Forfait développeur", "enterprisePlan": "Forfait Entreprise", "enterpriseSelfHostPlan": "Plan d'auto-hébergement d'entreprise", "enterRoomTitle": "Démarrer une nouvelle réunion ou saisir le nom de la salle existante", "features": "Caractéristiques", "footer": {"allRightsReserved": "Tous droits réservés", "androidAppDownload": "Téléchargement de l'application Android", "apiDocumentation": "Documentation API", "app": "Application", "blog": "Blogue", "company": "Entreprise", "contact": "Contact", "copyright": "<PERSON><PERSON> d'auteur", "copyrightText": "Copyright 2020 - 2024 Meet Hour LLC. ", "developers": "Développeurs", "disclaimer": "Clause de non-responsabilité", "download": "Télécharger", "email": "E-mail", "faqs": "FAQ", "followUs": "Suivez-nous", "helpDesk": "Service d'assistance", "home": "<PERSON><PERSON>", "integrations": "Intégrations", "inTheNews": "Dans l'actualité", "iOSAppDownload": "Téléchargement de l'application iOS", "knowledgeBase": "Base de connaissances", "meethour": "Heure de rencontre", "meethourLLC": "Meet Hour LLC", "officeAddress": "8825 Stanford, bureau 205 Columbia, MD 21045", "phone": "Téléphone", "privacyPolicy": "politique de confidentialité", "productPresentation": "Présentation du produit", "refundCancellationPolicy": "Politique de remboursement et d'annulation", "termsConditions": "Conditions générales", "testimonials": "Témoignages", "webMobileSDK": "SDK Web et mobile", "whoAreYou": "Qui es-tu"}, "forHospitals": "Pour les hôpitaux", "freeBannerDescription": "Vidéoconférence de qualité HD gratuite et illimitée comme jamais auparavant. ", "freePlan": "Forfait gratuit", "getHelp": "FAQ", "go": "<PERSON><PERSON><PERSON> ou rejoindre une réunion", "goSmall": "<PERSON><PERSON><PERSON> ou rejoindre une réunion", "header": {"accelerateDevelopmentWithPrebuiltSDKs": "Accélérez le développement avec des SDK prédéfinis.", "apiStatus": "Statut de l'API", "appointmentSchedulingVideoConference": "Prise de rendez-vous et vidéoconférence.", "blog": "Blogue", "customIntegrationDedicatedSupport": "Intégration personnalisée et support dédié", "customTailoredVideoMeetings": "Réunions vidéo sur mesure.", "developer": "Promoteur", "developers": "Développeurs", "documentation": "Documentation", "edTech": "Technologie éducative", "eMail": "E-mail", "engagingOnlineLearningForEducators": "Engager l'apprentissage en ligne pour les éducateurs.", "engagingVirtualEventExperiences": "Expériences d'événements virtuels engageantes.", "enterprise": "Entreprise", "enterpriseSelfHost": "Entreprise auto-hébergée", "features": "Caractéristiques", "fitness": "Aptitude", "free": "<PERSON><PERSON><PERSON>", "fundraiseEffortlesslyWithinVideoConferences": "Collectez des fonds sans effort via des vidéoconférences.", "fundraisingDonate": "Collecte de fonds / Faire un don", "fundraisingDonateOnline": "Collecte de fonds / Faire un don en ligne", "getStarted": "Commencer", "hdQualityVideoConferenceApp": "Application de vidéoconférence de qualité HD", "help": "Aide", "helpDesk": "Service d'assistance", "highQualityLiveEventStreaming": "Diffusion d'événements en direct de haute qualité.", "hostVideoConferenceOnYourServers": "H<PERSON><PERSON>ez une vidéoconférence sur vos serveurs.", "industries": "Secteurs", "integrateVideoCallWithinYourWebsiteApp": "Intégrez l'appel vidéo à votre site Web/application.", "interactiveVirtualLearningSolutions": "Solutions d'apprentissage virtuel interactif.", "joinAMeeting": "Rejoindre une réunion", "knowledgeBase": "Base de connaissances", "liveStreaming": "Diffusion en direct", "meethour": "Heure de rencontre", "mycaly": "MonCaly", "myCaly": "Mon Caly", "myCalyPricing": "<PERSON><PERSON><PERSON>.", "noAdsRecordingLiveStreaming": "Pas de publicité + enregistrement + diffusion en direct.", "noTimeLimitGroupCalls": "Aucune limite de temps pour les appels 1:1 et de groupe.", "preBuiltSDKs": "SDK pré-construits", "pricing": "<PERSON><PERSON><PERSON>", "pro": "Pro", "products": "Produits", "resources": "Ressources", "scheduleADemo": "Planifier une démo", "simplifiedAPIReferences": "Références API simplifiées", "smoothVideoOnboardingExperience": "Expérience d'intégration vidéo fluide.", "solutions": "Solutions", "stayuptodateWithOurBlog": "Restez à jour avec notre blog", "systemHealthStatusandUpdates": "État de santé du système et mises à jour", "tailoredSolutionsForYourHealthcareNeeds": "Des solutions sur mesure pour vos besoins en matière de soins de santé.", "telehealth": "Télésanté", "useCases": "Cas d'utilisation", "videoConference": "Vidéoconférence", "videoConferencePlans": "Forfaits de vidéoconférence", "videoConferencePricing": "Tarifs visioconférence.", "videoConferencing": "Vidéoconférence", "videoKYC": "Vidéo KYC", "virtualClassrooms": "Classes virtuelles", "virtualEvents": "Événements virtuels", "virtualSolutionForHomeFitness": "Solution virtuelle pour le fitness à domicile.", "webinars": "Webinaires", "webinarSessionsWithIndustryLeaders": " Séances de webinaires avec des leaders de l'industrie."}, "headerSubtitle": "Des réunions sécurisées et de qualité HD", "headerTitle": "Heure de rencontre", "info": "Informations de connexion", "invalidMeetingID": "ID de réunion invalide", "jitsiOnMobile": "Meet Hour sur mobile : téléchargez nos applications et démarrez une réunion où que vous soyez", "join": "CRÉER / REJOINDRE", "joinAMeeting": "Rejoindre une réunion", "logo": {"calendar": "Logo du calendrier", "desktopPreviewThumbnail": "Miniature d'aperçu du bureau", "googleLogo": "Logo Google", "logoDeepLinking": "Logo de rencontre Jitsi", "microsoftLogo": "Logo Microsoft", "policyLogo": "Logo de la politique"}, "meetingDate": "Date de la réunion", "meetingDetails": "Détails de la réunion", "meetingIsReady": "La réunion est prête", "mobileDownLoadLinkAndroid": "Téléchargez l'application mobile pour Android", "mobileDownLoadLinkFDroid": "Téléchargez l'application mobile pour F-Droid", "mobileDownLoadLinkIos": "Téléchargez l'application mobile pour iOS", "moderatedMessage": "Ou <a href=\"{{url}}\" rel=\"noopener noreferrer\" target=\"_blank\">réserver une URL de réunion</a> à l'avance où vous êtes le seul modérateur.", "oopsDeviceClockorTimezoneErr": "Oups ! ", "oopsThereSeemsToBeProblem": "Oups, il semble y avoir un problème", "pleaseWaitForTheStartMeeting": "Veuillez attendre le {{moderator}} pour commencer cette réunion.", "preRegistrationMsg": "Cette réunion nécessite une pré-inscription. ", "pricing": "<PERSON><PERSON><PERSON>", "privacy": "Confidentialité", "privateMeetingErr": "Vous semblez rejoindre une réunion privée. ", "proPlan": "Forfait Pro", "recentList": "<PERSON><PERSON><PERSON>", "recentListDelete": "Supprimer l'entrée", "recentListEmpty": "Votre liste récente est actuellement vide. ", "reducedUIText": "Bienvenue à {{app}}!", "registerNow": "Ins<PERSON>rivez-vous maintenant", "roomname": "Entrez l'ID de la réunion", "roomNameAllowedChars": "Le nom de la réunion ne doit contenir aucun des caractères suivants : ?, &, :, ', \", %, #.", "roomnameHint": "Saisis<PERSON>z le nom ou l'URL de la salle que vous souhaitez rejoindre. ", "scheduleAMeeting": "Planifier une réunion", "sendFeedback": "Envoyer des commentaires", "shiftingVirtualMeetToReality": "Passer de la rencontre virtuelle à la réalité", "solutions": "Solutions", "startMeeting": "Commencer la réunion", "terms": "Termes", "timezone": "<PERSON><PERSON> ho<PERSON>", "title": "Solution de visioconférence 100 % gratuite, illimitée, cryptée de bout en bout et de qualité HD", "tryNowItsFree": "Essayez maintenant, c'est gratuit", "waitingInLobby": "En attente dans le hall. {{moderator}} je vous laisserai bient<PERSON>t entrer.", "youtubeHelpTutorial": "Tu<PERSON><PERSON><PERSON> d'aide YouTube"}}