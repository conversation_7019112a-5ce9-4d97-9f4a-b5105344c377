import json
import os
import re

# Load the original English JSON data
with open('main.json', 'r', encoding='utf-8') as file:
    original_data = json.load(file)

# Directory containing the translated JSON files
translations_dir = '.'  # Assuming the translated files are in the current directory

# Function to find and replace placeholders in a string
def replace_placeholders(translated_string, original_string):
    # Find all placeholders in the original string ({{...}} and $t(...))
    original_placeholders = re.findall(r'\{\{.*?\}\}|\$t\(.*?\)', original_string)
    translated_placeholders = re.findall(r'\{\{.*?\}\}|\$t\(.*?\)', translated_string)

    # If the number of placeholders doesn't match, we can't safely replace
    if len(original_placeholders) != len(translated_placeholders):
        print(f"Warning: Placeholder count mismatch for string: '{original_string}'")
        print(f"Translated string: '{translated_string}'")
        return translated_string # Return the original translated string

    # Create a mapping from translated placeholders to original placeholders
    placeholder_mapping = dict(zip(translated_placeholders, original_placeholders))

    # Replace the translated placeholders with the original ones
    for translated_ph, original_ph in placeholder_mapping.items():
        translated_string = translated_string.replace(translated_ph, original_ph)

    return translated_string

# Recursive function to process the translated data
def process_translated_data(translated_data, original_data):
    if isinstance(translated_data, dict) and isinstance(original_data, dict):
        for key in translated_data.keys():
            if key in original_data:
                translated_data[key] = process_translated_data(translated_data[key], original_data[key])
    elif isinstance(translated_data, list) and isinstance(original_data, list):
        # We assume lists in the translated files have the same structure as the original
        for i in range(min(len(translated_data), len(original_data))):
             translated_data[i] = process_translated_data(translated_data[i], original_data[i])
    elif isinstance(translated_data, str) and isinstance(original_data, str):
        # Process strings to replace placeholders
        return replace_placeholders(translated_data, original_data)

    return translated_data

# Iterate through the translated language files
for filename in os.listdir(translations_dir):
    if filename.startswith('main-') and filename.endswith('.json') and filename != 'main.json':
        filepath = os.path.join(translations_dir, filename)
        print(f"Processing {filename}...")

        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                translated_data = json.load(f)

            # Process the translated data to replace placeholders
            processed_translated_data = process_translated_data(translated_data, original_data)

            # Save the updated data back to the file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(processed_translated_data, f, ensure_ascii=False, indent=4)

            print(f"Finished processing {filename}")

        except Exception as e:
            print(f"Error processing {filename}: {e}")

print("Placeholder replacement completed for all translated files.")