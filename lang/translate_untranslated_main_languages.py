import json
import os
from deep_translator import GoogleTranslator  # type: ignore

def find_untranslated_values(original_data, translated_data):
    """
    Recursively finds values in translated_data that are identical to
    their corresponding values in original_data (indicating they weren't translated).
    Returns a dictionary with the path to the untranslated value as the key
    and the original value as the value.
    """
    untranslated = {}

    def _find_untranslated_recursive(original, translated, path=""):
        if isinstance(original, dict) and isinstance(translated, dict):
            for key in original:
                if key in translated:
                    _find_untranslated_recursive(original[key], translated[key], f"{path}.{key}" if path else key)
        elif isinstance(original, list) and isinstance(translated, list):
            for i in range(min(len(original), len(translated))):
                _find_untranslated_recursive(original[i], translated[i], f"{path}[{i}]")
        elif isinstance(original, str) and isinstance(translated, str):
            if original == translated:
                untranslated[path] = original
        # Note: We don't handle cases where types mismatch or keys/indices are missing
        # as the goal is to find values that *should* have been translated but weren't.

    _find_untranslated_recursive(original_data, translated_data)
    return untranslated

def update_nested_value(data, path, new_value):
    """
    Updates a value in a nested dictionary or list based on a path string.
    Path examples: "key1.key2", "list[0].key", "list[1]"
    """
    keys = path.split('.')
    current_data = data
    for i, key in enumerate(keys):
        if '[' in key and ']' in key:
            # Handle list indices
            list_key, index_str = key.split('[')
            index = int(index_str.replace(']', ''))
            if i == len(keys) - 1:
                current_data[list_key][index] = new_value
            else:
                current_data = current_data[list_key][index]
        else:
            # Handle dictionary keys
            if i == len(keys) - 1:
                current_data[key] = new_value
            else:
                current_data = current_data[key]

# Load the original English JSON data
with open('main.json', 'r', encoding='utf-8') as file:
    original_words_data = json.load(file)

# Directory containing translated JSON files
translations_dir = "."  # Assuming the translated files are in the same directory

# Get a list of all main-*.json files (excluding main.json)
translated_files = [f for f in os.listdir(translations_dir) if f.startswith('main-') and f.endswith('.json') and f != 'main.json']

print(f"Found {len(translated_files)} translated language files to check.")

for translated_filename in translated_files:
    lang_code = translated_filename.replace('main-', '').replace('.json', '')
    translated_filepath = os.path.join(translations_dir, translated_filename)

    try:
        with open(translated_filepath, 'r', encoding='utf-8') as file:
            translated_data = json.load(file)

        print(f"\nChecking '{translated_filename}' for untranslated values...")

        untranslated_values = find_untranslated_values(original_words_data, translated_data)

        if not untranslated_values:
            print(f"No untranslated values found in '{translated_filename}'.")
            continue

        print(f"Found {len(untranslated_values)} untranslated values in '{translated_filename}'. Attempting to translate...")

        translator = GoogleTranslator(source='en', target=lang_code)
        translated_count = 0

        for path, original_value in untranslated_values.items():
            try:
                translated_value = translator.translate(original_value)
                update_nested_value(translated_data, path, translated_value)
                translated_count += 1
                print(f"  Translated '{original_value}' (at path '{path}') to '{translated_value}'")
            except Exception as translate_error:
                print(f"  Error translating '{original_value}' (at path '{path}'): {translate_error}")

        if translated_count > 0:
            # Save the updated translated data back to the file
            with open(translated_filepath, "w", encoding="utf-8") as file:
                json.dump(translated_data, file, ensure_ascii=False, indent=4)
            print(f"Updated '{translated_filename}' with {translated_count} new translations.")
        else:
            print(f"Could not translate any of the previously untranslated values in '{translated_filename}'.")

    except FileNotFoundError:
        print(f"Error: Translated file '{translated_filepath}' not found.")
    except json.JSONDecodeError:
        print(f"Error: Could not parse JSON from '{translated_filepath}'.")
    except Exception as e:
        print(f"An unexpected error occurred while processing '{translated_filename}': {e}")

print("\nProcess completed.")