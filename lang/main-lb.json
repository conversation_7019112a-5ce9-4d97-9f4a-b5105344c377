{"addPeople": {"add": "Invi<PERSON>ier<PERSON>", "addContacts": "Invitéiert Är Kontakter", "contacts": "<PERSON><PERSON><PERSON><PERSON>", "copyInvite": "<PERSON><PERSON>", "copyLink": "Ko<PERSON><PERSON><PERSON>t de Versammlungslink", "copyStream": "Kopéiert Live Streaming Link", "countryNotSupported": "Mir <PERSON>nnerstëtzen dës Destinatioun nach net.", "countryReminder": "R<PERSON>t ausserhalb vun den USA? Gitt s<PERSON>cher datt Dir mam Landcode ufänkt!", "defaultEmail": "Är Standard E-Mail", "disabled": "Dir kënnt d'Leit net invitéieren.", "doYouWantToRemoveThisPerson": "<PERSON><PERSON><PERSON> dë<PERSON>", "failedToAdd": "Ausgefall Participanten dobäizemaachen", "footerText": "Ausruffen ass behënnert.", "googleCalendar": "Google Kalenner", "googleEmail": "Google E-Mail", "inviteMoreHeader": "Dir sidd deen eenzegen an der Versammlung", "inviteMoreMailSubject": "Ma<PERSON>t mat bei {{appName}} Versammlung", "inviteMorePrompt": "Invi<PERSON>iert méi <PERSON>", "linkCopied": "Link op Clipboard kopéiert", "loading": "Sich no Leit an Telefonsnummeren", "loadingNumber": "Validéieren Telefonsnummer", "loadingPeople": "Sich no Leit ze invitéieren", "loadingText": "Lueden...", "noResults": "Keng passende Sichresultater", "noValidNumbers": "Gitt weg eng Telefonsnummer", "outlookEmail": "Outlook E-Mail", "phoneNumbers": "Telefonsnummer", "searchNumbers": "Dobäizemaachen Telefonsnummer", "searchPeople": "<PERSON>ch no Leit", "searchPeopleAndNumbers": "Sich no Leit oder füügt hir Telefonsnummeren derbäi", "searching": "Sichen ...", "sendWhatsa[pp": "Whatsapp", "shareInvite": "<PERSON><PERSON>", "shareInviteP": "Deelt Versammlungsinvitatioun mat <PERSON>", "shareLink": "Deelt de Versammlungslink fir anerer ze invitéieren", "shareStream": "Deelt de Live Streaming Link", "sipAddresses": "<PERSON><PERSON><PERSON><PERSON>", "telephone": "Telefon: {{number}}", "title": "Invitéiert d'Leit op dës Versammlung", "yahooEmail": "Yahoo E-Mail"}, "audioDevices": {"bluetooth": "Bluetooth", "headphones": "<PERSON><PERSON>h<PERSON><PERSON>", "none": "Keng Audio Geräter verfügbar", "phone": "Telefon", "speaker": "<PERSON><PERSON><PERSON><PERSON>"}, "audioOnly": {"audioOnly": "Niddereg Bandbreedung"}, "breakoutRooms": {"actions": {"add": "Füüge Breakout-Raum (Beta) derbäi", "autoAssign": "Automatesch dem Breakout-<PERSON><PERSON>", "close": "Schléissen", "join": "Bäitrieden", "leaveBreakoutRoom": "Verléisst Breakout-Raum", "more": "<PERSON><PERSON><PERSON>", "remove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rename": "Ëmschreiwen", "renameBreakoutRoom": "Breakout-<PERSON><PERSON>", "sendToBreakoutRoom": "<PERSON><PERSON><PERSON><PERSON><PERSON> de Participant op:"}, "breakoutList": "Breakout-<PERSON><PERSON><PERSON><PERSON>", "buttonLabel": "Breakout-Raimen", "defaultName": "Breakout-Raum #{{index}}", "hideParticipantList": "Participant-Lëscht verstoppen", "mainRoom": "<PERSON>ap<PERSON>-<PERSON><PERSON>", "notifications": {"joined": "<PERSON> \"{{name}}\" Breakout-<PERSON><PERSON>", "joinedMainRoom": "<PERSON>-<PERSON><PERSON> b<PERSON>en", "joinedTitle": "Breakout-Raimen"}, "showParticipantList": "Participant-Lëscht weisen", "showParticipants": "Participanten weisen", "title": "Breakout-Raimen"}, "calendarSync": {"addMeetingURL": "Füügt e Versammlungslink", "confirmAddLink": "W<PERSON>lt Dir e Liicht méi Zäit treffen Link zu dësem Event addéieren?", "error": {"appConfiguration": "Kalennerintegratioun ass net richteg konfiguréiert.", "generic": "<PERSON> Feeler ass geschitt. Kuckt w.e.g. Är Kalennerastellungen oder probéiert de Kalenner z'erfrëschen.", "notSignedIn": "E Feeler ass geschitt beim Authentifikatioun fir Kalennerevenementer ze gesinn. Kontrolléiert w.e.g. Är Kalennerastellungen a probéiert nach eng Kéier aloggen."}, "join": "<PERSON><PERSON>t mat", "joinTooltip": "Maacht mat bei der Versammlung", "nextMeeting": "nächst Versammlung", "noEvents": "Et gi keng zukünfteg Eventer geplangt.", "ongoingMeeting": "lafend Versammlung", "permissionButton": "Open Astellungen", "permissionMessage": "De Kalenner Erlaabnis ass erfuerderlech fir Är Reuniounen an der App ze gesinn.", "refresh": "Ka<PERSON>ner opfrëschen", "today": "<PERSON><PERSON>"}, "carmode": {"actions": {"selectSoundDevice": "Wielt Sound Apparat"}, "labels": {"buttonLabel": "Auto Modus", "title": "Auto Modus", "videoStopped": "Äre Video gëtt gestoppt"}}, "chat": {"enter": "G<PERSON> an de Cha<PERSON>um", "error": "Feeler: Äre Message gouf net geschéckt. Grond: {{error}}", "fieldPlaceHolder": "Gidd Äre Message hei", "message": "Message", "messageAccessibleTitle": "{{user}} seet:", "messageAccessibleTitleMe": "ech seet:", "messageTo": "Privat Noriicht un {{recipient}}", "messagebox": "Gidd e Message", "nickname": {"popover": "Wielt e Spëtznumm", "title": "Gitt e Spëtznumm fir Chat ze benotzen"}, "noMessagesMessage": "Et gi keng Messagen an der Versammlung nach. Start e Gespréich hei!", "privateNotice": "Privat Noriicht un {{recipient}}", "smileysPanel": "Emoji Panel", "tabs": {"chat": "Chats", "polls": "Ëmfroen"}, "title": "Chat an Ëmfroen", "titleWithPolls": "Chat an Ëmfroen", "you": "dech"}, "chromeExtensionBanner": {"buttonText": "Installéiert Chrome Extensioun", "close": "Zoumaachen", "dontShowAgain": "<PERSON>st mir dëst net erëm", "installExtensionText": "Installéiert d'Extensioun fir Google Kalenner an Office 365 Integratioun"}, "clickandpledge": {"errorDesc": "Gitt w.e.g. g<PERSON>teg <PERSON>lick and Pledge Connect GUID an. Fir méi Detailer besicht - https://connect.clickandpledge.com/", "errorNotification": "Ongëlteg Klick a Versprieche Guid", "title": "C&P Connect Spendenastellungen", "titlenative": "<PERSON><PERSON><PERSON> a <PERSON>ledge"}, "connectingOverlay": {"joiningRoom": "Verbënnt Iech mat Ärer Versammlung ..."}, "connection": {"ATTACHED": "Befestegt", "AUTHENTICATING": "Authentifikat<PERSON>un", "AUTHFAIL": "Authentifikat<PERSON>un huet gesche<PERSON>t", "CONNECTED": "Ugeschloss", "CONNECTING": "Verbannen", "CONNFAIL": "Verbindung gescheitert", "DISCONNECTED": "Deconnect<PERSON><PERSON><PERSON>", "DISCONNECTING": "<PERSON><PERSON><PERSON>", "ERROR": "Feeler", "FETCH_SESSION_ID": "Sessiouns-ID kréien ...", "GET_SESSION_ID_ERROR": "<PERSON><PERSON>-<PERSON> Feeler: {{code}}", "GOT_SESSION_ID": "Sessiouns-ID kréien ... fäerdeg", "LOW_BANDWIDTH": "De Video fir {{displayName}} gouf ausgeschalt fir Bandbreedung ze spueren"}, "connectionindicator": {"address": "Adress:", "audio_ssrc": "Audio SSRC:", "bandwidth": "Geschätzte Bandbreedung:", "bitrate": "Bitrate:", "bridgeCount": "Server Zuel:", "codecs": "Codecs (A/V):", "connectedTo": "Verbonne mat:", "e2e_rtt": "E2e RTT:", "framerate": "Frame <PERSON>:", "less": "<PERSON><PERSON> manner", "localaddress": "Lokal Adress:", "localaddress_plural": "Lokal Adressen:", "localport": "Lokal Hafen:", "localport_plural": "Lokal Häfen:", "maxEnabledResolution": "sch<PERSON>cken max", "more": "<PERSON><PERSON><PERSON> weisen", "packetloss": "Package Verloscht:", "participant_id": "Participant ID:", "quality": {"good": "<PERSON><PERSON>", "inactive": "Inaktiv", "lost": "Verluer", "nonoptimal": "Net optimal", "poor": "A<PERSON>séileg"}, "remoteaddress": "Remote Adress:", "remoteaddress_plural": "Re<PERSON>:", "remoteport": "Fernport:", "remoteport_plural": "Remote Ports:", "resolution": "Resolutioun:", "savelogs": "<PERSON>g<PERSON><PERSON> s<PERSON>", "status": "Verbindung:", "transport": "Transport:", "transport_plural": "Transporter:", "video_ssrc": "Video SSRC:"}, "dateUtils": {"earlier": "<PERSON><PERSON><PERSON>", "today": "<PERSON><PERSON>", "yesterday": "G<PERSON><PERSON><PERSON>"}, "deepLinking": {"appNotInstalled": "Benotzt eis {{app}} mobil App fir bei dëser Versammlung op Ärem Telefon matzemaachen.", "continueWithBrowser": "Fuert weider mam Browser", "description": "Näischt geschitt? Mir hu probéiert Är Versammlung an der {{app}} Desktop-App ze starten. Probéiert nach eng Kéier oder lancéiert se an der {{app}} Webapp.", "descriptionWithoutWeb": "Näischt geschitt? Mir hu probéiert Är Versammlung an der {{app}} Desktop-App ze starten.", "downloadApp": "<PERSON><PERSON> d'<PERSON> erof", "ifDoNotHaveApp": "<PERSON><PERSON>'<PERSON> nach net hutt:", "ifHaveApp": "<PERSON><PERSON> scho <PERSON>t:", "ifYouDontHaveTheAppYet": "<PERSON><PERSON>'<PERSON> nach net hutt", "joinInApp": "Maacht mat bei dëser Versammlung mat der App", "joinMeetingWithDesktopApp": "Maacht mat bei Versammlung mat Desktop App", "launchMeetingInDesktopApp": "Start Meeting an Desktop App", "launchWebButton": "Start am Web", "title": "Start Är Versammlung an {{app}}...", "tryAgainButton": "Probéiert nach eng Kéier am Desktop"}, "defaultLink": "z.<PERSON><PERSON> {{url}}", "defaultNickname": "ex. <PERSON> rosa", "deviceError": {"cameraError": "Ausgefall op Är Kamera", "cameraPermission": "<PERSON>er be<PERSON> vun der Kamera E<PERSON>", "microphoneError": "Ausgefall op Äre Mikro", "microphonePermission": "Feeler beim <PERSON> vun der Mikrofonerlaabnis"}, "deviceSelection": {"noPermission": "Erlaabnis net gëtt", "previewUnavailable": "Virschau net verfügbar", "selectADevice": "Wielt en Apparat", "testAudio": "Spillt en Test Sound"}, "dialOut": {"statusMessage": "ass elo {{status}}"}, "dialog": {"Back": "<PERSON><PERSON><PERSON>", "Cancel": "Ofbriechen", "IamHost": "<PERSON><PERSON> sinn den <PERSON>", "Ok": "OK", "Remove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Share": "<PERSON><PERSON>", "Submit": "Ofginn", "WaitForHostMsg": "<PERSON>'Konferenz <b>{{room}}</b> ass nach net ugefaang. <PERSON><PERSON><PERSON>, da authentifizéiere w.e.g. <PERSON>, waart w.e.g. bis den Host ukomm ass.", "WaitForHostMsgWOk": "D'Konferenz <b>{{room}}</b> ass nach net ugefaang. <PERSON><PERSON><PERSON> sidd, dréckt w.e.g. Ok fir ze authentifizéieren. Soss, waart w.e.g. bis den Host ukomm ass.", "WaitforModerator": "Waart w.e.g. bis de Moderator ukomm ass", "WaitforModeratorOk": "<PERSON><PERSON><PERSON>", "WaitingForHost": "Waart op den Host ...", "WaitingForHostTitle": "Waart op den Host ...", "Yes": "<PERSON>", "accessibilityLabel": {"liveStreaming": "Live Stream"}, "add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "allow": "<PERSON><PERSON><PERSON><PERSON>", "alreadySharedVideoMsg": "En anere Participant de<PERSON>t schonn e Video. Dës Konferenz er<PERSON>abt nëmmen ee gedeelt Video g<PERSON>äichzäiteg.", "alreadySharedVideoTitle": "Nëmmen ee gede<PERSON>t Video ass gläichzäiteg er<PERSON>t", "applicationWindow": "Applikatioun Fënster", "authenticationRequired": "Authentifikatioun n<PERSON>g", "cameraConstraintFailedError": "Är Kamera erfëllt net e puer vun den erfuerderlechen Aschränkungen.", "cameraNotFoundError": "Ka<PERSON>a gouf net fonnt.", "cameraNotSendingData": "Mir kënnen net op Är Kamera zougräifen. Kuckt w.e.g. ob eng aner Applikatioun dësen Apparat benotzt, wielt en aneren Apparat aus dem Astellungsmenü oder probéiert d'Applikatioun nei ze lueden.", "cameraNotSendingDataTitle": "Kann net op d'Kamera kommen", "cameraPermissionDeniedError": "Dir hutt keng Erlaabnis fir Är Kamera ze benotzen. Dir kënnt nach ëmmer op der Konferenz matmaachen, awer anerer gesinn dech net. Benotzt de Kamera Knäppchen an der Adressbar fir dëst ze fixéieren.", "cameraTimeoutError": "Konnt d'Videoquell net starten. Timeout ass geschitt!", "cameraUnknownError": "<PERSON>nn d'Kamera net aus engem onbekannte Grond benotzen.", "cameraUnsupportedResolutionError": "Är Kamera ënnerstëtzt net erfuerderlech Videoopléisung.", "cannotToggleScreenSharingNotSupported": "Kann net Écran Deele wiesselen: net ënnerstëtzt.", "close": "Zoumaachen", "closingAllTerminals": "All Terminals zoumaachen", "conferenceDisconnectMsg": "Dir wëllt vläicht Är Netzwierkverbindung kontrolléieren. Reconnectéiert an {{seconds}} Sekonnen...", "conferenceDisconnectTitle": "<PERSON>r sidd of<PERSON><PERSON>t ginn.", "conferenceReloadMsg": "Mir probéieren dëst ze fixéieren. Reconnectéiert an {{seconds}} Sekonnen...", "conferenceReloadTitle": "<PERSON><PERSON> ass eppes falsch gaangen.", "confirm": "Confirm<PERSON><PERSON><PERSON>", "confirmNo": "<PERSON><PERSON>", "confirmYes": "<PERSON>", "connectError": "Oops! Eppes ass falsch gaang a mir konnten net mat der Konferenz konnektéieren.", "connectErrorWithMsg": "Oops! Eppes ass falsch gaang a mir konnten net mat der Konferenz verbannen: {{msg}}", "connecting": "Verbannen", "contactSupport": "Kontakt Ënnerstëtzung", "copied": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON>", "customAwsRecording": "Benotzerdefin<PERSON><PERSON><PERSON>", "deleteCache": "<PERSON><PERSON>", "dismiss": "<PERSON><PERSON><PERSON><PERSON>", "displayNameRequired": "Salut! Wéi ass däin <PERSON>?", "displayUserName": "", "donationCNPLabel": "Gitt Connect Form URL oder Widget URL", "donationCNotificationTitle": "Spenden iwwer Click and Pledge", "donationLabel": "Gitt Spende Kampagne URL", "donationNotificationDescription": "Spend eis fir eis Saach z'ënnerstëtzen", "donationNotificationTitle": "Spenden iwwer Donorbox", "done": "<PERSON><PERSON><PERSON>", "e2eeDescription": "End-to-End Verschlësselung ass momentan EXPERIMENTAL. Bedenkt w.e.g. datt d'End-zu-Enn Verschlësselung ausschalten effektiv Server-Säit geliwwert Servicer auszeschalten wéi: Opname, Live Streaming an Telefon Participatioun. Denkt och drun datt d'Versammlung nëmme funktionnéiert fir Leit déi aus Browser mat Ënnerstëtzung fir insertable Streams bäitrieden.", "e2eeLabel": "End-zu-Enn Verschlësselung aktivéieren", "e2eeWarning": "OPGEPASST: Net all Participanten un dëser Versammlung schéngen Ënnerstëtzung fir End-to-End Verschlësselung ze hunn. Wann Dir et aktivéiert, kënne se dech net gesinn an héieren.", "embedMeeting": "<PERSON><PERSON>", "enterCdonation": "Beispill: https://connect.clickandpledge.com/w/Form/283ah-0069-441a-ba0f-d42a9dec9821", "enterDisplayEmail": "E-Mail ID", "enterDisplayName": "Ganzen numm", "enterDisplayNameToJoin": "Gitt weg Ären Numm fir matzemaachen", "enterDonation": "Beispill: https://donorbox.org/donate-an-organisation", "enterMeetingId": "Gitt d'Versammlung ID", "enterMeetingPassword": "<PERSON><PERSON>", "error": "Feeler", "errorMeetingID": "Füügt Meeting ID", "errorMeetingPassword": "<PERSON><PERSON><PERSON><PERSON>asswuert do<PERSON>", "forceMuteEveryoneDialog": "Sidd Dir sécher datt Dir de Mikrofon vu jidderengem ausser Moderatore späre wëllt? Si kënnen net de Mikrofon selwer opspären, awer wann <PERSON><PERSON> d'Forcéierungsmute annuléiert, kënne se d'Muecht opmaachen a schwätzen.", "forceMuteEveryoneElseDialog": "Mute se sou wéi och hire <PERSON><PERSON>", "forceMuteEveryoneElseTitle": "Force Mute jiddereen ausser {{whom}}?", "forceMuteEveryoneElsesVideoDialog": "<PERSON><PERSON> <PERSON>'<PERSON><PERSON>a ausgeschalt ass, k<PERSON>nnen se hir Kamera net aktivéieren", "forceMuteEveryoneElsesVideoTitle": "Force Mute jidderengem seng Ka<PERSON>a ausser {{whom}}?", "forceMuteEveryoneSelf": "selwer", "forceMuteEveryoneStartMuted": "Jiddereen fänkt Kraaft Mut vun elo un", "forceMuteEveryoneTitle": "Force Mute jiddereen?", "forceMuteEveryonesVideoDialog": "Sidd Dir sécher datt Dir de Video vun dësem Participant späre wëllt? Hien / Si wäert net fäeg sinn de Video opzemaachen", "forceMuteEveryonesVideoTitle": "Force Mute jiddereen säi Video?", "forceMuteParticipantBody": "Force Mute Participant.", "forceMuteParticipantButton": "For<PERSON><PERSON><PERSON>", "forceMuteParticipantDialog": "Sidd Dir sécher datt Dir de Mikrofon vun dësem Participant späre wëllt? Hien / hatt wäert net fäeg sinn de Mikrofon ze spären, awer wann Di<PERSON> d'Forcéierungsmute annuléiert, kann hien / hatt ophalen a schwätzen.", "forceMuteParticipantTitle": "Force Mute dëse Participant?", "forceMuteParticipantsVideoBody": "D'Participanten Video gëtt ausgeschalt a si kënnen net erëm opmaachen", "forceMuteParticipantsVideoButton": "<PERSON><PERSON><PERSON>", "forceMuteParticipantsVideoTitle": "Kamera vun dësem Participant auszeschalten?", "gracefulShutdown": "Eise Service ass am Moment erof fir Ënnerhalt. Probéiert w.e.g. méi spéit nach eng Kéier.", "grantModeratorDialog": "Sidd Dir sécher datt Dir dëse Participant zu engem Moderator wëllt maachen?", "grantModeratorTitle": "Grant Moderator", "guestUser": "Gaascht Déngscht", "hangUpLeaveReason": "<PERSON><PERSON><PERSON> Versammlung gouf vum Moderator ofgeschloss", "hideShareAudioHelper": "Weist dësen Dialog net erëm", "incorrectPassword": "Falsche Benotzernumm oder Passwuert", "incorrectRoomLockPassword": "<PERSON><PERSON><PERSON>", "internalError": "Oops! Eppes ass falsch gaangen. De folgende Feeler ass geschitt: {{error}}", "internalErrorTitle": "<PERSON>ne <PERSON>", "kickMessage": "Autsch! Dir sidd aus dem Treff geläscht!", "kickParticipantButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kickParticipantDialog": "<PERSON><PERSON> <PERSON>, datt <PERSON>r dëse Participant ewech<PERSON><PERSON> wëllt?", "kickParticipantTitle": "Dëse Participant ewechhuelen?", "kickTitle": "Autsch! Dir sidd aus der Versammlung geläscht ginn", "liveStreaming": "Live Streaming", "liveStreamingDisabledBecauseOfActiveRecordingTooltip": "Net méiglech wärend den Opnahmen aktiv ass", "liveStreamingDisabledForGuestTooltip": "Gäscht kënnen net Live Streaming ufänken.", "liveStreamingDisabledTooltip": "Start Live Stream behënnert.", "localUserControls": "Lokal Benotzer Kontrollen", "lockMessage": "Ausgefall d'Konferenz ze spären.", "lockRoom": "Add Meeting $t(lockRoomPasswordUppercase)", "lockTitle": "<PERSON><PERSON><PERSON><PERSON>", "login": "Logbroon", "logoutQuestion": "Sidd Dir sécher datt Dir wëllt ausloggen an d'Konferenz stoppen?", "logoutTitle": "Ausloggen", "maxUsersLimitReached": "D'Limite fir maximal Unzuel vun de Participanten ass erreecht ginn. D'Konferenz ass voll. Kontaktéiert w.e.g. de Versammlungsbesëtzer oder probéiert méi spéit nach eng Kéier!", "maxUsersLimitReachedTitle": "Maximum Participanten Limit erreecht", "meetHourRecording": "T<PERSON>fft Hour Recording", "meetingID": "Treffen ID", "meetingIDandPassword": "Gitt d'Versammlung ID a Passwuert un", "meetingPassword": "<PERSON><PERSON><PERSON><PERSON>", "messageErrorApi": "ops! Eppes ass schief gaangen", "messageErrorInvalid": "Ongülteg Umeldungsinformatiounen", "messageErrorNotModerator": "Oops! Dir sidd kee Moderator vun dëser Versammlung", "messageErrorNull": "Benotzernumm oder Passwuert ass eidel", "micConstraintFailedError": "Äre Mikro erfëllt e puer vun den erfuerderlechen Aschränkungen net.", "micNotFoundError": "Mikrofon gouf net fonnt.", "micNotSendingData": "Gitt an d'Astellunge vun Ärem Computer fir Äre Mic z'ënnerbriechen a säin Niveau unzepassen", "micNotSendingDataTitle": "Äre Mikro ass duerch Är Systemastellunge gedempt", "micPermissionDeniedError": "Dir hutt keng Erlaabnis fir Äre Mikrofon ze benotzen. Dir kënnt nach ëmmer op der Konferenz matmaachen, awer anerer héieren Iech net. Benotzt de Kamera Knäppchen an der Adressbar fir dëst ze fixéieren.", "micTimeoutError": "Konnt d'Audioquell net starten. Timeout ass geschitt!", "micUnknownError": "<PERSON><PERSON> de Mikro aus engem onbekannte Grond net benotzen.", "muteEveryoneDialog": "Sidd Dir sécher datt Dir jidderee wëllt mute loossen? Dir kënnt se net unmute loossen, awer si kënne sech zu all Moment unmute loossen.", "muteEveryoneElseDialog": "<PERSON><PERSON><PERSON>, kënnt Dir se net unmute loossen, awer si kënne sech zu all Moment opmaachen.", "muteEveryoneElseTitle": "<PERSON><PERSON><PERSON> mutt<PERSON><PERSON>en ausser {{whom}}?", "muteEveryoneElsesVideoDialog": "<PERSON><PERSON> <PERSON>'<PERSON><PERSON><PERSON> beh<PERSON> ass, kënnt Dir se net zré<PERSON>chalten, awer si kënnen et zu all Moment zréckschalten.", "muteEveryoneElsesVideoTitle": "<PERSON><PERSON><PERSON> seng Kamera auszes<PERSON>ten ausser {{whom}}?", "muteEveryoneSelf": "selwer", "muteEveryoneStartMuted": "<PERSON>dder<PERSON> fänkt vun elo un muted un", "muteEveryoneTitle": "<PERSON><PERSON><PERSON> ausdrécken?", "muteEveryonesVideoDialog": "Sidd Dir sécher datt Dir jidderengem seng Kamera auszeschalten wëllt? Dir kënnt et net erëm op<PERSON>n, awer si kënne<PERSON> et zu all Moment zréckschalten.", "muteEveryonesVideoDialogOk": "Desaktivéieren", "muteEveryonesVideoTitle": "<PERSON><PERSON><PERSON> seng Ka<PERSON> au<PERSON>?", "muteParticipantBody": "Dir kënnt se net unmute loossen, awer si kënne sech zu all Moment unmute loossen.", "muteParticipantButton": "Mute", "muteParticipantDialog": "<PERSON><PERSON> <PERSON><PERSON>, datt Dir dëse Participant mute wëllt? Dir kënnt se net unmute loossen, awer si kënne sech zu all Moment unmute loossen.", "muteParticipantTitle": "Mut dëse Participant?", "muteParticipantsVideoBody": "Dir kënnt d'Kamera net erëm unschalten, awer si kënnen et zu all Moment zréckschalten.", "muteParticipantsVideoButton": "<PERSON><PERSON><PERSON>", "muteParticipantsVideoDialog": "<PERSON>d <PERSON><PERSON>, datt <PERSON><PERSON> d'Kamera vun dësem Participant auszeschalten wëllt? Dir kënnt d'Kamera net erëm unschalten, awer si kënne<PERSON> et zu all Moment zréckschalten.", "muteParticipantsVideoTitle": "Kamera vun dësem Participant auszeschalten?", "noDropboxToken": "Kee valabele Dropbox Token", "noScreensharingInAudioOnlyMode": "Kee Screensharing am Audio nëmmen Modus", "password": "<PERSON><PERSON><PERSON>", "passwordLabel": "D'Versammlung gouf vun engem Moderator gespaart. Gitt w.e.g. den $t(lockRoomPassword) un fir matzemaachen.", "passwordNotSupported": "Eng Versammlung $t(lockRoomPassword) astellen gëtt net ënnerstëtzt.", "passwordNotSupportedTitle": "$t(lockRoomPasswordUppercase) net ënnerstëtzt", "passwordRequired": "$t(lockRoomPasswordUppercase) erfuerderlech", "permissionCameraRequiredError": "Ka<PERSON><PERSON> ass erfuerd<PERSON>ch fir un Konferenzen mat <PERSON>. Gitt w.e.g. et an Astellungen", "permissionErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> néideg", "permissionMicRequiredError": "Mikrofon Erlaabnis ass erfuerderlech fir un Konferenzen mat Audio <PERSON>zehu<PERSON>. Gitt w.e.g. et an Astellungen", "popupError": "Äre Browser blockéiert Pop-upfenster vun dësem Site. Aktivéiert w.e.g. Pop-ups an de Sécherheetsastellunge vun Ärem Browser a probéiert nach eng Kéier.", "popupErrorTitle": "Pop-up block<PERSON><PERSON><PERSON>", "readMore": "méi", "recording": "<PERSON><PERSON>", "recordingDisabledBecauseOfActiveLiveStreamingTooltip": "Net méiglech wann e Live Stream aktiv ass", "recordingDisabledForGuestTooltip": "Gäscht kënnen net Opzeechnunge ufänken.", "recordingDisabledTooltip": "Start Opnahmen behënnert.", "rejoinNow": "<PERSON><PERSON>t elo erëm mat", "remoteControlAllowedMessage": "{{user}} huet Är Fernsteierungsufro ugeholl!", "remoteControlDeniedMessage": "{{user}} huet Är Fernsteierungsufro refuséiert!", "remoteControlErrorMessage": "<PERSON> <PERSON>er ass geschitt beim <PERSON>, d'Fernsteuerpermissioune vum {{user}} ze froen!", "remoteControlRequestMessage": "<PERSON><PERSON><PERSON>bt Dir {{user}} Ären Desktop op Fernbedienung?", "remoteControlShareScreenWarning": "<PERSON><PERSON><PERSON>t datt wann Dir op \"Erlaabt\" d<PERSON><PERSON><PERSON>, wäert <PERSON><PERSON>!", "remoteControlStopMessage": "D'Fernbedienungssession ass eriwwer!", "remoteControlTitle": "Remote Desktop Kontroll", "remoteUserControls": "Fernbenotzer Kontrollen vun {{username}}", "removeCDonation": "Klickt a Pledge Guide geläscht", "removeCDonationD": "D'Spendelink gouf erfollegräich <PERSON>", "removeDonation": "Donorbox Don Link geläscht", "removeDonationD": "D'Spendelink gouf erfollegräich <PERSON>", "removePassword": "Ewechzehuelen $t (lockRoomPassword)", "removeSharedVideoMsg": "Sidd Dir sécher datt Dir Äre gedeelt Video läschen wëllt?", "removeSharedVideoTitle": "E<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "reservationError": "Reservatioun System Feeler", "reservationErrorMsg": "Feelercode: {{code}}, Message: {{msg}}", "restartInitiatedBecauseOfBridgeFailure": "Neistart initiéiert wéinst engem Bréckfehler", "retry": "Nach eng <PERSON><PERSON> pro<PERSON>en", "revokeModeration": "<PERSON> Benotzer als Moderator zréckzéien?", "revokeModerationTitle": "<PERSON><PERSON><PERSON><PERSON>", "screenSharingAudio": "Deelen Audio", "screenSharingFailed": "Oops! Eppes ass falsch gaang, mir konnten net Écran deelen ufänken!", "screenSharingFailedTitle": "Écran <PERSON> g<PERSON>che<PERSON>t!", "screenSharingPermissionDeniedError": "Oops! Eppes ass falsch gaang mat <PERSON><PERSON><PERSON><PERSON>. Lued w.e.g. nei a probéiert nach eng Kéier.", "screenSharingUser": "{{displayName}} de<PERSON><PERSON> <PERSON> den Écran", "sendPrivateMessage": "Dir krut viru kuerzem eng privat Noriicht. Hues du wëlles op dat privat ze äntweren, oder Dir wëllt Äre Message un de Grupp schécken?", "sendPrivateMessageCancel": "Schéckt un de Grupp", "sendPrivateMessageOk": "Schéckt privat", "sendPrivateMessageTitle": "Privat schécken?", "serviceUnavailable": "Service net verfügbar", "sessTerminated": "<PERSON><PERSON> ofgeschloss", "sessionRestarted": "Call restarted v<PERSON>", "shareAudio": "<PERSON>ert weider", "shareAudioTitle": "Wéi deelt een Audio", "shareAudioWarningD1": "<PERSON>r musst Écran Sharing stoppen ier Dir Ären Audio deelt.", "shareAudioWarningD2": "Dir musst Är Écran Sharing Reouverture a kontrolléieren der \"share Audio\" Optioun.", "shareAudioWarningH1": "<PERSON><PERSON> just Audio deele w<PERSON>lt:", "shareAudioWarningTitle": "<PERSON>r musst <PERSON><PERSON><PERSON> stoppen ier Dir Audio deelt", "shareMediaWarningGenericH2": "<PERSON><PERSON> w<PERSON>lt Ären <PERSON>cran an Audio deelen", "shareScreenWarningD1": "<PERSON>r musst den Audio Deele stoppen ier Dir Ären <PERSON>.", "shareScreenWarningD2": "<PERSON>r musst Audio Sharing stoppen, <PERSON><PERSON><PERSON> <PERSON>le ufänken a kontrolléieren der \"share Audio\" Optioun.", "shareScreenWarningH1": "<PERSON><PERSON>r just <PERSON><PERSON> w<PERSON><PERSON>:", "shareScreenWarningTitle": "<PERSON>r musst den Audio Deele stoppen ier Dir Ären <PERSON>t", "shareVideoLinkError": "Gitt w.e.g. e richtege YouTube Link un.", "shareVideoTitle": "<PERSON><PERSON>", "shareYourScreen": "<PERSON><PERSON>", "shareYourScreenDisabled": "<PERSON><PERSON><PERSON>.", "shareYourScreenDisabledForGuest": "Gäscht kënnen net Écran deelen.", "startLiveStreaming": "Live Stream + Opname", "startRecording": "Start Opnam", "startRemoteControlErrorMessage": "E Feeler ass geschitt beim Versuch vun der Fernsteierungssession unzefänken!", "stopLiveStreaming": "Stoppen streaming", "stopRecording": "Stop opzehuelen", "stopRecordingWarning": "Sidd Dir sécher datt Dir den Opnahmen stoppen wëllt?", "stopStreamingWarning": "Sidd Dir sécher datt Dir de Live Streaming wëllt stoppen?", "streamKey": "Live Stream Schlëssel", "switchInProgress": "Wiessel am Fortschrëtt.", "thankYou": "<PERSON><PERSON><PERSON> da<PERSON> {{appName}} benotzt!", "token": "To<PERSON>zee<PERSON>g", "tokenAuthFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> sidd net erla<PERSON>t un dësem Uruff matzemaachen.", "tokenAuthFailedTitle": "Authentifikat<PERSON>un huet gesche<PERSON>t", "transcribing": "Transkriptioun", "unforceMuteEveryoneDialog": "Sidd Dir sécher datt Dir de Mikro vu jidderee wëllt opmaachen? si kënnen unmute & schwätzen.", "unforceMuteEveryoneElseDialog": "Undoen Mute hinnen a loosse se hire Mikro aktivéieren", "unforceMuteEveryoneElseTitle": "Ophiewen Force Mute jiddereen ausser {{whom}}?", "unforceMuteEveryoneElsesVideoDialog": "<PERSON><PERSON> d'Kamera aktivéiert ass, k<PERSON>nne<PERSON> Si hir Kamera aktivéieren", "unforceMuteEveryoneElsesVideoTitle": "<PERSON><PERSON><PERSON> seng Kamera aktivéieren ausser {{whom}}?", "unforceMuteEveryoneSelf": "selwer", "unforceMuteEveryoneTitle": "Undoen Force Mute jidderengem säi Mikro?", "unforceMuteEveryonesVideoDialog": "Sidd Dir sécher datt Dir de Video vu jidderee wëllt opmaachen?", "unforceMuteEveryonesVideoTitle": "Jiddereen seng Kamera aktivéieren?", "unforceMuteParticipantBody": "Undoen Mute Participant.", "unforceMuteParticipantButton": "Force Mute undoen", "unforceMuteParticipantDialog": "Sidd Dir sécher datt Dir de Video vun dësem Participant wëllt opspären?.", "unforceMuteParticipantTitle": "Undo Force Mute dëse Participant?", "unforceMuteParticipantsVideoBody": "D'Participanten Video gëtt ageschalt a si kënnen erëm op<PERSON>achen", "unforceMuteParticipantsVideoButton": "Kamera aktivéieren", "unforceMuteParticipantsVideoTitle": "Spär Video vun dësem Participant?", "unlockRoom": "Ewechzehuelen Sëtzung $t (lockRoomPassword)", "user": "<PERSON><PERSON><PERSON>", "userIdentifier": "<PERSON><PERSON><PERSON> Identifizéierer", "userPassword": "<PERSON><PERSON><PERSON>", "videoLink": "Video Link", "viewUpgradeOptions": "View Upgrade Optiounen", "viewUpgradeOptionsContent": "Fir onlimitéiert Zougang zu Premium Features wéi Opnam, Transkriptiounen, RTMP Streaming a méi ze kréien, musst Dir Äre Plang upgraden.", "viewUpgradeOptionsTitle": "Dir hutt eng Premium Feature entdeckt!", "yourEntireScreen": "<PERSON><PERSON> ganzen <PERSON>ran"}, "documentSharing": {"title": "<PERSON><PERSON>"}, "donorbox": {"errorDesc": "Gitt w.e.g. gëlteg Donorbox Kampagne URL an. Fir méi Detailer besicht - www.donorbox.org", "errorNotification": "Ongëlteg Donorbox URL", "title": "Dobäizemaachen DonorBox Campaign URL", "titlenative": "Spenderbox"}, "e2ee": {"labelToolTip": "Audio a Video Kommunikatioun op dësem Uruff ass end-to-end verschlësselte"}, "embedMeeting": {"title": "<PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "feedback": {"average": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bad": "<PERSON><PERSON><PERSON><PERSON>", "detailsLabel": "Sot eis méi doriwwer.", "good": "<PERSON><PERSON>", "rateExperience": "Bewäert Är Versammlungserfarung", "star": "<PERSON><PERSON><PERSON>", "veryBad": "Ganz schlecht", "veryGood": "<PERSON><PERSON><PERSON> gutt"}, "giphy": {"giphy": "<PERSON><PERSON><PERSON><PERSON>", "noResults": "<PERSON>g Resultater fonnt :(", "search": "Sich GIPHY"}, "helpView": {"header": "<PERSON><PERSON><PERSON><PERSON>"}, "incomingCall": {"answer": "Äntwert", "audioCallTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "decline": "<PERSON><PERSON><PERSON><PERSON>", "productLabel": "aus Liicht méi Zäit treffen", "videoCallTitle": "Entréeën Video Uruff"}, "info": {"accessibilityLabel": "Info weisen", "addPassword": "Füügt $ T (Lockroompassword)", "cancelPassword": "Annuléieren $t(lockRoomPassword)", "conferenceURL": "Link:", "copyNumber": "<PERSON><PERSON>", "country": "Land", "dialANumber": "Fir bei Ärer Versammlung matzemaachen, wielt eng vun dësen <PERSON> a gitt dann de <PERSON>.", "dialInConferenceID": "PaG: <PERSON><PERSON><PERSON><PERSON>", "dialInNotSupported": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> gëtt de Moment net ënnerstëtzt.", "dialInNumber": "Uruff:", "dialInSummaryError": "Fe<PERSON> beim Ofhu<PERSON>n vun Uruffinformatioun elo. Probéiert w.e.g. méi spéit nach eng Kéier.", "dialInTollFree": "<PERSON><PERSON> gratis", "genericError": "<PERSON>s, eppes ass falsch gaang.", "inviteLiveStream": "<PERSON>r de Livestream vun dëser Versammlung ze gesinn, klickt op dëse Link: {{url}}", "invitePhone": "Fir amplaz per Telefon matzemaachen, tippt op dës: {{number}},,{{conferenceID}}#", "invitePhoneAlternatives": "Dir sicht eng aner Uruffnummer?\nKuckt d'Versammlungsnummeren: {{url}}\n\n\nWann Dir och duerch e Raumtelefon urufft, maacht mat ouni mat Audio ze verbannen: {{silentUrl}}", "inviteSipEndpoint": "<PERSON>r matze<PERSON>n mat der S<PERSON>, gitt dës: {{sipUri}}", "inviteTextiOSInviteUrl": "<PERSON><PERSON>t op de folgende Link fir matzemaachen: {{inviteUrl}}.", "inviteTextiOSJoinSilent": "<PERSON><PERSON> e Raumtelefon urufft, ben<PERSON><PERSON><PERSON> dëse Link fir matzemaachen ouni mat Audio ze verbannen: {{silentUrl}}.", "inviteTextiOSPersonal": "{{name}} invi<PERSON>iert Iech op eng Versammlung.", "inviteTextiOSPhone": "Fir iwwer Telefon matzemaachen, ben<PERSON><PERSON>t d<PERSON><PERSON>: {{number}},,{{conferenceID}}#. Wann Dir no enger anerer <PERSON> sicht, ass dat déi komplett Lëscht: {{didUrl}}.", "inviteURLFirstPartGeneral": "Dir sidd invitéiert op eng Versammlung matzemaachen.", "inviteURLFirstPartPersonal": "{{name}} invi<PERSON>iert Iech op eng Versammlung.", "inviteURLSecondPart": "Maacht mat bei der Versammlung:\n{{url}}", "label": "<PERSON><PERSON>", "liveStreamURL": "Livestream:", "moreNumbers": "<PERSON><PERSON><PERSON>", "noNumbers": "<PERSON><PERSON>.", "noPassword": "<PERSON><PERSON>", "noRoom": "<PERSON>e Raum gouf spezifizéiert fir anzeruffen.", "numbers": "<PERSON><PERSON>", "password": "$ T (Lockroompasswordupperce):", "sip": "SIP Adress", "title": "<PERSON><PERSON>", "tooltip": "Deelt de Link an d'Informatioun fir dës Versammlung"}, "inlineDialogFailure": {"msg": "Mir sinn e b<PERSON>ssen getrollt.", "retry": "Prob<PERSON>iert nach eng Kéier", "support": "Ënnerstëtzung", "supportMsg": "<PERSON><PERSON> d<PERSON>st weider geet, rufft un"}, "inviteDialog": {"alertText": "Ausgefall e puer Participanten ze invitéieren.", "header": "Invi<PERSON>ier<PERSON>", "searchCallOnlyPlaceholder": "Gitt Telefonsnummer", "searchPeopleOnlyPlaceholder": "Sich no Participanten", "searchPlaceholder": "Participant oder Telefonsnummer", "send": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "jitsiHome": "{{logo}} Logo, Linken op d'Homepage", "keyboardShortcuts": {"focusLocal": "Focus op Äre Video", "focusRemote": "Focus op de Video vun enger anerer Persoun", "fullScreen": "View oder Sortie Vollbildmodus", "keyboardShortcuts": "Tastatur Ofkiirzungen", "localRecording": "Show oder verstoppt lokal Opnamkontrollen", "mute": "<PERSON>te oder unmute Äre Mi<PERSON>", "pushToTalk": "Dréckt fir ze schwätzen", "raiseHand": "<PERSON><PERSON>t oder senken Är Hand", "showSpeakerStats": "Show Spriecherstatistiken", "toggleChat": "Den Chat opmaachen oder zou<PERSON>achen", "toggleFilmstrip": "Show oder verstoppen Video Miniatyren", "toggleParticipantsPane": "Weist oder verstoppt d'Participantenfenster", "toggleScreensharing": "Wiesselt tëscht Kamera an Écran Deele", "toggleShortcuts": "Tastatur Ofkiirzungen weisen oder verstoppen", "videoMute": "Start oder stoppen Är Kamera", "videoQuality": "Verwalte Uruffqualitéit"}, "liveChatView": {"header": "24x7 Live Ënnerstëtzung"}, "liveStreaming": {"addStream": "Dobäizemaachen Destinatioun", "busy": "Mir schaffen un der fräier Streaming Ressourcen. Probéiert w.e.g. nach eng Kéier an e puer Minutten.", "busyTitle": "All Streamer sinn am Moment beschäftegt", "changeSignIn": "Wiesselt Konten.", "choose": "Wielt e Live Stream", "chooseCTA": "Wielt eng Streamingoptioun. Dir sidd am Moment als {{email}} ageloggt.", "enterLinkedInUrlWithTheKey": "<PERSON>itt LinkedIn URL mat dem Schlëssel un", "enterStreamKey": "Gitt Ären YouTube Live Stream Schlëssel hei.", "enterStreamKeyFacebook": "Gitt Äre Facebook Live Stream Schlëssel hei.", "enterStreamKeyInstagram": "Gitt Ären Instagram Live Stream Schlëssel hei.", "enterStreamKeyYouTube": "Gitt hei <PERSON> {{youtube}} Live Stream Schlëssel an.", "error": "Live Streaming gescheitert. Probéiert w.e.g. nach eng <PERSON>éier.", "errorAPI": "<PERSON> <PERSON>er ass geschitt beim Zougang zu Äre YouTube Sendungen. Probéiert w.e.g. nach eng Kéier aloggen.", "errorLiveStreamNotEnabled": "Live Streaming ass net aktivéiert op {{email}}. Aktivéiert w.e.g. Live Streaming oder loggt Iech op e Kont mat Live Streaming aktivéiert.", "expandedOff": "De Livestream ass gestoppt", "expandedOn": "D'Versammlung gëtt de Moment op YouTube gestreamt.", "expandedPending": "De Livestream gëtt ugefaang ...", "failToStartAutoLiveStreaming": "Ausgefall Auto Live Streaming starten", "failToStartAutoRecording": "Ausgefall Auto Recording ufänken", "failedToStart": "Live Streaming konnt net starten", "getStreamKeyManually": "Mir konnten keng Live Streams sichen. Probéiert Äre Live Stream Schlëssel vun YouTube ze kréien.", "googlePrivacyPolicy": "Google Privatsphär Politik", "invalidStreamKey": "Live Stream Schlëssel kann falsch sinn.", "limitNotificationDescriptionNative": "Äre Streaming gëtt op {{limit}} min limitéiert. Fir onlimitéiert Streaming probéiert {{app}}.", "limitNotificationDescriptionWeb": "Wé<PERSON>t der grousser Nofro ass Äre Streaming op {{limit}} min limitéiert. Fir onlimitéiert Streaming probéiert <a href={{url}} rel='noopener noreferrer' target='_blank'>{{app}}</a>.", "makeSureYouHaveEnoughStorageAvailableOnYourAccount": "Vergewëssert Iech datt Dir genuch Späichere op Ärem Kont hutt.", "note": "<PERSON><PERSON><PERSON><PERSON>", "off": "Live Streaming gestoppt", "offBy": "{{name}} huet de Livestream gestoppt", "on": "Live Streaming huet ugefaang", "onBy": "{{name}} huet de Livestream ugefaangen", "pending": "Start Live Stream ...", "pleaseContactSupportForAssistance": "Weg Kontakt Ënnerstëtzung fir Hëllef.", "serviceName": "Live Streaming Service", "signIn": "<PERSON><PERSON>ech mat Google un", "signInCTA": "<PERSON><PERSON> un oder gitt Äre Live Stream Schlëssel vun YouTube.", "signOut": "<PERSON><PERSON>chen aus", "signedInAs": "Dir sidd de <PERSON> ugemellt als:", "start": "Opnam + Live Stream", "startService": "Start Service", "streamIdHelp": "Wat ass dat?", "unavailableTitle": "Live Streaming net verfügbar", "youtubeTerms": "YouTube Konditioune vun Servicer"}, "lobby": {"admit": "<PERSON><PERSON><PERSON><PERSON>", "admitAll": "All zouginn", "allow": "<PERSON><PERSON><PERSON><PERSON>", "backToKnockModeButton": "<PERSON><PERSON>, frot amplaz <PERSON>", "dialogTitle": "<PERSON><PERSON>", "disableDialogContent": "De Lobbymodus ass momentan aktivéiert. Dës Fonktioun garantéiert datt onerwënscht Participanten net bei Ärer Versammlung kënne matmaachen. Wëllt Dir et auszeschalten?", "disableDialogSubmit": "Desaktivéieren", "emailField": "<PERSON><PERSON> <PERSON><PERSON>", "enableDialogPasswordField": "<PERSON><PERSON><PERSON> (fakultativ)", "enableDialogSubmit": "Aktivéieren", "enableDialogText": "Lobby Modus erlaabt Iech Är Versammlung ze schützen andeems Dir nëmmen d'Leit erlaabt no enger formell Genehmegung vun engem Moderator anzeginn.", "enterPasswordButton": "Gitt S<PERSON>tzung Passwuert", "enterPasswordTitle": "<PERSON><PERSON>wuert fir d'Versammlung matzemaachen", "invalidPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "joinRejectedMessage": "Är Ufro fir Umeldung gouf vun engem Moderator refus<PERSON>iert.", "joinTitle": "Maacht mat bei der Versammlung", "joinWithPasswordMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> mat <PERSON><PERSON>, waart w.e.g. ...", "joiningMessage": "Dir gitt der Versammlung mat soubal een Är Demande ak<PERSON>pté<PERSON>t", "joiningTitle": "Frot Iech un der Versammlung matzemaachen ...", "joiningWithPasswordTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mat <PERSON> ...", "knockButton": "Frot Iech <PERSON>", "knockTitle": "<PERSON><PERSON><PERSON> w<PERSON><PERSON> der Versammlung matmaachen", "knockingParticipantList": "Klappen Participant Lëscht", "nameField": "<PERSON><PERSON> <PERSON>", "notificationLobbyAccessDenied": "{{targetParticipantName}} gouf refuséiert fir matzemaachen vum {{originParticipantName}}", "notificationLobbyAccessGranted": "{{targetParticipantName}} ass erlaabt matze<PERSON>achen vum {{originParticipantName}}", "notificationLobbyDisabled": "Lobby gouf vum {{originParticipantName}} desaktivéiert", "notificationLobbyEnabled": "Lobby gouf vum {{originParticipantName}} aktivéiert", "notificationTitle": "Lobby", "passwordField": "Gitt S<PERSON>tzung Passwuert", "passwordJoinButton": "<PERSON><PERSON>t mat", "reject": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rejectAll": "All refuséieren", "toggleLabel": "<PERSON>bby aktivé<PERSON>en"}, "localRecording": {"clientState": {"off": "<PERSON>un der ze", "on": "Op", "unknown": "Onbekannt"}, "dialogTitle": "Lokal Recording Kontrollen", "duration": "<PERSON><PERSON>", "durationNA": "N / a", "encoding": "Kodéierung", "label": "<PERSON>r", "labelToolTip": "Lokal Opnahmen ass engagé<PERSON>t", "localRecording": "Lokal Recording", "me": "<PERSON><PERSON>", "messages": {"engaged": "Lokal Opnahmen engagéiert.", "finished": "D'Opnahmssession {{token}} fäerdeg. Schéckt w.e.g. déi opgeholl Datei un de Moderator.", "finishedModerator": "D'Opnahmssession {{token}} fäerdeg. Den Opnahm vun der lokaler Streck gouf gespäichert. Frot w.e.g. déi aner Participanten hir Opzeechnunge ofzeginn.", "notModerator": "<PERSON>r sidd net de Moderator. Dir kënnt d'lokal Opnam net starten oder stoppen."}, "moderator": "<PERSON><PERSON><PERSON>", "no": "<PERSON><PERSON>", "participant": "<PERSON><PERSON><PERSON>", "participantStats": "Participant Statistiken", "sessionToken": "<PERSON><PERSON><PERSON><PERSON>", "start": "Start Opnam", "stop": "Stoppen Opnam", "yes": "<PERSON>"}, "lockRoomPassword": "<PERSON><PERSON><PERSON>", "lockRoomPasswordUppercase": "<PERSON><PERSON><PERSON>", "lonelyMeetingExperience": {"button": "<PERSON><PERSON><PERSON><PERSON><PERSON> an<PERSON>", "youAreAlone": "Dir sidd deen eenzegen an der Versammlung"}, "me": "ech", "notify": {"OldElectronAPPTitle": "Sécherheet Schwachstelle!", "connectedOneMember": "{{name}} ass bei der Versammlung ugeschloss", "connectedThreePlusMembers": "{{name}} an {{count}} anerer si bei der Versammlung matgemaach", "connectedTwoMembers": "{{first}} an {{second}} si bei der Versammlung ugeschloss", "disconnected": "deconnect<PERSON><PERSON><PERSON>", "focus": "Konferenz konzentréieren", "focusFail": "{{component}} net verfügbar - probéiert nach eng Kéier an {{ms}} Sekonnen", "grantedTo": "Moderator<PERSON><PERSON> ginn un {{to}}!", "groupTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hostAskedUnmute": "De <PERSON> hätt gär datt Di<PERSON>Must<PERSON>hu<PERSON>", "invitedOneMember": "{{name}} gouf in<PERSON>t", "invitedThreePlusMembers": "{{name}} an {{count}} anerer goufen invitéiert", "invitedTwoMembers": "{{first}} an {{second}} goufen invi<PERSON>t", "kickParticipant": "{{kicked}} gouf vum {{kicker}} geläscht", "me": "<PERSON><PERSON>", "moderationInEffectCSDescription": "Huelt w.e.g. d'Hand op wann Dir Äre Video deele wëllt", "moderationInEffectCSTitle": "Inhaltsdeele gëtt vum Moderator ausgeschalt", "moderationInEffectDescription": "Gitt w.e.g. d'Hand op wann Dir wëllt schwätzen", "moderationInEffectTitle": "De Mikrofon gëtt vum Moderator gedämpft", "moderationInEffectVideoDescription": "Huelt w.e.g. Är Hand op wann Dir wëllt datt Äre Video sichtbar ass", "moderationInEffectVideoTitle": "De Video gëtt vum Moderator gedämpft", "moderationRequestFromModerator": "De <PERSON> hätt gär datt Di<PERSON>Must<PERSON>hu<PERSON>", "moderationRequestFromParticipant": "<PERSON><PERSON><PERSON> s<PERSON>", "moderationStartedTitle": "<PERSON><PERSON><PERSON><PERSON> u<PERSON>", "moderationStoppedTitle": "<PERSON><PERSON><PERSON><PERSON> gestoppt", "moderationToggleDescription": "vum {{participantDisplayName}}", "moderator": "Moderator <PERSON><PERSON><PERSON> ginn!", "muted": "Dir hutt d'Gespréich gestoppt ugefaangen.", "mutedRemotelyDescription": "Dir kënnt ëmmer d'Muster ophalen wann Dir prett sidd ze schwätzen. Mute z<PERSON>ck wann Dir fäerdeg sidd fir Kaméidi vun der Versammlung ewech ze halen.", "mutedRemotelyTitle": "Dir sidd vum {{participantDisplayName}} gedämpft!", "mutedTitle": "Dir sidd gedämpft!", "newDeviceAction": "Benotzt", "newDeviceAudioTitle": "Neien Audiogerät entdeckt", "newDeviceCameraTitle": "<PERSON><PERSON> entdeckt", "oldElectronClientDescription1": "Dir schéngen eng al Versioun vum Liicht méi Zäit treffen Client ze benotzen déi bekannte Sécherheetsschwieregkeeten huet. Gitt sécher datt Dir op eis aktualiséiert", "oldElectronClientDescription2": "lescht bauen", "oldElectronClientDescription3": "elo!", "passwordRemovedRemotely": "$t(lockRoomPasswordUppercase) vun engem anere Participant geläscht", "passwordSetRemotely": "$t(lockRoomPasswordUppercase) vun engem anere Participant gesat", "raiseHandAction": "<PERSON><PERSON>t d'Hand op", "raisedHand": "{{name}} g<PERSON><PERSON> g<PERSON> s<PERSON>.", "reactionSounds": "Kläng auszeschal<PERSON>", "reactionSoundsForAll": "Desaktivéiere Kläng fir all", "screenShareNoAudio": "Deelen Audio Këscht war net an der Fënster Auswiel Écran iwwerpréift.", "screenShareNoAudioTitle": "Konnt de System Audio net deelen!", "somebody": "<PERSON><PERSON>", "startSilentDescription": "Ma<PERSON>t erëm mat der Versammlung fir Audio z'aktivéieren", "startSilentTitle": "Dir sidd ugeschloss ouni Audioausgang!", "suboptimalBrowserWarning": "Mir fäerten datt Är Versammlungserfarung hei net sou super wäert sinn. Mir sichen no Weeër fir dëst ze verbesseren, awer bis dohin probéiert w.e.g. ee vun de <a href='{{recommendedBrowserPageLink}}' target='_blank'>voll ënnerstëtzte Browser</a> ze benotzen.", "suboptimalExperienceTitle": "Browser <PERSON>", "unmute": "<PERSON><PERSON><PERSON><PERSON>", "videoMutedRemotelyDescription": "<PERSON><PERSON> kë<PERSON> et ëmmer erëm op<PERSON>achen.", "videoMutedRemotelyTitle": "Är Kamera gouf vum {{participantDisplayName}} ausgeschalt!"}, "participantsPane": {"actions": {"allow": "Erlaabt Participanten:", "askUnmute": "Frot de Mute unzeschléissen", "blockEveryoneMicCamera": "<PERSON><PERSON><PERSON><PERSON> jidderengem säi Mikro a Kamera", "breakoutRooms": "Breakout <PERSON><PERSON>", "forceMute": "Forcéieren Mute Audio", "forceMuteAll": "Forcéieren stut all", "forceMuteAllVideo": "Forcéieren Video mute all", "forceMuteEveryoneElse": "Force Mute <PERSON>", "forceMuteEveryoneElseVideo": "Force Mute Ji<PERSON>", "forceMuteVideo": "Forcéieren Mute Video", "invite": "Invi<PERSON><PERSON>t een", "mute": "Mute", "muteAll": "Mut all", "muteEveryoneElse": "Mute all aner", "startModeration": "Unmute selwer oder Start Video", "stopEveryonesVideo": "Stop jidderengem säi Video", "stopVideo": "Stop Video", "unForceMute": "réckgängeg Kraaft Mute Audio", "unForceMuteAll": "Undoen Force Mute All", "unForceMuteVideo": "Ofbriechen Kraaft Mute Video", "unblockEveryoneMicCamera": "<PERSON><PERSON><PERSON><PERSON> jidderengem säi Mikro a Kamera", "unforceMuteAllVideo": "Undoen Force Video Mute All", "unforceMuteEveryoneElse": "Undoen Kraaft Mute Jiddereen Aneren", "unforceMuteEveryoneElseVideo": "Undoen Kraaft Mute Everyone Else Video"}, "close": "Zoumaachen", "header": "Participanten", "headings": {"lobby": "Lobby ({{count}})", "participantsList": "Meeting Participanten ({{count}})", "waitingLobby": "Waarden an der Lobby ({{count}})"}, "search": "<PERSON>ch Participanten"}, "passwordDigitsOnly": "Bis zu {{number}} <PERSON><PERSON><PERSON><PERSON>", "passwordSetRemotely": "vun engem anere Participant gesat", "polls": {"answer": {"skip": "<PERSON><PERSON><PERSON><PERSON>", "submit": "Ofginn"}, "by": "Vun {{ name }}", "create": {"addOption": "<PERSON><PERSON>", "answerPlaceholder": "Optioun {{index}}", "cancel": "Ofbriechen", "create": "Erst<PERSON>t eng Ëmfro", "pollOption": "Ëmfrooptioun {{index}}", "pollQuestion": "Ëmfro Fro", "questionPlaceholder": "Stellt eng <PERSON>o", "removeOption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "send": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "notification": {"description": "Open Emfroen Tab fir ze stëmmen", "title": "Eng nei Ëmfro gouf zu dëser Versammlung bäigefüügt"}, "results": {"changeVote": "Änneren Vote", "empty": "Et sinn nach keng Ëmfroen an der Versammlung. Start eng Ëmfro hei!", "hideDetailedResults": "<PERSON><PERSON><PERSON> ve<PERSON>", "showDetailedResults": "Show Detailer", "vote": "Stëmmen"}}, "poweredby": "© trefft Stonn LLC", "prejoin": {"alreadyOneConferenceIsRunningInBackground": "Schonn eng Konferenz leeft am Hannergrond.", "audioAndVideoError": "Audio a Video Feeler:", "audioDeviceProblem": "Et g<PERSON>tt e Problem mat Ärem Audiogerät", "audioOnlyError": "Audiofehler:", "audioTrackError": "Konnt den Audiotrack net erstellen.", "callMe": "Ruff mir un", "callMeAtNumber": "<PERSON><PERSON><PERSON> mech op dëser Nummer:", "calling": "<PERSON><PERSON><PERSON>", "configuringDevices": "Apparater konfiguréieren ...", "connectedWithAudioQ": "Sidd Dir mat Audio verbonnen?", "connection": {"good": "Är Internetverbindung gesäit gutt aus!", "nonOptimal": "Är Internetverbindung ass net optimal", "poor": "Dir hutt eng schlecht Internetverbindung"}, "connectionDetails": {"audioClipping": "Mir erwaarden datt Ären Audio ofgeschnidden ass.", "audioHighQuality": "Mir erwaarden datt Ären Audio eng exzellent Qualitéit huet.", "audioLowNoVideo": "Mir erwaarden datt Är Audioqualitéit niddereg ass a kee Video.", "goodQuality": "Fantastesch! Är Medienqualitéit wäert super sinn.", "noMediaConnectivity": "Mir konnten kee Wee fannen fir Medienkonnektivitéit fir dësen Test ze etabléieren. Dëst gëtt normalerweis vun enger Firewall oder NAT verursaacht.", "noVideo": "Mir erwaarden datt Äre Video schrecklech wäert sinn.", "undetectable": "Wann Dir nach ëmmer net am Browser u<PERSON> k<PERSON>, empf<PERSON><PERSON> mir <PERSON><PERSON> sécher ze stellen datt Är Spriecher, <PERSON>k<PERSON><PERSON>n a Kamera richteg ageriicht sinn, datt Dir Äre Browser Rechter kritt hutt fir Äre Mikrofon a Kamera ze ben<PERSON>, an datt Är Browser Versioun up-to-date ass. -Datum. Wann Dir nach ëmmer Probleemer hutt ze ruffen, sollt Dir de Webapplikatiounsentwéckler kontaktéieren.", "veryPoorConnection": "Mir erwaarden datt Är Uruffqualitéit wierklech schrecklech ass.", "videoFreezing": "Mir erwaarden datt Äre Video afréiert, schwa<PERSON>z gëtt a pixeléiert gëtt.", "videoHighQuality": "Mir erwaarden datt Äre Video gutt Qualitéit huet.", "videoLowQuality": "Mir erwaarden datt Äre Video niddereg Qualitéit wat d'Framesrate an d'Resolutioun ugeet.", "videoTearing": "Mir erwaarden datt Äre Video pixeléiert gëtt oder visuell Artefakte huet."}, "copyAndShare": "Kopéiert & deelt Versammlungslink", "dashboard": "Dashboard", "daysAgo": "Virun {{daysCount}} Deeg", "dialInMeeting": "Wielt an d'Versammlung", "dialInPin": "Wielt an d'Versammlung a gitt PIN Code:", "dialing": "<PERSON><PERSON><PERSON>", "doNotShow": "<PERSON><PERSON> d<PERSON><PERSON>ran net erëm", "enterMeetingIdOrLink": "Gitt Versammlung ID oder Link", "errorDialOut": "Konnt net erausruffen", "errorDialOutDisconnected": "Konnt net erausruffen. Deconnectéiert", "errorDialOutFailed": "Konnt net erausruffen. Uruff gescheitert", "errorDialOutStatus": "Feeler be<PERSON>", "errorMissingEmail": "Gitt w.e.g. Är E-Mail un fir bei der Versammlung matzemaachen", "errorMissingName": "Gitt w.e.g. <PERSON><PERSON> un fir matzemaachen", "errorNameLength": "Gitt weg op d'mannst 3 Buschtawen an Ärem Numm", "errorStatusCode": "Feeler be<PERSON>, Statuscode: {{status}}", "errorValidation": "<PERSON><PERSON> g<PERSON>", "features": "Fonctiounen", "guestNotAllowedMsg": "Gäscht sinn net erlaabt dës Versammlung matzemaachen", "iWantToDialIn": "<PERSON><PERSON> w<PERSON><PERSON>", "initiated": "Uruff initiéiert", "invalidEmail": "Ongëlteg E-Mail", "joinAMeeting": "Maacht mat bei enger Versammlung", "joinAudioByPhone": "Maacht mat Telefon Audio", "joinMeeting": "Maacht mat bei der Versammlung", "joinMeetingGuest": "Maacht mat bei der Versammlung als Gaascht", "joinWithoutAudio": "Ma<PERSON>t mat ouni <PERSON>", "keyboardShortcuts": "Aktivéiert Tastatur Ofkiirzungen", "linkCopied": "Link op Clipboard kopéiert", "logout": "Ausloggen", "lookGood": "Et kléngt wéi Äre Mikrofon richteg funktionnéiert", "maximumAllowedParticipantsErr": "Maximal erlaabt Participanten hunn op dëser Reunioun erreecht. Kontakt Meeting Organisateur.", "meetingReminder": "D'Versammlung fänkt {{time}} un. Maacht w.e.g. <PERSON> op oder virum geplangten Zäit.", "multipleConferenceInitiation": "Multiple Konferenz Initiatioun", "oops": "Oops!", "oppsMaximumAllowedParticipantsErr": "Oops! Maximal Participanten erlaabt Limit ass erreecht. W.e.g. waart op d'Participanten fir ze verloossen oder kontaktéiert den Versammlungsorganisateur.", "or": "oder", "parallelMeetingsLicencesErr": "Kann d'Versammlung net starten. Vergewëssert Iech datt Dir eng aktiv Lizenz hutt fir Parallel Versammlungen matzemaachen", "peopleInTheCall": "Leit am Opruff", "pleaseEnterEmail": "<PERSON><PERSON> weg <PERSON>", "pleaseEnterFullName": "Gitt w.e.g. <PERSON> gan<PERSON>", "premeeting": "Virversammlung", "profile": "Profil", "readyToJoin": "Prett fir matze<PERSON>achen?", "recentMeetings": "Rezent Reuniounen", "screenSharingError": "<PERSON><PERSON><PERSON>:", "showScreen": "Aktivéiert Pre Meeting Écran", "signinsignup": "Umellen / Umellen", "startWithPhone": "Start mat Telefon Audio", "subScriptionInactiveErr": "Ären Abonnement ass inaktiv. Kann d'Versammlung net starten.", "systemUpgradedInformation": "Mir hunn eise System op 2.0 Versioun aktualiséiert. Fuerdert dës Versammlung andeems Dir dëst Versammlungspasswuert de<PERSON>t", "userNotAllowedToJoin": "Benotzer net erlaabt matzemaachen", "videoOnlyError": "Video Feeler:", "videoTrackError": "Konnt net Video Streck schafen.", "viewAllNumbers": "gesinn all Zuelen", "waitForModeratorMsg": "Waart w.e.g. wann de Moderator mam <PERSON> b<PERSON>.", "waitForModeratorMsgDynamic": "Waart w.e.g. wann {{Moderator}} beim <PERSON> b<PERSON>.", "youAreNotAllowed": "<PERSON>r sidd net erla<PERSON>t"}, "presenceStatus": {"busy": "Beschäftegt", "calling": "Rufft ...", "connected": "Ugeschloss", "connecting": "Connectéiert ...", "connecting2": "Verbindung*...", "disconnected": "Deconnect<PERSON><PERSON><PERSON>", "expired": "<PERSON><PERSON><PERSON><PERSON>", "ignored": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "initializingCall": "Uruff initialiséieren ...", "invalidToken": "Ongëlteg <PERSON>", "invited": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ringing": "Réngt...", "signInAsHost": "<PERSON><PERSON> als Host un"}, "profile": {"avatar": "avatar", "setDisplayNameLabel": "<PERSON><PERSON> Numm", "setEmailInput": "Gitt E-Mail", "setEmailLabel": "Är E-Mail", "title": "Profil"}, "raisedHand": "<PERSON><PERSON><PERSON>", "recording": {"authDropboxText": "Eroplueden op Dropbox", "availableS3Space": "Benotzt Plaz: {{s3_used_space}} vun {{s3_free_space}}", "availableSpace": "Verfügbar Plaz: {{spaceLeft}} MB (ongeféier {{duration}} Minutten Opnam)", "beta": "Betaa", "busy": "Mir schaffen un der Fräisetzung vun Opnamressourcen. Probéiert w.e.g. nach eng Kéier an e puer Minutten.", "busyTitle": "All Rekorder sinn am Moment beschäftegt", "consentDialog": {"accept": "<PERSON>ch sinn averstan", "disclaimer": "De Start kann fir Do<PERSON><PERSON>ati<PERSON>, Training oder aner flotten Zwecker benotzt ginn. Wann Dir net averstan sidd, lo<PERSON>t <PERSON>ech elo d'Sëtzung.", "leaveMeeting": "Verloossen Versammlung", "message": "<PERSON><PERSON><PERSON> ass amgaang ze ginn. Mat weiderhi weider mat<PERSON>, <PERSON><PERSON> a<PERSON> ze gesinn.", "title": "Opnamen Zoustëmmung"}, "copyLink": "<PERSON>", "error": "Opnam gescheitert. Probéiert w.e.g. nach eng <PERSON>éier.", "errorFetchingLink": "Feeler be<PERSON> vun den Opnamlink.", "expandedOff": "D'Opnahm ass gestoppt", "expandedOn": "D'Versammlung gëtt am Moment opgeholl.", "expandedPending": "D'Opnahm gëtt ugefaang ...", "failedToStart": "Den Opnahmen konnt net ufänken", "fileSharingdescription": "<PERSON>lt den Opnahme mat de Versammlungsparticipanten", "limitNotificationDescriptionNative": "W<PERSON><PERSON>t der grousser Nofro ass Är Opnam op {{limit}} min limitéiert. Fir onlimitéiert Opzeechnunge probéiert <3>{{app}}</3>.", "limitNotificationDescriptionWeb": "W<PERSON><PERSON>t der grousser Nofro ass Är Opnam op {{limit}} min limitéiert. Fir onlimitéiert Opzeechnunge probéiert <a href={{url}} rel='noopener noreferrer' target='_blank'>{{app}}</a>.", "linkGenerated": "<PERSON> hunn e Link op Är Opnam generéiert.", "live": "LIEWEN", "loggedIn": "<PERSON>oggen als {{userName}}", "off": "Opnahmen gestoppt", "offBy": "{{name}} huet d'Opnahm gestoppt", "on": "<PERSON>nah<PERSON>", "onBy": "{{name}} huet den Opnahmen ugefaangen", "pending": "Preparéiere fir d'Versammlung opzehuelen ...", "rec": "Re<PERSON><PERSON>", "recLive": "Live + rec", "serviceDescription": "Är Opnam gëtt vum Opnamservice gespäichert", "serviceDescriptionCloud": "<PERSON>", "serviceName": "Recording Service", "signIn": "<PERSON><PERSON><PERSON> an", "signOut": "<PERSON><PERSON>chen aus", "unavailable": "Oops! De {{serviceName}} ass momentan net verfügbar. Mir schaffen un der Léisung vum Problem. Probéiert w.e.g. méi spéit nach eng Kéier.", "unavailableTitle": "Opnam net verfügbar", "uploadToCloud": "Eroplueden an d'Wollek"}, "sectionList": {"pullToRefresh": "Pull fir ze erfrëschen"}, "security": {"about": "Dir kënnt en $t(lockRoomPassword) op Är Versammlung derbäisetzen. D'Participanten mussen den $t (lockRoomPassword) ubidden ier se erlaabt sinn an d'Versammlung deelzehuelen.", "aboutReadOnly": "Moderator Participanten kënnen en $t(lockRoomPassword) op d'Sëtzung addéieren. D'Participanten mussen den $t (lockRoomPassword) ubidden ier se erlaabt sinn an d'Versammlung deelzehuelen.", "insecureRoomNameWarning": "Den Numm vum Zëmmer ass onsécher. Onerwënscht Participanten kënnen op Är Konferenz matmaachen. Betruecht Är Versammlung mat der Sécherheetsknäppchen ze sécheren.", "securityOptions": "Sécherheet Optiounen"}, "settings": {"calendar": {"about": "D'{{appName}} Kalennerintegratioun gëtt benotzt fir sécher Zougang zu Ärem Kalenner ze kréien, sou datt et zukünfteg Eventer ka liesen.", "disconnect": "<PERSON><PERSON><PERSON>", "microsoftSignIn": "<PERSON><PERSON><PERSON> mat <PERSON>", "signedIn": "Aktuell Zougang zu Kalennerevenementer fir {{email}}. <PERSON><PERSON><PERSON> op d'Disconnect Knäppchen hei ënnen fir opzehalen Kalennerevenementer.", "title": "<PERSON><PERSON><PERSON>"}, "desktopShareFramerate": "Desktop Deele Frame Taux", "desktopShareHighFpsWarning": "E méi héije Frame Taux fir Desktop Sharing kéint Är Bandbreedung beaflossen. Dir musst den Écran deelen nei starten fir datt déi nei Astellungen a Kraaft trieden.", "desktopShareWarning": "Dir musst den Écran deelen nei starten fir datt déi nei Astellungen a Kraaft trieden.", "devices": "Apparater", "followMe": "<PERSON><PERSON><PERSON> verfollegt mech", "framesPerSecond": "Frames pro Sekonn", "incomingMessage": "Entréeën Message", "language": "<PERSON><PERSON><PERSON><PERSON>", "languageSettings": "Sprooch Astellunge", "loggedIn": "<PERSON><PERSON><PERSON> als {{name}}", "microphones": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "moderator": "<PERSON><PERSON><PERSON>", "more": "<PERSON><PERSON><PERSON>", "name": "Numm", "noDevice": "<PERSON><PERSON>", "noLanguagesAvailable": "Keng <PERSON> verfügbar", "participantJoined": "Participant dobäi", "participantLeft": "Participant Lénks", "playSounds": "Spillt Toun op", "sameAsSystem": "Selwecht wéi System ({{label}})", "selectAudioOutput": "Audioausgang", "selectCamera": "<PERSON><PERSON><PERSON>", "selectLanguage": "<PERSON><PERSON>t Sprooch", "selectMic": "Mikrofon", "sounds": "Kläng", "speakers": "<PERSON><PERSON><PERSON><PERSON>", "startAudioMuted": "Jiddereen fänkt gedämpft", "startVideoMuted": "Jiddereen fänkt verstoppt", "talkWhileMuted": "Schwätzt während dem Mut", "title": "Astellungen"}, "settingsView": {"advanced": "Fortgeschratt", "alertCancel": "Ofbriechen", "alertOk": "OK", "alertTitle": "<PERSON><PERSON><PERSON>", "alertURLText": "Déi aginn Server URL ass ongëlteg", "buildInfoSection": "Bauen Informatiounen", "conferenceSection": "Konferenz", "disableCallIntegration": "Native Call Integratioun auszeschalten", "disableCrashReporting": "Desaktivéiere Crash Berichterstattung", "disableCrashReportingWarning": "Sidd Dir sécher datt Dir Crashberichterstattung auszeschalten wëllt? D'Astellung gëtt applizéiert nodeems Dir d'App nei starten.", "disableP2P": "Desaktivéiere Peer-To-<PERSON><PERSON>", "displayName": "Affich<PERSON>ier<PERSON>", "email": "E-Mail", "header": "Astellungen", "profileSection": "Profil", "serverURL": "Server URL", "showAdvanced": "Weisen fortgeschratt Astellungen", "startWithAudioMuted": "Fänkt mat Audio gedämpft un", "startWithVideoMuted": "Start mat Video Muted", "version": "Versioun"}, "share": {"dialInfoText": "=====\n\nWëllt Dir just op Ärem Telefon uruffen?\n\n{{defaultDialInNumber}}Klickt op dëse Link fir d'Telefonsnummere fir dës Versammlung ze gesinn\n{{dialInfoPageUrl}}", "mainText": "<PERSON>lickt op de folgende Link fir un der Versammlung deelzehuelen:\n{{roomUrl}}"}, "speaker": "<PERSON><PERSON><PERSON><PERSON>", "speakerStats": {"hours": "{{count}} h", "minutes": "{{count}} m", "name": "Numm", "seconds": "{{count}} s", "speakerStats": "Speaker <PERSON><PERSON><PERSON><PERSON><PERSON>", "speakerTime": "Speaker <PERSON><PERSON><PERSON>"}, "startupoverlay": {"genericTitle": "D'Versammlung muss Äre Mikrofon a Kamera benotzen.", "policyText": "", "title": "{{app}} muss <PERSON><PERSON> a Kamera ben<PERSON>."}, "suspendedoverlay": {"rejoinKeyTitle": "<PERSON><PERSON><PERSON> er<PERSON>m mat", "text": "<PERSON><PERSON><PERSON><PERSON> den <i>Rejoin</i> Knäppchen fir nei ze verbannen.", "title": "Äre Video Uruff gouf ënnerbrach well dëse Computer geschlof ass."}, "toolbar": {"Settings": "Astellungen", "Share": "<PERSON><PERSON>", "accessibilityLabel": {"Settings": "Toggle <PERSON>lungen", "audioOnly": "Toggle nëmmen Audio", "audioRoute": "Verwalte Sound Apparat", "boo": "Bu", "callQuality": "Managen Video Qualitéit", "carmode": "Auto Modus", "cc": "Toggle Ënnertitelen", "chat": "Chat op<PERSON> / zoumaachen", "clap": "<PERSON><PERSON><PERSON>", "collapse": "Zesummebroch", "document": "Toggle gedeelt Dokument", "donationCLP": "C & P Connect Astellungen", "donationLink": "DonorBox Astellungen", "download": "<PERSON>et eis <PERSON> erof", "embedMeeting": "<PERSON><PERSON>", "expand": "Erweideren", "feedback": "Verloossen Feedback", "fullScreen": "Toggle Vollbildschierm", "genericIFrame": "Toggle g<PERSON><PERSON>t Applikatioun", "giphy": "Toggle GIPHY Menu", "grantModerator": "Grant Moderator", "hangup": "Loosst d'Versammlung", "help": "<PERSON><PERSON><PERSON><PERSON>", "invite": "Leit invité<PERSON>", "kick": "Kick Participant", "laugh": "<PERSON><PERSON>", "leaveConference": "Verloossen Sitzung", "like": "<PERSON><PERSON>", "lobbyButton": "Aktivéiert / auszeschalten Lobby Modus", "localRecording": "Toggle lokal Opnam Kontrollen", "lockRoom": "Toggle <PERSON><PERSON><PERSON>", "moreActions": "<PERSON><PERSON><PERSON>", "moreActionsMenu": "<PERSON><PERSON><PERSON>", "moreOptions": "<PERSON><PERSON><PERSON> weisen", "mute": "Mute / netmutéieren", "muteEveryone": "<PERSON><PERSON> jidder<PERSON>", "muteEveryoneElse": "Mute all aner", "muteEveryoneElsesVideo": "Desaktivéiere jidderengem seng Ka<PERSON>a", "muteEveryonesVideo": "Desaktivéiere jidderengem seng Ka<PERSON>a", "participants": "Participanten", "party": "Partei Popper", "pip": "Toggle Bild-an-<PERSON><PERSON><PERSON>", "privateMessage": "Schéckt privat Noriicht", "profile": "Änneren Äre Profil", "raiseHand": "<PERSON><PERSON>t / senken Är Hand", "reactionsMenu": "Reaktiounsmenü opmaachen / zoumaachen", "recording": "Toggle Opname", "remoteMute": "Mute Participant", "remoteVideoMute": "Desaktivéiere Kamera vum Participant", "removeDonation": "Ewechzehuelen <PERSON>ox", "rmoveCDonation": "Ewechzehuelen C&P", "security": "Sécherheet Optiounen", "selectBackground": "<PERSON><PERSON><PERSON>", "shareRoom": "Invi<PERSON><PERSON>t een", "shareYourScreen": "Start / Stop deelen Ärem Écran", "shareaudio": "Deelen Audio", "sharedvideo": "Toggle YouTube Video Deele", "shortcuts": "Toggle <PERSON>", "show": "Show op der Bühn", "speakerStats": "Toggle Spriecher Statistiken", "surprised": "Iwwerrascht", "tileView": "Toggle <PERSON><PERSON><PERSON>", "toggleCamera": "<PERSON><PERSON><PERSON> w<PERSON>", "toggleFilmstrip": "Toggle Filmstrip", "toggleReactions": "<PERSON><PERSON>", "videoblur": "Toggle Video Blur", "videomute": "Start / Stop Kamera"}, "addPeople": "<PERSON><PERSON><PERSON><PERSON> Le<PERSON> op Ären Uruff", "audioOnlyOff": "Desaktivéiere Low-Bandwidth-Modus", "audioOnlyOn": "Aktivéiert niddereg bandwidth Modus", "audioRoute": "Verwalte Sound Apparat", "audioSettings": "Audio Astellunge", "authenticate": "Authentifizéieren", "boo": "<PERSON><PERSON><PERSON>", "callQuality": "Managen Video Qualitéit", "chat": "Chat op<PERSON> / zoumaachen", "clap": "<PERSON><PERSON><PERSON>", "closeChat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "closeParticipantsPane": "Zoumaachen Participanten Panel", "closeReactionsMenu": "<PERSON>ou<PERSON><PERSON><PERSON>", "disableNoiseSuppression": "Ka<PERSON>id<PERSON> Ënnerdréckung auszeschalten", "disableReactionSounds": "<PERSON>r kënnt Reaktiounskläng fir dës Versammlung auszeschalten", "documentClose": "Zoumaachen LivePad", "documentOpen": "<PERSON><PERSON>", "donationCLP": "C & P Connect Astellungen", "donationLink": "DonorBox Astellungen", "download": "<PERSON>et eis <PERSON> erof", "e2ee": "Enn-<PERSON>-<PERSON>n Verschlësselung", "embedMeeting": "<PERSON><PERSON>", "enterFullScreen": "View Vollbildmodus", "enterTileView": "<PERSON><PERSON>", "exitFullScreen": "<PERSON><PERSON><PERSON>bildschierm", "exitTileView": "Ausgang Fliesen <PERSON>", "feedback": "Verloossen Feedback", "genericIFrameClose": "Stop Whiteboard", "genericIFrameOpen": "<PERSON><PERSON>", "genericIFrameWeb": "Wäiss Board", "hangUpText": "Sidd Dir sécher datt Dir wëllt ophänken?", "hangUpforEveryOne": "Hangup fir <PERSON>", "hangUpforMe": "Hangup nëmme fir mech", "hangup": "<PERSON><PERSON><PERSON>", "help": "<PERSON><PERSON><PERSON><PERSON>", "hideReactions": "Verstoppen Reaktioun", "iOSStopScreenShareAlertMessage": "Stopp w.e.g. den Écran Deele virum HangUp.", "iOSStopScreenShareAlertTitle": "<PERSON><PERSON>ran <PERSON>", "invite": "Leit invité<PERSON>", "inviteViaCalendar": "Invitéieren iwwer <PERSON>", "laugh": "<PERSON><PERSON>", "leaveConference": "Verloossen Sitzung", "like": "<PERSON><PERSON>", "lobbyButtonDisable": "Desaktivéiere <PERSON>", "lobbyButtonEnable": "Aktiv<PERSON><PERSON><PERSON> Lobbymodus", "login": "Logbroon", "logout": "Ausloggen", "lowerYourHand": "Maacht Är Hand erof", "moreActions": "<PERSON><PERSON><PERSON>", "moreOptions": "<PERSON><PERSON><PERSON>", "mute": "Mute / netmutéieren", "muteEveryone": "<PERSON><PERSON> jidder<PERSON>", "muteEveryonesVideo": "Desaktivéiere jidderengem seng Ka<PERSON>a", "noAudioSignalDesc": "Wann Dir et net virsiichteg aus Systemastellungen oder Hardware mutéiert hutt, betruecht den Apparat ze wiesselen.", "noAudioSignalDescSuggestion": "Wann Dir et net virsiichteg aus Systemastellungen oder Hardware mutéiert hutt, betruecht op de proposéierten Apparat ze wiesselen.", "noAudioSignalDialInDesc": "<PERSON>r kënnt och uruffen mat:", "noAudioSignalDialInLinkDesc": "<PERSON><PERSON>", "noAudioSignalTitle": "Et kënnt keen Input vun Ärem Mikro!", "noisyAudioInputDesc": "Et kléngt wéi wann Ä<PERSON> Mi<PERSON>, betruecht w.e.g. den Apparat ze muten oder z'änneren.", "noisyAudioInputTitle": "<PERSON><PERSON> Mikro <PERSON> ze sinn!", "openChat": "Open Chat", "openReactionsMenu": "Open Reaktiounsmenü", "participants": "Participanten", "party": "<PERSON><PERSON>", "pip": "Bild am Bildmodus", "privateMessage": "Schéckt privat Noriicht", "profile": "Änneren Äre Profil", "raiseHand": "Erh<PERSON><PERSON><PERSON> / senken Är Hand", "raiseYourHand": "Huelt Är Hand op", "reactionBoo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reactionClap": "Schécken klappt Reaktioun", "reactionLaugh": "Schécken laachen Reaktioun", "reactionLike": "Schéckt Daumen Reaktioun", "reactionParty": "Schécken Partei Popper Reaktioun", "reactionSurprised": "Schéckt iwwerrascht Reaktioun", "removeDonation": "Ewechzehuelen <PERSON>ox", "rmoveCDonation": "Ewechzehuelen C&P", "security": "Sécherheet Optiounen", "selectBackground": "<PERSON><PERSON><PERSON>", "shareRoom": "Invi<PERSON><PERSON>t een", "shareaudio": "Deelen Audio", "sharedvideo": "<PERSON><PERSON>", "shortcuts": "View Ofkiirzungen", "showReactions": "Show Reaktioun", "speakerStats": "Speaker <PERSON><PERSON><PERSON><PERSON><PERSON>", "startScreenSharing": "<PERSON> <PERSON><PERSON><PERSON>", "startSubtitles": "Start Ënnertitelen", "stopAudioSharing": "Stop Audio Deele", "stopScreenSharing": "<PERSON><PERSON>", "stopSharedVideo": "Stop YouTube Video", "stopSubtitles": "Stop Ënnertitelen", "surprised": "Iwwerrascht", "talkWhileMutedPopup": "Probéieren ze schwätzen? Dir sidd gedämpft.", "tileViewToggle": "Toggle <PERSON><PERSON><PERSON>", "toggleCamera": "<PERSON><PERSON><PERSON> w<PERSON>", "videoSettings": "Video Astellungen", "videomute": "Start / Stop Kamera", "voiceCommand": "Oppen Stëmm Kommando", "whiteBoardOpen": "<PERSON><PERSON>", "zoomin": "Zoom an", "zoomout": "<PERSON><PERSON><PERSON><PERSON>"}, "transcribing": {"ClosedCaptions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LiveCaptions": "Live Untertitel", "NoCaptionsAvailable": "<PERSON><PERSON> verfügbare", "OpenCloseCaptions": "Open / z<PERSON><PERSON><PERSON>n <PERSON>", "Transcribing": "Transfcident", "TranscribingNotAvailable": "Transkribert net verfügbar", "TranscriptionLangDefaultNote": "NOTIZ: Transkriptioun ass net verfügbar an der gewielter Reuniounsprooch {{language}}}, also wäert Standard op Englesch.", "TranscriptionLanguageCannotBeChangedOngoingCall": "Transkriptiounsprooch kann net op engem lafenden Uruff geännert ginn. Frëndlech rejoin fir a Kraaft trieden.", "TranscriptionLanguageCantChange": "Transkriptiounsprooch kann 'll net änneren", "Transcriptions": "Transkriptiounen", "Translate": "Iwwersetzen", "TranslateTo": "Iwwersetze op", "Translating": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TranslationNotAvailable": "Iwwersetzung net verfügbar", "ccButtonTooltip": "Start / Stop Ënnertitelen", "error": "Transkriptioun gescheitert. Probéiert w.e.g. nach eng <PERSON>éier.", "expandedLabel": "Transkriptioun ass am Moment op", "failedToStart": "D'Transskriptioun konnt net ufänken", "labelToolTip": "D'Versammlung gëtt transkribéiert", "off": "Transkriptioun gestoppt", "pending": "Preparéiere fir d'Versammlung ze transkriptéieren ...", "sourceLanguageDesc": "De Moment ass d'Konferenzsprooch op {{sourceLanguage}} gesat", "sourceLanguageHere": "<PERSON>r kënnt et vun hei änneren", "start": "Start Ënnertitelen weisen", "stop": "Stop weisen Ënnertitelen", "subtitlesOff": "Aus", "subtitlesTitle": "Untertitel", "tr": "Drés", "transcriptionQuotaExceeded": "Transkriptiounsquote fir dëse Mount iwwerschratt", "transcriptionQuotaExceededTitle": "Transkriptiounsquote iwwerschratt"}, "userMedia": {"androidGrantPermissions": "<PERSON><PERSON>t <b><i><PERSON><PERSON><PERSON><PERSON></i></b> wann <PERSON><PERSON> Browser no Permissiounen freet.", "chromeGrantPermissions": "<PERSON><PERSON>t <b><i><PERSON><PERSON><PERSON><PERSON></i></b> wann <PERSON><PERSON> Browser no Permissiounen freet.", "edgeGrantPermissions": "<PERSON><PERSON>t <b><i><PERSON></i></b> wann <PERSON><PERSON> Browser no Permissiounen freet.", "electronGrantPermissions": "Gitt w.e.g. Erlaabnes fir Är Kamera a Mikrofon ze benotzen", "firefoxGrantPermissions": "Wielt <b><i><PERSON><PERSON> ausgewielten Apparat</i></b> wann Äre Browser no Permissiounen freet.", "iexplorerGrantPermissions": "Wielt <b><i>OK</i></b> wann Ä<PERSON> Browser no Permissiounen freet.", "nwjsGrantPermissions": "Gitt w.e.g. Erlaabnes fir Är Kamera a Mikrofon ze benotzen", "operaGrantPermissions": "<PERSON><PERSON>t <b><i><PERSON><PERSON><PERSON><PERSON></i></b> wann <PERSON><PERSON> Browser no Permissiounen freet.", "react-nativeGrantPermissions": "<PERSON><PERSON>t <b><i><PERSON><PERSON><PERSON><PERSON></i></b> wann <PERSON><PERSON> Browser no Permissiounen freet.", "safariGrantPermissions": "Wielt <b><i>OK</i></b> wann Ä<PERSON> Browser no Permissiounen freet."}, "videoSIPGW": {"busy": "Mir schaffen drun fir Ressourcen ze befreien. Probéiert w.e.g. nach eng Kéier an e puer Minutten.", "busyTitle": "De Roomservice ass am Moment beschäftegt", "errorAlreadyInvited": "{{displayName}} scho <PERSON><PERSON>t", "errorInvite": "Konferenz nach net etabléiert. Probéiert w.e.g. méi spéit nach eng Kéier.", "errorInviteFailed": "Mir schaffen un der Léisung vum Problem. Probéiert w.e.g. méi spéit nach eng Kéier.", "errorInviteFailedTitle": "D'Invitatioun vum {{displayName}} ass gescheitert", "errorInviteTitle": "Feeler beim <PERSON> vum Raum", "pending": "{{displayName}} gouf invi<PERSON>t"}, "videoStatus": {"audioOnly": "AUD an", "audioOnlyExpanded": "Dir sidd am Modus vun enger niddereger Bandbreedung. An dësem Modus kritt Dir nëmmen Audio an Écran Deele.", "callQuality": "Video Qualitéit", "hd": "HD", "hdTooltip": "<PERSON><PERSON><PERSON> kucken", "highDefinition": "<PERSON><PERSON><PERSON>", "labelTooiltipNoVideo": "<PERSON>e Video", "labelTooltipAudioOnly": "Low bandwidth Modus aktivéiert", "ld": "Ld", "ldTooltip": "<PERSON><PERSON><PERSON>", "lowDefinition": "<PERSON><PERSON><PERSON>", "onlyAudioAvailable": "Nëmmen Audio ass verfügbar", "onlyAudioSupported": "Mir ënnerstëtzen nëmmen Audio an dësem Browser.", "sd": "SD", "sdTooltip": "Standard Definitioun Video kucken", "standardDefinition": "Standard Definitioun", "uhd": "Uhd", "uhdTooltip": "Ultra High Definition Video kucken", "uhighDefinition": "Ultra High Definitioun"}, "videothumbnail": {"connectionInfo": "Verbindung Info", "domute": "Mute", "domuteOthers": "Mute all aner", "domuteVideo": "<PERSON><PERSON><PERSON>", "domuteVideoOfOthers": "Desaktivéiere Kamera vun all aneren", "flip": "<PERSON><PERSON>", "grantModerator": "Grant Moderator", "kick": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "moderator": "<PERSON><PERSON><PERSON>", "mute": "De Participant ass gedämpft", "muted": "<PERSON><PERSON><PERSON><PERSON>", "remoteControl": "Start / Stop Fernsteierung", "show": "Show op der Bühn", "videoMuted": "<PERSON><PERSON><PERSON>", "videomute": "De Participant huet d'Kamera gestoppt"}, "virtualBackground": {"addBackground": "Hintergrund derbäi", "appliedCustomImageTitle": "Eropgelueden Benotzerdefinéiert Bild", "apply": "<PERSON><PERSON><PERSON>", "blur": "Blour", "customImg": "Benotzerdefinéiert Bild", "deleteImage": "Bild läschen", "desktopShare": "Desktop deelen", "desktopShareError": "Konnt net Desktop Share erstellen", "enableBlur": "Blur aktivéieren", "image1": "Plage", "image2": "<PERSON><PERSON><PERSON> <PERSON>", "image3": "<PERSON><PERSON><PERSON> eidel <PERSON>", "image4": "<PERSON><PERSON><PERSON><PERSON>", "image5": "<PERSON><PERSON><PERSON>", "image6": "<PERSON><PERSON><PERSON>", "image7": "Sonnenopgang", "none": "<PERSON><PERSON>", "pleaseWait": "W.e.g. waart ...", "removeBackground": "Ewechze<PERSON><PERSON><PERSON>", "slightBlur": "Liicht Blur", "switchBackgroundTitle": "Sc<PERSON><PERSON> Han<PERSON>", "title": "<PERSON>irt<PERSON><PERSON>", "uploadedImage": "Eropgelueden Bild {{index}}", "virtualImagesTitle": "Virtuell Built-In <PERSON>", "webAssemblyWarning": "WebAssembly net ënnerstëtzt"}, "voicecommand": {"activePIPLabel": "Aktiv PIP", "clickOnMic": "<PERSON><PERSON>t op de Mic a Voice e Kommando", "hints": {"StopScreenSharing": "Stop Screen Sharing", "closeLivePad": "Zoumaachen LivePad", "closeWhiteboard": "Zoumaachen Whiteboard", "closeYoutube": "Youtube zoumaachen", "hints": "Hiweiser", "invitePeople": "Leit invité<PERSON>", "lowerHand": "Ënneschten Hand", "openChatBox": "Open Chat <PERSON>", "openClickAndPledge": "Open klickt a plädéieren", "openDonorbox": "Oppen Don<PERSON>", "openFullScreen": "Open Vollbildschierm", "openLivePad": "Open LivePad", "openLivestream": "Livestream opmaachen", "openParticipantPane": "Open Participant Panel", "openRecording": "Open Opbau", "openSettings": "Open Astellungen", "openSpeakerStats": "Open Speaker <PERSON><PERSON><PERSON><PERSON><PERSON>", "openVideoQualityDialog": "Open Video Qualitéit Dialog", "openVirtualBackground": "Open virtue<PERSON>", "openWhiteboard": "<PERSON><PERSON> Whiteboard", "openYoutube": "Youtube opmaachen", "raiseHand": "<PERSON><PERSON>t d'Hand op", "removeClickAndPledge": "Ewechzehuelen Klick a Verspriechen", "removeDonorbox": "Ewechzehuelen Donorbox", "startScreenSharing": "<PERSON> <PERSON><PERSON><PERSON>"}, "inActivePIPLabel": "An aktiv PIP", "pleaseWaitWeAreRecording": "Waart w.e.g. mir registréieren", "vcLabel": "Stëmm <PERSON>mmando", "voiceCommandForMeethour": "Stëmm Kommando Fir Liicht méi Zäit treffen"}, "volumeSlider": "<PERSON><PERSON>", "welcomepage": {"accessibilityLabel": {"join": "<PERSON><PERSON><PERSON> fir matze<PERSON>n", "roomname": "Gitt d'Versammlung ID"}, "addMeetingName": "<PERSON><PERSON><PERSON>gt V<PERSON>ammlungsnumm", "appDescription": "<PERSON><PERSON> vir, <PERSON><PERSON><PERSON> mat der ganzer Equipe. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, invi<PERSON><PERSON>t jiddereen deen Dir kennt. {{app}} ass eng komplett verschlësselte, 100% Open Source Videokonferenzléisung déi Di<PERSON>, all Dag, gratis benotze kënnt - ouni Konto néideg.", "audioVideoSwitch": {"audio": "<PERSON><PERSON><PERSON>", "video": "Videospiller"}, "calendar": "<PERSON><PERSON><PERSON>", "connectCalendarButton": "Connect <PERSON><PERSON>", "connectCalendarText": "Connect Äre Kalenner fir all Är Reuniounen an {{app}} ze gesinn. Plus, füügt {{provider}} Reuniounen un Äre Kalenner a fänkt se mat engem Klick un.", "developerPlan": "Entwéckler Plang", "enterRoomTitle": "Fänkt eng nei Versammlung un oder gitt bestehend Zëmmernumm", "enterprisePlan": "Enterprise Plang", "enterpriseSelfHostPlan": "Enterprise Selbst Hostplang", "features": "Fonctiounen", "footer": {"allRightsReserved": "All Rechter reservéiert", "androidAppDownload": "Android App Download", "apiDocumentation": "API Dokumentatioun", "app": "Appa", "blog": "Blog", "company": "Firma", "contact": "Kontakt", "copyright": "Copyright", "copyrightText": "Copyright 2020 - 2024 Liicht méi Zäit treffen LLC. All Rechter reservéiert", "developers": "Entwéckler", "disclaimer": "Verzichterklärung", "download": "<PERSON>ck erofgesat", "email": "E-Mail", "faqs": "Faqs", "followUs": "Foll<PERSON>t eis", "helpDesk": "<PERSON><PERSON><PERSON><PERSON>", "home": "<PERSON><PERSON><PERSON>", "iOSAppDownload": "iOS App Download eroflueden", "inTheNews": "An den Neiegkeeten", "integrations": "Integra<PERSON><PERSON><PERSON>", "knowledgeBase": "Wëssen Base", "meethour": "<PERSON><PERSON><PERSON><PERSON>", "meethourLLC": "Trefft Stonn LLC", "officeAddress": "8825 Stanford, Suite 205 Columbia, MD 21045", "phone": "Telefon", "privacyPolicy": "Privatsphär Politik", "productPresentation": "Produit <PERSON>", "refundCancellationPolicy": "Remboursement & Annulatiounspolitik", "termsConditions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "testimonials": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "webMobileSDK": "Web & Mobil SDK", "whoAreYou": "Wee bass du"}, "forHospitals": "<PERSON><PERSON>", "freeBannerDescription": "Gratis & Onlimitéiert HD Qualitéit Videokonferenz wéi ni virdrun. Maacht mat bei der Versammlung vun iwwerall.", "freePlan": "<PERSON><PERSON><PERSON>", "getHelp": "Faqs", "go": "<PERSON>rst<PERSON>t oder maacht mat bei enger Versammlung", "goSmall": "<PERSON>rst<PERSON>t oder maacht mat bei enger Versammlung", "header": {"accelerateDevelopmentWithPrebuiltSDKs": "Beschleunegt d'Entwécklung mat pre-built SDKs.", "apiStatus": "Api Status", "appointmentSchedulingVideoConference": "Rendez-vous & Videokonferenz.", "blog": "Blog", "customIntegrationDedicatedSupport": "Benotzerdefinéiert Integratioun & Engagéierten Ënnerstëtzung", "customTailoredVideoMeetings": "Benotzerdefinéiert Video Reuniounen.", "developer": "Entwéckler", "developers": "Entwéckler", "documentation": "Dokumentatioun", "eMail": "E-Mailen", "edTech": "<PERSON><PERSON>", "engagingOnlineLearningForEducators": "Engagéiert Online Léieren fir Educateuren.", "engagingVirtualEventExperiences": "Engagéiert virtuell Eventerfarungen.", "enterprise": "Entreprise", "enterpriseSelfHost": "Enterprise Selbst Host", "features": "Fonctiounen", "fitness": "Fitness", "free": "fräi", "fundraiseEffortlesslyWithinVideoConferences": "Spendenaktioun ouni Effort bannent Videokonferenzen.", "fundraisingDonate": "Spendenaktioun / Spendenaktioun", "fundraisingDonateOnline": "Spendenaktioun / Spenden online", "getStarted": "Fänkt un", "hdQualityVideoConferenceApp": "HD Qualitéit Video Konferenz App", "help": "<PERSON><PERSON><PERSON><PERSON>", "helpDesk": "Hëllef-Desk", "highQualityLiveEventStreaming": "Héich Qualitéit Live Event Streaming.", "hostVideoConferenceOnYourServers": "Host Videokonferenz op Äre Serveren.", "industries": "Industrien", "integrateVideoCallWithinYourWebsiteApp": "Integréiert Video Call an Ärer Websäit / App.", "interactiveVirtualLearningSolutions": "Interaktiv Virtuell Léierléisungen.", "joinAMeeting": "Maacht mat bei enger Versammlung", "knowledgeBase": "Wëssen Base", "liveStreaming": "Live Streaming", "meethour": "<PERSON><PERSON><PERSON><PERSON>", "myCaly": "<PERSON><PERSON>", "myCalyPricing": "MyCaly Präispolitik.", "mycaly": "Mycaly", "noAdsRecordingLiveStreaming": "Keng Annoncen + Opname + Live Streaming.", "noTimeLimitGroupCalls": "Keng Zäitlimit op 1: 1 & Grupp Uriff.", "preBuiltSDKs": "Pre-gebaut SDKS", "pricing": "Präisser", "pro": "<PERSON><PERSON>", "products": "Produiten", "resources": "Ressourcen", "scheduleADemo": "Plangt eng Demo", "simplifiedAPIReferences": "Vereinfacht API Referenzen", "smoothVideoOnboardingExperience": "Smooth Video Onboarding Experienz.", "solutions": "Léisungen", "stayuptodateWithOurBlog": "Bleift up-to-date mat eisem Blog", "systemHealthStatusandUpdates": "System Gesondheetsstatus an Updates", "tailoredSolutionsForYourHealthcareNeeds": "Mooss <PERSON>e fir Är Gesondheetsbedürfnisser.", "telehealth": "Telefonesch", "useCases": "Ben<PERSON><PERSON> Fäll", "videoConference": "Video Konferenz", "videoConferencePlans": "Video Konferenz Pläng", "videoConferencePricing": "Videokonferenz Präisser.", "videoConferencing": "Video Konferenzen", "videoKYC": "Videost-Kycc", "virtualClassrooms": "Virtuell Klassesäll", "virtualEvents": "<PERSON><PERSON><PERSON><PERSON>", "virtualSolutionForHomeFitness": "Virtuell Léisung fir Heem Fitness.", "webinarSessionsWithIndustryLeaders": "<PERSON><PERSON><PERSON> mat Industrieleit.", "webinars": "<PERSON><PERSON><PERSON>"}, "headerSubtitle": "Sécher an HD Qualitéit Reuniounen", "headerTitle": "<PERSON><PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON>", "invalidMeetingID": "Invalid Versammlung ID", "jitsiOnMobile": "Trefft Hour um Handy - luet eis Apps erof a start eng Versammlung iwwerall", "join": "<PERSON><PERSON><PERSON><PERSON> / matmaachen", "joinAMeeting": "Maacht mat bei enger Versammlung", "logo": {"calendar": "<PERSON><PERSON><PERSON> logo", "desktopPreviewThumbnail": "Desktop Preview Miniatur", "googleLogo": "Googleo méi", "logoDeepLinking": "Jitsi treffe Logo", "microsoftLogo": "Microsoft Logo", "policyLogo": "Politik logo"}, "meetingDate": "<PERSON><PERSON><PERSON><PERSON>", "meetingDetails": "<PERSON><PERSON><PERSON><PERSON>", "meetingIsReady": "<PERSON>ers<PERSON><PERSON><PERSON> ass prett", "mobileDownLoadLinkAndroid": "Download Handy App fir Android", "mobileDownLoadLinkFDroid": "Download Handy App fir F-Droid", "mobileDownLoadLinkIos": "Download mobil App fir iOS", "moderatedMessage": "Oder <a href=\"{{url}}\" rel=\"noopener noreferrer\" target=\"_blank\">Bucht eng Versammlungs-URL am Viraus</a> wou Dir deen eenzege Moderator sidd.", "oopsDeviceClockorTimezoneErr": "Oops! Eppes ass falsch gaangen. Vergewëssert Iech datt Ären Apparat Auer / Zäitzone korrekt ass", "oopsThereSeemsToBeProblem": "O<PERSON>, et schéngt e Problem ze sinn", "pleaseWaitForTheStartMeeting": "Waart w.e.g. op de {{moderator}} fir dës Versammlung unzefänken.", "preRegistrationMsg": "Dës Versammlung erfuerdert Pre-Umeldung. Registréiert Iech selwer am Browser a gitt zréck vum invitéierten E-Mail Link.", "pricing": "Präisser", "privacy": "Privatsphär", "privateMeetingErr": "Dir schéngt eng privat Versammlung matzemaachen. Gitt w.e.g. mat<PERSON> andeems Dir <PERSON><PERSON> ugemellt hutt mat der invitéierter E-Mailadress.", "proPlan": "proPlan", "recentList": "Rezent", "recentListDelete": "Läschen Entrée", "recentListEmpty": "Är rezent Lëscht ass am Moment eidel. Chat mat Ärem Team an Dir fannt all Är rezent Reuniounen hei.", "reducedUIText": "Wëllkomm op {{app}}!", "registerNow": "Registréiert Iech elo", "roomNameAllowedChars": "Versammlungsnumm däerf keng vun dësen Zeechen enthalen: ?, &, :, ', \", %, #.", "roomname": "Gitt d'Versammlung ID", "roomnameHint": "<PERSON><PERSON> den Numm oder d'URL vum Raum un, deen <PERSON>r wëllt mat<PERSON>. <PERSON>r kënnt en Numm maachen, lo<PERSON>t just d'<PERSON>, d<PERSON><PERSON><PERSON>, et <PERSON><PERSON><PERSON>, fir datt se deeselwechten Numm aginn.", "scheduleAMeeting": "Plangt eng Versammlung", "sendFeedback": "Feed<PERSON> s<PERSON>n", "shiftingVirtualMeetToReality": "Verännert Virtuell Meet To Reality", "solutions": "Léisungen", "startMeeting": "Start Sëtzung", "terms": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timezone": "<PERSON><PERSON><PERSON><PERSON>", "title": "100% <PERSON><PERSON><PERSON>, <PERSON> zu <PERSON>, an HD Qualitéit Videokonferenz Léisung", "tryNowItsFree": "<PERSON><PERSON><PERSON><PERSON><PERSON> elo, et ass gratis", "waitingInLobby": "Waarden an der Lobby. {{moderator}} léisst Iech geschwënn eran.", "youtubeHelpTutorial": "YouTube H<PERSON><PERSON>f <PERSON>"}}