/* global __dirname */

const CircularDependencyPlugin = require('circular-dependency-plugin');
const fs = require('fs');
const { join } = require('path');
const process = require('process');
const webpack = require('webpack');
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');

/**
 * The URL of the Jitsi Meet deployment to be proxy to in the context of
 * development with webpack-dev-server.
 */
const devServerProxyTarget
    = process.env.WEBPACK_DEV_SERVER_PROXY_TARGET || 'https://meethour.org'; // Use it for Development

/** .........
 * Build a Performance configuration object for the given size.
 * See: https://webpack.js.org/configuration/performance/.
 *
 * @param {Object} options - Options for the bundles configuration.
 * @param {boolean} options.analyzeBundle - Whether the bundle needs to be analyzed for size.
 * @param {boolean} options.isProduction - Whether this is a production build or not.
 * @param {number} size - The size limit to apply.
 * @returns {Object} - A performance hints object.
 */
function getPerformanceHints(options, size) {
    const { analyzeBundle, isProduction } = options;

    return {
        hints: isProduction && !analyzeBundle ? 'error' : false,
        maxAssetSize: size,
        maxEntrypointSize: size
    };
}

/**
 * Build a BundleAnalyzerPlugin plugin instance for the given bundle name.
 *
 * @param {boolean} analyzeBundle - Whether the bundle needs to be analyzed for size.
 * @param {string} name - The name of the bundle.
 * @returns {Array} A configured list of plugins.
 */
function getBundleAnalyzerPlugin(analyzeBundle, name) {
    if (!analyzeBundle) {
        return [];
    }

    return [
        new BundleAnalyzerPlugin({
            analyzerMode: 'disabled',
            generateStatsFile: true,
            statsFilename: `${name}-stats.json`
        })
    ];
}

/** .
 * Determines whether a specific (HTTP) request is to bypass the proxy of
 * webpack-dev-server (i.e. Is to be handled by the proxy target) and, if not,
 * which local file is to be served in response to the request.
 *
 * @param {Object} request - The (HTTP) request received by the proxy.
 * @returns {string|undefined} If the request is to be served by the proxy
 * target, undefined; otherwise, the path to the local file to be served.
 */
function devServerProxyBypass({ path }) {
    if (
        path.startsWith('/css/')
        || path.startsWith('/doc/')
        || path.startsWith('/fonts/')
        || path.startsWith('/images/')
        || path.startsWith('/lang/')
        || path.startsWith('/sounds/')
        || path.startsWith('/static/')
        || path.endsWith('.wasm')
        || path.endsWith('lib-meet-hour.min.js')
        || path.endsWith('.wasm')
    ) {
        return path;
    }

    if (path.startsWith('/libs/')) {
        if (
            path.endsWith('.min.js')
            && !fs.existsSync(join(process.cwd(), path))
        ) {
            return path.replace('.min.js', '.js');
        }

        return path;
    }
}

/**
 * The base Webpack configuration to bundle the JavaScript artifacts of
 * jitsi-meet such as app.bundle.js and external_api.js.
 *
 * @param {Object} options - Options for the bundles configuration.
 * @param {boolean} options.detectCircularDeps - Whether to detect circular dependencies or not.
 * @param {boolean} options.isProduction - Whether this is a production build or not.
 * @returns {Object} The base config object.
 */
function getConfig(options = {}) {
    const { detectCircularDeps, isProduction } = options;

    return {
        devtool: isProduction ? 'source-map' : 'eval-source-map',
        mode: isProduction ? 'production' : 'development',
        module: {
            rules: [
                {
                    // Transpile ES2015 (aka ES6) to ES5. Accept the JSX syntax by React
                    // as well.

                    loader: 'babel-loader',
                    options: {
                        // Avoid loading babel.config.js, since we only use it for React Native.
                        configFile: false,

                        // XXX The require.resolve below solves failures to locate the
                        // presets when lib-meet-hour, for example, is npm linked in
                        // jitsi-meet.
                        plugins: [
                            require.resolve(
                                '@babel/plugin-proposal-export-default-from'
                            )
                        ],
                        presets: [
                            [
                                require.resolve('@babel/preset-env'),

                                // Tell babel to avoid compiling imports into CommonJS
                                // so that webpack may do tree shaking.
                                {
                                    modules: false,

                                    // Specify our target browsers so no transpiling is
                                    // done unnecessarily. For browsers not specified
                                    // here, the ES2015+ profile will be used.
                                    targets: {
                                        chrome: 80,
                                        electron: 10,
                                        firefox: 68,
                                        safari: 14
                                    }
                                }
                            ],
                            require.resolve('@babel/preset-flow'),
                            require.resolve('@babel/preset-react')
                        ]
                    },
                    test: /\.jsx?$/
                },

                // {
                //     test: /\.jsx?$/,
                //     exclude: /node_modules/, // Ensure node_modules are excluded
                //     loader: "babel-loader",
                //     options: {
                //         configFile: false,
                //         plugins: [
                //             require.resolve(
                //                 "@babel/plugin-proposal-export-default-from"
                //             ),
                //         ],
                //         presets: [
                //             [
                //                 require.resolve("@babel/preset-env"),
                //                 {
                //                     modules: false,
                //                     targets: {
                //                         chrome: 80,
                //                         electron: 10,
                //                         firefox: 68,
                //                         safari: 14,
                //                     },
                //                 },
                //             ],
                //             require.resolve("@babel/preset-flow"),
                //             require.resolve("@babel/preset-react"),
                //         ],
                //     },
                // },
                {
                    // Allow CSS to be imported into JavaScript.

                    test: /\.css$/,
                    use: [ 'style-loader', 'css-loader' ]
                },
                {
                    test: /\/node_modules\/@atlaskit\/modal-dialog\/.*\.js$/,
                    resolve: {
                        alias: {
                            'react-focus-lock': `${__dirname}/react/features/base/util/react-focus-lock-wrapper.js`,
                            '../styled/Modal': `${__dirname}/react/features/base/dialog/components/web/ThemedDialog.js`
                        }
                    }
                },
                {
                    test: /\/react\/features\/base\/util\/react-focus-lock-wrapper.js$/,
                    resolve: {
                        alias: {
                            'react-focus-lock': `${__dirname}/node_modules/react-focus-lock`
                        }
                    }
                },
                {
                    test: /\.svg$/,
                    use: [
                        {
                            loader: '@svgr/webpack',
                            options: {
                                dimensions: false,
                                expandProps: 'start'
                            }
                        }
                    ]
                },

                {
                    test: /\.tsx?$/,
                    exclude: [ /node_modules/, /\.js$/ ], // Exclude node_modules and .js files
                    use: {
                        loader: 'ts-loader',
                        options: {
                            configFile: 'tsconfig.json',
                            transpileOnly: true // Skip type checking for dev builds.
                        }
                    }
                }
            ]
        },
        node: {
            // Allow the use of the real filename of the module being executed. By
            // default Webpack does not leak path-related information and provides a
            // value that is a mock (/index.js).
            __filename: true
        },
        optimization: {
            concatenateModules: isProduction,
            minimize: isProduction
        },
        output: {
            filename: `[name]${isProduction ? '.min' : ''}.js`,
            path: `${__dirname}/build`,
            publicPath: '/libs/',
            sourceMapFilename: '[file].map'
        },
        plugins: [
            detectCircularDeps
                && new CircularDependencyPlugin({
                    allowAsyncCycles: false,
                    exclude: /node_modules/,
                    failOnError: false
                })
        ].filter(Boolean),
        resolve: {
            alias: {
                'focus-visible': 'focus-visible/dist/focus-visible.min.js'
            },
            aliasFields: [ 'browser' ],
            extensions: [
                '.web.js',
                '.web.ts',

                // Typescript:
                '.tsx',
                '.ts',

                // Webpack defaults:
                '.js',
                '.json'
            ],
            fallback: {
                // Provide some empty Node modules (required by AtlasKit, olm).
                crypto: false,
                fs: false,
                path: false,
                process: false
            }
        }
    };
}

/** .........
 * Helper function to build the dev server config. It's necessary to split it in
 * Webpack 5 because only one devServer entry is supported, so we attach it to
 * the main bundle.
 *

 * @returns {Object} the dev server configuration.
 */
function getDevServerConfig() {
    return {
        client: {
            overlay: {
                errors: true,
                warnings: false
            }
        },
        host: 'localhost',
        port: 3050,
        hot: true,
        proxy: {
            '/conf': {
                target: devServerProxyTarget,
                onProxyReq(proxyReq, req, res) {
                    // Modify the request URL if it matches '/conf'
                    if (req.url.startsWith('/conf/')) {
                        res.writeHead(301, {
                            Location: req.url.replace('/conf/', '/')
                        });
                        res.end();
                    }
                },
                secure: false,
                headers: {
                    Host: new URL(devServerProxyTarget).host
                }
            },
            '/': {
                bypass: devServerProxyBypass,
                secure: false,
                target: devServerProxyTarget,
                headers: {
                    Host: new URL(devServerProxyTarget).host
                }
            }
        },
        server: 'https',
        static: {
            directory: process.cwd()
        }
    };
}

module.exports = (_env, argv) => {
    const analyzeBundle = Boolean(process.env.ANALYZE_BUNDLE);
    const mode = typeof argv.mode === 'undefined' ? 'production' : argv.mode;
    const isProduction = mode === 'production';
    const configOptions = {
        detectCircularDeps: Boolean(process.env.DETECT_CIRCULAR_DEPS),
        isProduction
    };
    const config = getConfig(configOptions);
    const perfHintOptions = {
        analyzeBundle,
        isProduction
    };

    return [
        Object.assign({}, config, {
            entry: {
                'app.bundle': './app.js'
            },
            devServer: isProduction ? {} : getDevServerConfig(),
            plugins: [
                ...config.plugins,
                ...getBundleAnalyzerPlugin(analyzeBundle, 'app'),
                new webpack.IgnorePlugin({
                    resourceRegExp: /^canvas$/,
                    contextRegExp: /resemblejs$/
                }),
                new webpack.IgnorePlugin({
                    resourceRegExp: /^\.\/locale$/,
                    contextRegExp: /moment$/
                }),
                new webpack.ProvidePlugin({
                    process: 'process/browser'
                })
            ],

            performance: getPerformanceHints(perfHintOptions, 5 * 1024 * 1024)
        }),
        Object.assign({}, config, {
            entry: {
                device_selection_popup_bundle:
                    './react/features/settings/popup.js'
            },
            performance: getPerformanceHints(950 * 1024)
        }),
        Object.assign({}, config, {
            entry: {
                alwaysontop: './react/features/always-on-top/index.js'
            },
            plugins: [
                ...config.plugins,
                ...getBundleAnalyzerPlugin(analyzeBundle, 'alwaysontop')
            ],
            performance: getPerformanceHints(perfHintOptions, 1800 * 1024)
        }),
        Object.assign({}, config, {
            entry: {
                dial_in_info_bundle:
                    './react/features/invite/components/dial-in-info-page'
            },
            plugins: [
                ...config.plugins,
                ...getBundleAnalyzerPlugin(analyzeBundle, 'dial_in_info'),
                new webpack.IgnorePlugin({
                    resourceRegExp: /^\.\/locale$/,
                    contextRegExp: /moment$/
                })
            ],
            performance: getPerformanceHints(perfHintOptions, 600 * 1024)
        }),
        Object.assign({}, config, {
            entry: {
                do_external_connect:
                    './connection_optimization/do_external_connect.js'
            },
            plugins: [
                ...config.plugins,
                ...getBundleAnalyzerPlugin(
                    analyzeBundle,
                    'do_external_connect'
                )
            ],
            performance: getPerformanceHints(perfHintOptions, 500 * 1024)
        }),
        Object.assign({}, config, {
            entry: {
                flacEncodeWorker:
                    './react/features/local-recording/recording/flac/flacEncodeWorker.js'
            },
            performance: getPerformanceHints(6 * 1024)
        }),
        Object.assign({}, config, {
            entry: {
                'analytics-ga':
                    './react/features/analytics/handlers/GoogleAnalyticsHandler.js'
            },
            performance: getPerformanceHints(29 * 1024)
        }),
        Object.assign({}, config, {
            entry: {
                'donorbox-script': './donorbox-script.js'
            },
            performance: getPerformanceHints(5 * 1024)
        }),
        Object.assign({}, config, {
            entry: {
                close3: './static/close3.js'
            },
            plugins: [
                ...config.plugins,
                ...getBundleAnalyzerPlugin(analyzeBundle, 'close3')
            ],
            performance: getPerformanceHints(perfHintOptions, 128 * 1024)
        }),

        // Because both virtual-background-effect and rnnoise-processor modules are loaded
        // in a lazy manner using the loadScript function with a hard coded name,
        // i.e.loadScript('libs/rnnoise-processor.min.js'), webpack dev server
        // won't know how to properly load them using the default config filename
        // and sourceMapFilename parameters which target libs without .min in dev
        // mode. Thus we change these modules to have the same filename in both
        // prod and dev mode.
        Object.assign({}, config, {
            entry: {
                'virtual-background-effect':
                    './react/features/stream-effects/virtual-background/index.js'
            },
            output: Object.assign({}, config.output, {
                library: [ 'MeetHourJS', 'app', 'effects' ],
                libraryTarget: 'window',
                filename: '[name].min.js',
                sourceMapFilename: '[name].min.map'
            }),
            performance: getPerformanceHints(1 * 1024 * 1024)
        }),

        Object.assign({}, config, {
            entry: {
                'rnnoise-processor':
                    './react/features/stream-effects/rnnoise/index.js'
            },
            output: Object.assign({}, config.output, {
                library: [ 'MeetHourJS', 'app', 'effects', 'rnnoise' ],
                libraryTarget: 'window',
                filename: '[name].min.js',
                sourceMapFilename: '[name].min.map'
            }),
            performance: getPerformanceHints(30 * 1024)
        }),

        Object.assign({}, config, {
            entry: {
                external_api: './modules/API/external/index.js'
            },
            output: Object.assign({}, config.output, {
                library: 'MeetHourExternalAPI',
                libraryTarget: 'umd'
            }),
            performance: getPerformanceHints(60 * 1024)
        })
    ];
};
