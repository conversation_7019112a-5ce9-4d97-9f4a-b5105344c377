import groovy.json.JsonSlurper
import org.gradle.util.VersionNumber

// Top-level build file where you can add configuration options common to all
// sub-projects/modules.

buildscript {
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath('com.android.tools.build:gradle:8.3.2')
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath 'com.google.gms:google-services:4.4.2'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.2'
    }
}

ext {
    kotlinVersion = "1.9.22"
    buildToolsVersion = "34.0.0"
    compileSdkVersion = 34
    minSdkVersion    = 23
    targetSdkVersion = 34
    supportLibVersion = "28.0.0"

    // We use NDK 23 which has both M1 support and is the side-by-side NDK version from AGP.
    ndkVersion = "25.1.8937393"

    // The Maven artifact groupdId of the third-party react-native modules which
    // Meet Hour SDK for Android depends on and which are not available in
    // third-party Maven repositories so we have to deploy to a Maven repository
    // of ours.
    moduleGroupId = 'com.facebook.react'

    // Maven repo where artifacts will be published
    mavenRepo = System.env.MVN_REPO ?: ""
    mavenUser = System.env.MVN_USER ?: ""
    mavenPassword = System.env.MVN_PASSWORD ?: ""

    // Libre build
    libreBuild = (System.env.LIBRE_BUILD ?: "false").toBoolean()

    googleServicesEnabled = project.file('app/google-services.json').exists() && !libreBuild
}

allprojects {
    repositories {
        google()
        mavenCentral()
        // React Native (JS, Obj-C sources, Android binaries) is installed from npm.
        maven { url "$rootDir/../node_modules/react-native/android" }
        // Android JSC is installed from npm.
        maven { url("$rootDir/../node_modules/jsc-android/dist") }
    }

    // Make sure we use the react-native version in node_modules and not the one
    // published in jcenter / elsewhere.
    configurations.configureEach {
        resolutionStrategy {
            eachDependency { DependencyResolveDetails details ->
                if (details.requested.group == 'com.facebook.react'
                        && details.requested.name == 'react-native') {
                    def file = new File("$rootDir/../node_modules/react-native/package.json")
                    def version = new JsonSlurper().parseText(file.text).version
                    details.useVersion version
                }
            }
        }
    }

    // Third-party react-native modules which Meet Hour SDK for Android depends
    // on and which are not available in third-party Maven repositories need to
    // be deployed in a Maven repository of ours.
    //

    if (project.name.startsWith('react-native-')) {
        apply plugin: 'maven-publish'
        publishing {
            publications {}
            repositories {
                maven {
                    url rootProject.ext.mavenRepo
                    if (!rootProject.ext.mavenRepo.startsWith("file")) {
                        credentials {
                            username rootProject.ext.mavenUser
                            password rootProject.ext.mavenPassword
                        }
                    }
                }
            }
        }
    }

    // Use the number of seconds/10 since Jan 1 2019 as the version qualifier number.
    // This will last for the next ~680 years.
    // https://stackoverflow.com/a/38643838
    def versionQualifierNumber = (int)(((new Date().getTime()/1000) - 1546297200) / 10)

    afterEvaluate { project ->
        if (project.plugins.hasPlugin('android') || project.plugins.hasPlugin('android-library')) {
            project.android {
                compileSdkVersion rootProject.ext.compileSdkVersion
                buildToolsVersion rootProject.ext.buildToolsVersion
            }
        }

        if (project.name.startsWith('react-native-')) {
            def npmManifest = project.file('../package.json')
            def json = new JsonSlurper().parseText(npmManifest.text)

            // Release every dependency the SDK has with a -jitsi-XXX qualified version. This allows
            // us to pin the dependencies and make sure they are always updated, no matter what.

            project.version = "${json.version}-meethour-${versionQualifierNumber}"

            tasks.register('androidSourcesJar', Jar) {
                archiveClassifier = 'sources'
                from android.sourceSets.main.java.source
            }

            publishing.publications {
                aarArchive(MavenPublication) {
                    groupId rootProject.ext.moduleGroupId
                    artifactId project.name
                    version project.version

                    artifact("${project.buildDir}/outputs/aar/${project.name}-release.aar") {
                        extension "aar"
                    }
                    artifact(androidSourcesJar)
                    pom.withXml {
                        def pomXml = asNode()
                        pomXml.appendNode('name', project.name)
                        pomXml.appendNode('description', json.description)
                        pomXml.appendNode('url', json.homepage)
                        if (json.license) {
                            def license = pomXml.appendNode('licenses').appendNode('license')
                            license.appendNode('name', json.license)
                            license.appendNode('distribution', 'repo')
                        }

                        def dependencies = pomXml.appendNode('dependencies')
                        configurations.getByName('releaseCompileClasspath').getResolvedConfiguration().getFirstLevelModuleDependencies().each {
                            def artifactId = it.moduleName
                            def version = it.moduleVersion
                            // React Native signals breaking changes by
                            // increasing the minor version number. So the
                            // (third-party) React Native modules we utilize can
                            // depend not on a specific react-native release but
                            // a wider range.
                            if (artifactId == 'react-native') {
                                def versionNumber = VersionNumber.parse(version)
                                version = "${versionNumber.major}.${versionNumber.minor}"
                            }

                            def dependency = dependencies.appendNode('dependency')
                            dependency.appendNode('groupId', it.moduleGroup)
                            dependency.appendNode('artifactId', artifactId)
                            dependency.appendNode('version', version)
                        }
                    }
                }
            }
        }
    }
}

// Force the version of the Android build tools we have chosen on all
// subprojects. The forcing was introduced for react-native and the third-party
// modules that we utilize such as react-native-background-timer.
subprojects { subproject ->
    afterEvaluate {
        if ((subproject.plugins.hasPlugin('android')
                    || subproject.plugins.hasPlugin('android-library'))
                && rootProject.ext.has('buildToolsVersion')) {
            android {
                buildToolsVersion rootProject.ext.buildToolsVersion
            }
        }

    }

    }
