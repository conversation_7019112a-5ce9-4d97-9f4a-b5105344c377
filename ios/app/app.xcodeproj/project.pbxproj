// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		0B412F1F1EDEE6E800B1A0A6 /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0B412F1E1EDEE6E800B1A0A6 /* ViewController.m */; };
		0B412F211EDEE95300B1A0A6 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 0B412F201EDEE95300B1A0A6 /* Main.storyboard */; };
		0B5418471F7C5D8C00A2DD86 /* MeetingRowController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B5418461F7C5D8C00A2DD86 /* MeetingRowController.swift */; };
		0B7001701F7C51CC005944F4 /* InCallController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B70016F1F7C51CC005944F4 /* InCallController.swift */; };
		0BEA5C291F7B8F73000D0AB4 /* Interface.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 0BEA5C271F7B8F73000D0AB4 /* Interface.storyboard */; };
		0BEA5C2B1F7B8F73000D0AB4 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 0BEA5C2A1F7B8F73000D0AB4 /* Assets.xcassets */; };
		0BEA5C321F7B8F73000D0AB4 /* Meet Hour Companion Extension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 0BEA5C311F7B8F73000D0AB4 /* Meet Hour Companion Extension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		0BEA5C371F7B8F73000D0AB4 /* InterfaceController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BEA5C361F7B8F73000D0AB4 /* InterfaceController.swift */; };
		0BEA5C391F7B8F73000D0AB4 /* ExtensionDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BEA5C381F7B8F73000D0AB4 /* ExtensionDelegate.swift */; };
		0BEA5C3B1F7B8F73000D0AB4 /* ComplicationController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BEA5C3A1F7B8F73000D0AB4 /* ComplicationController.swift */; };
		0BEA5C3D1F7B8F73000D0AB4 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 0BEA5C3C1F7B8F73000D0AB4 /* Assets.xcassets */; };
		0BEA5C411F7B8F73000D0AB4 /* Meet Hour.app in Embed Watch Content */ = {isa = PBXBuildFile; fileRef = 0BEA5C251F7B8F73000D0AB4 /* Meet Hour.app */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.m */; };
		13B07FBD1A68108700A75B9A /* LaunchScreen.xib in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB11A68108700A75B9A /* LaunchScreen.xib */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		190B230525FF7DCB002F500F /* MeetHourSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 190B22F925FF7A95002F500F /* MeetHourSDK.framework */; };
		190B230625FF7DCB002F500F /* MeetHourSDK.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 190B22F925FF7A95002F500F /* MeetHourSDK.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		19318E6C254B465400D67E9B /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 19318E6B254B465400D67E9B /* GoogleService-Info.plist */; };
		19590A662550492100842FA6 /* GraphicRectangle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 19590A612550492100842FA6 /* GraphicRectangle.swift */; };
		19590A672550492100842FA6 /* GraphicCorner.swift in Sources */ = {isa = PBXBuildFile; fileRef = 19590A622550492100842FA6 /* GraphicCorner.swift */; };
		19590A682550492100842FA6 /* GraphicCircular.swift in Sources */ = {isa = PBXBuildFile; fileRef = 19590A632550492100842FA6 /* GraphicCircular.swift */; };
		19590A692550492100842FA6 /* Utilities.swift in Sources */ = {isa = PBXBuildFile; fileRef = 19590A642550492100842FA6 /* Utilities.swift */; };
		19590A6A2550492100842FA6 /* GraphicBezel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 19590A652550492100842FA6 /* GraphicBezel.swift */; };
		19590A6F2550493100842FA6 /* TemplateConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 19590A6D2550493100842FA6 /* TemplateConfiguration.swift */; };
		19590A702550493100842FA6 /* Timeline.swift in Sources */ = {isa = PBXBuildFile; fileRef = 19590A6E2550493100842FA6 /* Timeline.swift */; };
		4E51B75E25E4115F0038575A /* DarwinNotificationCenter.m in Sources */ = {isa = PBXBuildFile; fileRef = 4E51B75D25E4115F0038575A /* DarwinNotificationCenter.m */; };
		4EC49BB725BEDAC100E76218 /* ReplayKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4EC49B8625BED71300E76218 /* ReplayKit.framework */; };
		4EC49BBB25BEDAC100E76218 /* SampleHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = 4EC49BBA25BEDAC100E76218 /* SampleHandler.m */; };
		4EC49BBF25BEDAC100E76218 /* MeetHour_Broadcast_Extension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 4EC49BB625BEDAC100E76218 /* MeetHour_Broadcast_Extension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		4EC49BCB25BEDB6400E76218 /* SocketConnection.m in Sources */ = {isa = PBXBuildFile; fileRef = 4EC49BCA25BEDB6400E76218 /* SocketConnection.m */; };
		4EC49BD125BF19CF00E76218 /* SampleUploader.m in Sources */ = {isa = PBXBuildFile; fileRef = 4EC49BD025BF19CF00E76218 /* SampleUploader.m */; };
		69A7789F262448AC004DFE67 /* WebRTC.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 69A7789E262448AC004DFE67 /* WebRTC.xcframework */; };
		69A778A0262448AC004DFE67 /* WebRTC.xcframework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 69A7789E262448AC004DFE67 /* WebRTC.xcframework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		6C91A67AD2F0F20F904FFB9D /* libPods-meet-hour.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 60A59BDF420147ECDBB1C9B8 /* libPods-meet-hour.a */; };
		842EDC0E2DC1244D00F7B1C7 /* JavaScriptCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 842EDC082DC121E000F7B1C7 /* JavaScriptCore.framework */; };
		84B537152D033CE10029852B /* hermes.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 84B537142D033CE10029852B /* hermes.xcframework */; };
		84B537162D033CE10029852B /* hermes.xcframework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 84B537142D033CE10029852B /* hermes.xcframework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		A0413E562CB6DAC30083D38E /* GiphyUISDK.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = A0413E552CB6DAC30083D38E /* GiphyUISDK.xcframework */; };
		A0413E572CB6DAC30083D38E /* GiphyUISDK.xcframework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = A0413E552CB6DAC30083D38E /* GiphyUISDK.xcframework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		A08C06C52C820AE2004DE694 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = A08C06C42C820AE2004DE694 /* PrivacyInfo.xcprivacy */; };
		DE4C456121DE1E4E00EA0709 /* FIRUtilities.m in Sources */ = {isa = PBXBuildFile; fileRef = DE4C455F21DE1E4E00EA0709 /* FIRUtilities.m */; };
		E588011722789D43008B0561 /* MeetHourContext.swift in Sources */ = {isa = PBXBuildFile; fileRef = E58801132278944E008B0561 /* MeetHourContext.swift */; };
		E5C97B63227A1EB400199214 /* MeetHourCommands.swift in Sources */ = {isa = PBXBuildFile; fileRef = E5C97B62227A1EB400199214 /* MeetHourCommands.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		0BEA5C331F7B8F73000D0AB4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0BEA5C301F7B8F73000D0AB4;
			remoteInfo = Meet_Hour_Companion_Extension;
		};
		0BEA5C3F1F7B8F73000D0AB4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0BEA5C241F7B8F73000D0AB4;
			remoteInfo = MeetHourCompanion;
		};
		4EC49BBD25BEDAC100E76218 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 4EC49BB525BEDAC100E76218;
			remoteInfo = MeetHour_Broadcast_Extension;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		0B26BE701EC5BC3C00EEFB41 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				69A778A0262448AC004DFE67 /* WebRTC.xcframework in Embed Frameworks */,
				84B537162D033CE10029852B /* hermes.xcframework in Embed Frameworks */,
				A0413E572CB6DAC30083D38E /* GiphyUISDK.xcframework in Embed Frameworks */,
				190B230625FF7DCB002F500F /* MeetHourSDK.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		0BEA5C471F7B8F73000D0AB4 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				0BEA5C321F7B8F73000D0AB4 /* Meet Hour Companion Extension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
		0BEA5C491F7B8F73000D0AB4 /* Embed Watch Content */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "$(CONTENTS_FOLDER_PATH)/Watch";
			dstSubfolderSpec = 16;
			files = (
				0BEA5C411F7B8F73000D0AB4 /* Meet Hour.app in Embed Watch Content */,
			);
			name = "Embed Watch Content";
			runOnlyForDeploymentPostprocessing = 0;
		};
		4EC49B9025BED71300E76218 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				4EC49BBF25BEDAC100E76218 /* MeetHour_Broadcast_Extension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		0B412F1D1EDEE6E800B1A0A6 /* ViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		0B412F1E1EDEE6E800B1A0A6 /* ViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		0B412F201EDEE95300B1A0A6 /* Main.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = Main.storyboard; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		0B5418461F7C5D8C00A2DD86 /* MeetingRowController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MeetingRowController.swift; sourceTree = "<group>"; };
		0B70016F1F7C51CC005944F4 /* InCallController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InCallController.swift; sourceTree = "<group>"; };
		0BBD021F212EB69D00CCB19F /* Types.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Types.h; sourceTree = "<group>"; };
		0BEA5C251F7B8F73000D0AB4 /* Meet Hour.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Meet Hour.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		0BEA5C281F7B8F73000D0AB4 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Interface.storyboard; sourceTree = "<group>"; };
		0BEA5C2A1F7B8F73000D0AB4 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		0BEA5C2C1F7B8F73000D0AB4 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		0BEA5C311F7B8F73000D0AB4 /* Meet Hour Companion Extension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = "Meet Hour Companion Extension.appex"; sourceTree = BUILT_PRODUCTS_DIR; };
		0BEA5C361F7B8F73000D0AB4 /* InterfaceController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InterfaceController.swift; sourceTree = "<group>"; };
		0BEA5C381F7B8F73000D0AB4 /* ExtensionDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExtensionDelegate.swift; sourceTree = "<group>"; };
		0BEA5C3A1F7B8F73000D0AB4 /* ComplicationController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ComplicationController.swift; sourceTree = "<group>"; };
		0BEA5C3C1F7B8F73000D0AB4 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		0BEA5C3E1F7B8F73000D0AB4 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* Meet Hour.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Meet Hour.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		13B07FB21A68108700A75B9A /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/LaunchScreen.xib; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		190B22F925FF7A95002F500F /* MeetHourSDK.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = MeetHourSDK.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		19318E6B254B465400D67E9B /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		19590A612550492100842FA6 /* GraphicRectangle.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GraphicRectangle.swift; sourceTree = "<group>"; };
		19590A622550492100842FA6 /* GraphicCorner.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GraphicCorner.swift; sourceTree = "<group>"; };
		19590A632550492100842FA6 /* GraphicCircular.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GraphicCircular.swift; sourceTree = "<group>"; };
		19590A642550492100842FA6 /* Utilities.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Utilities.swift; sourceTree = "<group>"; };
		19590A652550492100842FA6 /* GraphicBezel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GraphicBezel.swift; sourceTree = "<group>"; };
		19590A6D2550493100842FA6 /* TemplateConfiguration.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TemplateConfiguration.swift; sourceTree = "<group>"; };
		19590A6E2550493100842FA6 /* Timeline.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Timeline.swift; sourceTree = "<group>"; };
		4E51B75C25E4115F0038575A /* DarwinNotificationCenter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DarwinNotificationCenter.h; sourceTree = "<group>"; };
		4E51B75D25E4115F0038575A /* DarwinNotificationCenter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DarwinNotificationCenter.m; sourceTree = "<group>"; };
		4EC49B8625BED71300E76218 /* ReplayKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ReplayKit.framework; path = System/Library/Frameworks/ReplayKit.framework; sourceTree = SDKROOT; };
		4EC49BB625BEDAC100E76218 /* MeetHour_Broadcast_Extension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = MeetHour_Broadcast_Extension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		4EC49BB925BEDAC100E76218 /* SampleHandler.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SampleHandler.h; sourceTree = "<group>"; };
		4EC49BBA25BEDAC100E76218 /* SampleHandler.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SampleHandler.m; sourceTree = "<group>"; };
		4EC49BBC25BEDAC100E76218 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		4EC49BC925BEDB6400E76218 /* SocketConnection.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SocketConnection.h; sourceTree = "<group>"; };
		4EC49BCA25BEDB6400E76218 /* SocketConnection.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SocketConnection.m; sourceTree = "<group>"; };
		4EC49BCF25BF19CF00E76218 /* SampleUploader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SampleUploader.h; sourceTree = "<group>"; };
		4EC49BD025BF19CF00E76218 /* SampleUploader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SampleUploader.m; sourceTree = "<group>"; };
		4EC49BDB25BF280A00E76218 /* extension.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = extension.entitlements; sourceTree = "<group>"; };
		60A59BDF420147ECDBB1C9B8 /* libPods-meet-hour.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-meet-hour.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		69A7789E262448AC004DFE67 /* WebRTC.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = WebRTC.xcframework; path = "../../node_modules/react-native-webrtc/apple/WebRTC.xcframework"; sourceTree = "<group>"; };
		842EDC082DC121E000F7B1C7 /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		84B537142D033CE10029852B /* hermes.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = hermes.xcframework; path = "../Pods/hermes-engine/destroot/Library/Frameworks/universal/hermes.xcframework"; sourceTree = "<group>"; };
		9F1F74315D5142D3E4BD134E /* Pods-meet-hour.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-meet-hour.debug.xcconfig"; path = "../Pods/Target Support Files/Pods-meet-hour/Pods-meet-hour.debug.xcconfig"; sourceTree = "<group>"; };
		A0413E552CB6DAC30083D38E /* GiphyUISDK.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = GiphyUISDK.xcframework; path = ../Pods/Giphy/GiphySDK/GiphyUISDK.xcframework; sourceTree = "<group>"; };
		A08C06C42C820AE2004DE694 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		AB7A3F2D1C8047B58405B288 /* libRNAddCalendarEvent.a */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = archive.ar; path = libRNAddCalendarEvent.a; sourceTree = "<group>"; };
		B3B083EB1D4955FF0069CEE7 /* app.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = app.entitlements; sourceTree = "<group>"; };
		BE9BF1836C04C89F2E47FA3A /* Pods-meet-hour.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-meet-hour.release.xcconfig"; path = "../Pods/Target Support Files/Pods-meet-hour/Pods-meet-hour.release.xcconfig"; sourceTree = "<group>"; };
		DE4C455F21DE1E4E00EA0709 /* FIRUtilities.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FIRUtilities.m; sourceTree = "<group>"; };
		DE4C456021DE1E4E00EA0709 /* FIRUtilities.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FIRUtilities.h; sourceTree = "<group>"; };
		E58801132278944E008B0561 /* MeetHourContext.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MeetHourContext.swift; sourceTree = "<group>"; };
		E5C97B62227A1EB400199214 /* MeetHourCommands.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MeetHourCommands.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		0BEA5C2E1F7B8F73000D0AB4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				190B230525FF7DCB002F500F /* MeetHourSDK.framework in Frameworks */,
				69A7789F262448AC004DFE67 /* WebRTC.xcframework in Frameworks */,
				A0413E562CB6DAC30083D38E /* GiphyUISDK.xcframework in Frameworks */,
				84B537152D033CE10029852B /* hermes.xcframework in Frameworks */,
				6C91A67AD2F0F20F904FFB9D /* libPods-meet-hour.a in Frameworks */,
				842EDC0E2DC1244D00F7B1C7 /* JavaScriptCore.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1F021A8A5B056078665DE530 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4EC49BB325BEDAC100E76218 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4EC49BB725BEDAC100E76218 /* ReplayKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0B26BE711EC5BC4D00EEFB41 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				842EDC082DC121E000F7B1C7 /* JavaScriptCore.framework */,
				84B537142D033CE10029852B /* hermes.xcframework */,
				A0413E552CB6DAC30083D38E /* GiphyUISDK.xcframework */,
				69A7789E262448AC004DFE67 /* WebRTC.xcframework */,
				190B22F925FF7A95002F500F /* MeetHourSDK.framework */,
				4EC49B8625BED71300E76218 /* ReplayKit.framework */,
				60A59BDF420147ECDBB1C9B8 /* libPods-meet-hour.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		0BEA5C261F7B8F73000D0AB4 /* Watch app */ = {
			isa = PBXGroup;
			children = (
				0BEA5C271F7B8F73000D0AB4 /* Interface.storyboard */,
				0BEA5C2A1F7B8F73000D0AB4 /* Assets.xcassets */,
				0BEA5C2C1F7B8F73000D0AB4 /* Info.plist */,
			);
			name = "Watch app";
			path = watchos/app;
			sourceTree = "<group>";
		};
		0BEA5C351F7B8F73000D0AB4 /* WatchKit extension */ = {
			isa = PBXGroup;
			children = (
				19590A60255048B300842FA6 /* Families */,
				0BEA5C361F7B8F73000D0AB4 /* InterfaceController.swift */,
				0BEA5C381F7B8F73000D0AB4 /* ExtensionDelegate.swift */,
				0BEA5C3A1F7B8F73000D0AB4 /* ComplicationController.swift */,
				0BEA5C3C1F7B8F73000D0AB4 /* Assets.xcassets */,
				0BEA5C3E1F7B8F73000D0AB4 /* Info.plist */,
				0B70016F1F7C51CC005944F4 /* InCallController.swift */,
				19590A6D2550493100842FA6 /* TemplateConfiguration.swift */,
				19590A6E2550493100842FA6 /* Timeline.swift */,
				0B5418461F7C5D8C00A2DD86 /* MeetingRowController.swift */,
				E58801132278944E008B0561 /* MeetHourContext.swift */,
				E5C97B62227A1EB400199214 /* MeetHourCommands.swift */,
			);
			name = "WatchKit extension";
			path = watchos/extension;
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* src */ = {
			isa = PBXGroup;
			children = (
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.m */,
				DE4C456021DE1E4E00EA0709 /* FIRUtilities.h */,
				DE4C455F21DE1E4E00EA0709 /* FIRUtilities.m */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				13B07FB11A68108700A75B9A /* LaunchScreen.xib */,
				19318E6B254B465400D67E9B /* GoogleService-Info.plist */,
				13B07FB71A68108700A75B9A /* main.m */,
				0B412F201EDEE95300B1A0A6 /* Main.storyboard */,
				0BBD021F212EB69D00CCB19F /* Types.h */,
				0B412F1D1EDEE6E800B1A0A6 /* ViewController.h */,
				0B412F1E1EDEE6E800B1A0A6 /* ViewController.m */,
				A08C06C42C820AE2004DE694 /* PrivacyInfo.xcprivacy */,
			);
			path = src;
			sourceTree = "<group>";
		};
		19590A60255048B300842FA6 /* Families */ = {
			isa = PBXGroup;
			children = (
				19590A652550492100842FA6 /* GraphicBezel.swift */,
				19590A632550492100842FA6 /* GraphicCircular.swift */,
				19590A622550492100842FA6 /* GraphicCorner.swift */,
				19590A612550492100842FA6 /* GraphicRectangle.swift */,
				19590A642550492100842FA6 /* Utilities.swift */,
			);
			name = Families;
			sourceTree = "<group>";
		};
		19E1276225FE097300E383E7 /* Recovered References */ = {
			isa = PBXGroup;
			children = (
				AB7A3F2D1C8047B58405B288 /* libRNAddCalendarEvent.a */,
			);
			name = "Recovered References";
			sourceTree = "<group>";
		};
		4EC49BB825BEDAC100E76218 /* MeetHour_Broadcast_Extension */ = {
			isa = PBXGroup;
			children = (
				4EC49BDB25BF280A00E76218 /* extension.entitlements */,
				4EC49BB925BEDAC100E76218 /* SampleHandler.h */,
				4EC49BBA25BEDAC100E76218 /* SampleHandler.m */,
				4EC49BC925BEDB6400E76218 /* SocketConnection.h */,
				4EC49BCA25BEDB6400E76218 /* SocketConnection.m */,
				4EC49BCF25BF19CF00E76218 /* SampleUploader.h */,
				4EC49BD025BF19CF00E76218 /* SampleUploader.m */,
				4EC49BBC25BEDAC100E76218 /* Info.plist */,
				4E51B75C25E4115F0038575A /* DarwinNotificationCenter.h */,
				4E51B75D25E4115F0038575A /* DarwinNotificationCenter.m */,
			);
			name = MeetHour_Broadcast_Extension;
			path = "broadcast-extension";
			sourceTree = "<group>";
		};
		5E96ADD5E49F3B3822EF9A52 /* Pods */ = {
			isa = PBXGroup;
			children = (
				9F1F74315D5142D3E4BD134E /* Pods-meet-hour.debug.xcconfig */,
				BE9BF1836C04C89F2E47FA3A /* Pods-meet-hour.release.xcconfig */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				B3B083EB1D4955FF0069CEE7 /* app.entitlements */,
				0B26BE711EC5BC4D00EEFB41 /* Frameworks */,
				83CBBA001A601CBA00E9B192 /* Products */,
				13B07FAE1A68108700A75B9A /* src */,
				5E96ADD5E49F3B3822EF9A52 /* Pods */,
				0BEA5C261F7B8F73000D0AB4 /* Watch app */,
				4EC49BB825BEDAC100E76218 /* MeetHour_Broadcast_Extension */,
				0BEA5C351F7B8F73000D0AB4 /* WatchKit extension */,
				19E1276225FE097300E383E7 /* Recovered References */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* Meet Hour.app */,
				0BEA5C251F7B8F73000D0AB4 /* Meet Hour.app */,
				0BEA5C311F7B8F73000D0AB4 /* Meet Hour Companion Extension.appex */,
				4EC49BB625BEDAC100E76218 /* MeetHour_Broadcast_Extension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		0BEA5C241F7B8F73000D0AB4 /* MeetHourCompanion */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0BEA5C481F7B8F73000D0AB4 /* Build configuration list for PBXNativeTarget "MeetHourCompanion" */;
			buildPhases = (
				0BEA5C231F7B8F73000D0AB4 /* Resources */,
				0BEA5C471F7B8F73000D0AB4 /* Embed Foundation Extensions */,
				1F021A8A5B056078665DE530 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				0BEA5C341F7B8F73000D0AB4 /* PBXTargetDependency */,
			);
			name = MeetHourCompanion;
			productName = MeetHourCompanion;
			productReference = 0BEA5C251F7B8F73000D0AB4 /* Meet Hour.app */;
			productType = "com.apple.product-type.application.watchapp2";
		};
		0BEA5C301F7B8F73000D0AB4 /* Meet_Hour_Companion_Extension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0BEA5C461F7B8F73000D0AB4 /* Build configuration list for PBXNativeTarget "Meet_Hour_Companion_Extension" */;
			buildPhases = (
				0BEA5C2D1F7B8F73000D0AB4 /* Sources */,
				0BEA5C2E1F7B8F73000D0AB4 /* Frameworks */,
				0BEA5C2F1F7B8F73000D0AB4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Meet_Hour_Companion_Extension;
			productName = Meet_Hour_Companion_Extension;
			productReference = 0BEA5C311F7B8F73000D0AB4 /* Meet Hour Companion Extension.appex */;
			productType = "com.apple.product-type.watchkit2-extension";
		};
		13B07F861A680F5B00A75B9A /* meet-hour */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "meet-hour" */;
			buildPhases = (
				B6607F42A5CF0C76E98929E2 /* [CP] Check Pods Manifest.lock */,
				0BBA83C41EC9F7600075A103 /* Run React packager */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				0B26BE701EC5BC3C00EEFB41 /* Embed Frameworks */,
				0BB7DA181EC9E695007AAE98 /* Adjust ATS */,
				DEF4813D224925A2002AD03A /* Copy Google Plist file */,
				DEC2069321CBBD6900072F03 /* Setup Crashlytics */,
				DE11877A21EE09640078D059 /* Setup Google reverse URL handler */,
				DE4F6D6E22005C0400DE699E /* Setup Dropbox */,
				0BEA5C491F7B8F73000D0AB4 /* Embed Watch Content */,
				4EC49B9025BED71300E76218 /* Embed Foundation Extensions */,
				4FC818A65BD90639CD0D8EB1 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				0BEA5C401F7B8F73000D0AB4 /* PBXTargetDependency */,
				4EC49BBE25BEDAC100E76218 /* PBXTargetDependency */,
			);
			name = "meet-hour";
			productName = "Meet Hour";
			productReference = 13B07F961A680F5B00A75B9A /* Meet Hour.app */;
			productType = "com.apple.product-type.application";
		};
		4EC49BB525BEDAC100E76218 /* MeetHour_Broadcast_Extension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4EC49BC025BEDAC100E76218 /* Build configuration list for PBXNativeTarget "MeetHour_Broadcast_Extension" */;
			buildPhases = (
				4EC49BB225BEDAC100E76218 /* Sources */,
				4EC49BB325BEDAC100E76218 /* Frameworks */,
				4EC49BB425BEDAC100E76218 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = MeetHour_Broadcast_Extension;
			productName = MeetHour_Broadcast_Extension;
			productReference = 4EC49BB625BEDAC100E76218 /* MeetHour_Broadcast_Extension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1500;
				ORGANIZATIONNAME = Facebook;
				TargetAttributes = {
					0BEA5C241F7B8F73000D0AB4 = {
						CreatedOnToolsVersion = 9.0;
					};
					0BEA5C301F7B8F73000D0AB4 = {
						CreatedOnToolsVersion = 9.0;
					};
					13B07F861A680F5B00A75B9A = {
						SystemCapabilities = {
							com.apple.SafariKeychain = {
								enabled = 1;
							};
							com.apple.Siri = {
								enabled = 1;
							};
						};
					};
					4EC49BB525BEDAC100E76218 = {
						CreatedOnToolsVersion = 12.2;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "app" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* meet-hour */,
				0BEA5C241F7B8F73000D0AB4 /* MeetHourCompanion */,
				0BEA5C301F7B8F73000D0AB4 /* Meet_Hour_Companion_Extension */,
				4EC49BB525BEDAC100E76218 /* MeetHour_Broadcast_Extension */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		0BEA5C231F7B8F73000D0AB4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0BEA5C2B1F7B8F73000D0AB4 /* Assets.xcassets in Resources */,
				0BEA5C291F7B8F73000D0AB4 /* Interface.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0BEA5C2F1F7B8F73000D0AB4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0BEA5C3D1F7B8F73000D0AB4 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0B412F211EDEE95300B1A0A6 /* Main.storyboard in Resources */,
				19318E6C254B465400D67E9B /* GoogleService-Info.plist in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				A08C06C52C820AE2004DE694 /* PrivacyInfo.xcprivacy in Resources */,
				13B07FBD1A68108700A75B9A /* LaunchScreen.xib in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4EC49BB425BEDAC100E76218 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		0BB7DA181EC9E695007AAE98 /* Adjust ATS */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 12;
			files = (
			);
			inputPaths = (
			);
			name = "Adjust ATS";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "../scripts/fixup-ats.sh\n";
		};
		0BBA83C41EC9F7600075A103 /* Run React packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 12;
			files = (
			);
			inputPaths = (
			);
			name = "Run React packager";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "../scripts/run-packager.sh\n";
		};
		4FC818A65BD90639CD0D8EB1 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-meet-hour/Pods-meet-hour-resources.sh",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal/FirebaseCoreInternal_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations/FirebaseInstallations_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport/GoogleDataTransport_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities/GoogleUtilities_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC/FBLPromises_Privacy.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseCoreInternal_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseInstallations_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleDataTransport_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleUtilities_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FBLPromises_Privacy.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-meet-hour/Pods-meet-hour-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		B6607F42A5CF0C76E98929E2 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-meet-hour-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		DE11877A21EE09640078D059 /* Setup Google reverse URL handler */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 12;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Setup Google reverse URL handler";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "INFO_PLIST=\"$BUILT_PRODUCTS_DIR/$INFOPLIST_PATH\"\nGOOGLE_PLIST=\"$PROJECT_DIR/GoogleService-Info.plist\"\n\nif [[ -f $GOOGLE_PLIST ]]; then\n    REVERSED_CLIENT_ID=$(/usr/libexec/PlistBuddy -c \"Print :REVERSED_CLIENT_ID:\" $GOOGLE_PLIST)\n    /usr/libexec/PlistBuddy -c \"Set :CFBundleURLTypes:1:CFBundleURLSchemes:0 $REVERSED_CLIENT_ID\" $INFO_PLIST\nfi\n";
		};
		DE4F6D6E22005C0400DE699E /* Setup Dropbox */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Setup Dropbox";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "INFO_PLIST=\"$BUILT_PRODUCTS_DIR/$INFOPLIST_PATH\"\nDROPBOX_KEY_FILE=\"$PROJECT_DIR/dropbox.key\"\n\nif [[ -f $DROPBOX_KEY_FILE ]]; then\n    /usr/libexec/PlistBuddy -c \"Delete :LSApplicationQueriesSchemes\" $INFO_PLIST\n    /usr/libexec/PlistBuddy -c \"Add :LSApplicationQueriesSchemes array\" $INFO_PLIST\n    /usr/libexec/PlistBuddy -c \"Add :LSApplicationQueriesSchemes:0 string 'dbapi-2'\" $INFO_PLIST\n    /usr/libexec/PlistBuddy -c \"Add :LSApplicationQueriesSchemes:1 string 'dbapi-8-emm'\" $INFO_PLIST\n\n    DROPBOX_KEY=$(head -n 1 $DROPBOX_KEY_FILE)\n    /usr/libexec/PlistBuddy -c \"Add :CFBundleURLTypes:2:CFBundleURLName string dropbox\" $INFO_PLIST\n    /usr/libexec/PlistBuddy -c \"Add :CFBundleURLTypes:2:CFBundleURLSchemes array\" $INFO_PLIST\n    /usr/libexec/PlistBuddy -c \"Add :CFBundleURLTypes:2:CFBundleURLSchemes:0 string $DROPBOX_KEY\" $INFO_PLIST\nfi\n";
		};
		DEC2069321CBBD6900072F03 /* Setup Crashlytics */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 12;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Setup Crashlytics";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "GOOGLE_PLIST=\"$PROJECT_DIR/GoogleService-Info.plist\"\n\nif [[ -f $GOOGLE_PLIST ]]; then\n    if [ \"${CONFIGURATION}\" != \"Debug\" ]; then\n        find \"${DWARF_DSYM_FOLDER_PATH}\" -name \"*.dSYM\" | xargs -I \\{\\}\n         ${PODS_ROOT}/FirebaseCrashlytics/run\n         ${PODS_ROOT}/FirebaseCrashlytics/upload-symbols -gsp $GOOGLE_PLIST -p ios \\{\\}\n    fi\nfi\n";
		};
		DEF4813D224925A2002AD03A /* Copy Google Plist file */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 12;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Copy Google Plist file";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "GOOGLE_PLIST_NAME=\"GoogleService-Info.plist\"\nGOOGLE_PLIST=\"$PROJECT_DIR/$GOOGLE_PLIST_NAME\"\nBUILD_APP_DIR=\"$BUILT_PRODUCTS_DIR/$PRODUCT_NAME.app\"\n\nif [[ -f $GOOGLE_PLIST ]]; then\n    cp $GOOGLE_PLIST \"$BUILD_APP_DIR/$GOOGLE_PLIST_NAME\"\nfi\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		0BEA5C2D1F7B8F73000D0AB4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				19590A672550492100842FA6 /* GraphicCorner.swift in Sources */,
				0B7001701F7C51CC005944F4 /* InCallController.swift in Sources */,
				19590A6F2550493100842FA6 /* TemplateConfiguration.swift in Sources */,
				19590A692550492100842FA6 /* Utilities.swift in Sources */,
				E5C97B63227A1EB400199214 /* MeetHourCommands.swift in Sources */,
				0B5418471F7C5D8C00A2DD86 /* MeetingRowController.swift in Sources */,
				E588011722789D43008B0561 /* MeetHourContext.swift in Sources */,
				19590A662550492100842FA6 /* GraphicRectangle.swift in Sources */,
				19590A702550493100842FA6 /* Timeline.swift in Sources */,
				0BEA5C391F7B8F73000D0AB4 /* ExtensionDelegate.swift in Sources */,
				19590A682550492100842FA6 /* GraphicCircular.swift in Sources */,
				19590A6A2550492100842FA6 /* GraphicBezel.swift in Sources */,
				0BEA5C371F7B8F73000D0AB4 /* InterfaceController.swift in Sources */,
				0BEA5C3B1F7B8F73000D0AB4 /* ComplicationController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0B412F1F1EDEE6E800B1A0A6 /* ViewController.m in Sources */,
				13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */,
				DE4C456121DE1E4E00EA0709 /* FIRUtilities.m in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4EC49BB225BEDAC100E76218 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4EC49BCB25BEDB6400E76218 /* SocketConnection.m in Sources */,
				4EC49BBB25BEDAC100E76218 /* SampleHandler.m in Sources */,
				4E51B75E25E4115F0038575A /* DarwinNotificationCenter.m in Sources */,
				4EC49BD125BF19CF00E76218 /* SampleUploader.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		0BEA5C341F7B8F73000D0AB4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 0BEA5C301F7B8F73000D0AB4 /* Meet_Hour_Companion_Extension */;
			targetProxy = 0BEA5C331F7B8F73000D0AB4 /* PBXContainerItemProxy */;
		};
		0BEA5C401F7B8F73000D0AB4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 0BEA5C241F7B8F73000D0AB4 /* MeetHourCompanion */;
			targetProxy = 0BEA5C3F1F7B8F73000D0AB4 /* PBXContainerItemProxy */;
		};
		4EC49BBE25BEDAC100E76218 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 4EC49BB525BEDAC100E76218 /* MeetHour_Broadcast_Extension */;
			targetProxy = 4EC49BBD25BEDAC100E76218 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		0BEA5C271F7B8F73000D0AB4 /* Interface.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				0BEA5C281F7B8F73000D0AB4 /* Base */,
			);
			name = Interface.storyboard;
			sourceTree = "<group>";
		};
		13B07FB11A68108700A75B9A /* LaunchScreen.xib */ = {
			isa = PBXVariantGroup;
			children = (
				13B07FB21A68108700A75B9A /* Base */,
			);
			name = LaunchScreen.xib;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		0BEA5C421F7B8F73000D0AB4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1.1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 7AAZ3ZK627;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				IBSC_MODULE = Meet_Hour_Companion_Extension;
				INFOPLIST_FILE = watchos/app/Info.plist;
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"\"$(SRCROOT)/Meet Hour\"",
					"\"$(SRCROOT)/Meet Hour\"",
				);
				MARKETING_VERSION = 21.0.5;
				PRODUCT_BUNDLE_IDENTIFIER = go.meethour.io.watchkit;
				PRODUCT_NAME = "Meet Hour";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 4.0;
			};
			name = Debug;
		};
		0BEA5C431F7B8F73000D0AB4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1.1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 7AAZ3ZK627;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				IBSC_MODULE = Meet_Hour_Companion_Extension;
				INFOPLIST_FILE = watchos/app/Info.plist;
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"\"$(SRCROOT)/Meet Hour\"",
					"\"$(SRCROOT)/Meet Hour\"",
				);
				MARKETING_VERSION = 21.0.5;
				PRODUCT_BUNDLE_IDENTIFIER = go.meethour.io.watchkit;
				PRODUCT_NAME = "Meet Hour";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 4.0;
			};
			name = Release;
		};
		0BEA5C441F7B8F73000D0AB4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_COMPLICATION_NAME = Complication;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1.1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 7AAZ3ZK627;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				INFOPLIST_FILE = watchos/extension/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 21.0.5;
				PRODUCT_BUNDLE_IDENTIFIER = go.meethour.io.watchkit.extension;
				PRODUCT_NAME = "Meet Hour Companion Extension";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 4.0;
			};
			name = Debug;
		};
		0BEA5C451F7B8F73000D0AB4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_COMPLICATION_NAME = Complication;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1.1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 7AAZ3ZK627;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				INFOPLIST_FILE = watchos/extension/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 21.0.5;
				PRODUCT_BUNDLE_IDENTIFIER = go.meethour.io.watchkit.extension;
				PRODUCT_NAME = "Meet Hour Companion Extension";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 4.0;
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9F1F74315D5142D3E4BD134E /* Pods-meet-hour.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIconDebug;
				CODE_SIGN_ENTITLEMENTS = app.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1.1;
				DEVELOPMENT_TEAM = 7AAZ3ZK627;
				ENABLE_BITCODE = NO;
				EXCLUDED_ARCHS = "";
				"EXCLUDED_ARCHS[sdk=*]" = x86_64;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"../../node_modules/react-native-webrtc/ios",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(SRCROOT)/../node_modules/react-native-splash-screen/ios",
				);
				INFOPLIST_FILE = src/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Meet Hour";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"\"$(SRCROOT)/Meet Hour\"",
					"\"$(SRCROOT)/Meet Hour\"",
				);
				MARKETING_VERSION = 21.0.5;
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = go.meethour.io;
				PRODUCT_NAME = "Meet Hour";
				PROVISIONING_PROFILE_SPECIFIER = "";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BE9BF1836C04C89F2E47FA3A /* Pods-meet-hour.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIconRelease;
				CODE_SIGN_ENTITLEMENTS = app.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1.1;
				DEVELOPMENT_TEAM = 7AAZ3ZK627;
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "arm64 x86_64";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"../../node_modules/react-native-webrtc/ios",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(SRCROOT)/../node_modules/react-native-splash-screen/ios",
					"$(SRCROOT)/../../node_modules/react-native-add-calendar-event/ios",
					"$(SRCROOT)/../../node_modules/react-native-add-calendar-event/ios",
				);
				INFOPLIST_FILE = src/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Meet Hour";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"\"$(SRCROOT)/Meet Hour\"",
					"\"$(SRCROOT)/Meet Hour\"",
				);
				MARKETING_VERSION = 21.0.5;
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = go.meethour.io;
				PRODUCT_NAME = "Meet Hour";
				PROVISIONING_PROFILE_SPECIFIER = "";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		4EC49BC125BEDAC100E76218 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "broadcast-extension/extension.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1.1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 7AAZ3ZK627;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				INFOPLIST_FILE = "broadcast-extension/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 21.0.5;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = go.meethour.io.BroadcastExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		4EC49BC225BEDAC100E76218 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "broadcast-extension/extension.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1.1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 7AAZ3ZK627;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				INFOPLIST_FILE = "broadcast-extension/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 21.0.5;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = go.meethour.io.BroadcastExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CXX = "";
				ENABLE_BITCODE = YES;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 12.1;
				LD = "";
				LDPLUSPLUS = "";
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				CXX = "";
				ENABLE_BITCODE = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 12.1;
				LD = "";
				LDPLUSPLUS = "";
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				TARGETED_DEVICE_FAMILY = "1,2";
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		0BEA5C461F7B8F73000D0AB4 /* Build configuration list for PBXNativeTarget "Meet_Hour_Companion_Extension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0BEA5C441F7B8F73000D0AB4 /* Debug */,
				0BEA5C451F7B8F73000D0AB4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0BEA5C481F7B8F73000D0AB4 /* Build configuration list for PBXNativeTarget "MeetHourCompanion" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0BEA5C421F7B8F73000D0AB4 /* Debug */,
				0BEA5C431F7B8F73000D0AB4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "meet-hour" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4EC49BC025BEDAC100E76218 /* Build configuration list for PBXNativeTarget "MeetHour_Broadcast_Extension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4EC49BC125BEDAC100E76218 /* Debug */,
				4EC49BC225BEDAC100E76218 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "app" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
