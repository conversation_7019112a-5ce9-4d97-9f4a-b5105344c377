/* eslint-disable react-native/no-unused-styles */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react-native/no-color-literals */
import _ from 'lodash';
import moment from 'moment';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { BackHandler, Clipboard, Dimensions, Image,
    KeyboardAvoidingView, Linking, Platform, SafeAreaView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Collapsible from 'react-native-collapsible';
import { useDispatch, useSelector } from 'react-redux';

import { appNavigate } from '../../../app/actions';
import { getDefaultURL } from '../../../app/functions';
import logger from '../../../app/logger';
import { Avatar } from '../../../base/avatar';
import { Icon, IconArrowBack, IconArrowDownWide, IconArrowUpWide, IconCopy, IconNotAllowed } from '../../../base/icons';
import { parseRETURNURLFromURLParams } from '../../../base/jwt';
import { updateSettings } from '../../../base/settings';
import { ColorPalette } from '../../../base/styles';
import { parseURLParams } from '../../../base/util';


const screenHeight = Dimensions.get('window').height;
const styles = StyleSheet.create({

    container: {
        flex: 1,
        backgroundColor: ColorPalette.darkblue,
        height: 'auto',
        maxHeight: screenHeight,
        position: 'absolute',
        display: 'flex',
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 5000000,
        width: '100%'
    },
    input: {
        height: 45,
        width: '100%',
        backgroundColor: '#fff',
        borderRadius: 10
    },
    joinButton: {
        alignItems: 'center',
        backgroundColor: '#1ACB8C',
        height: 50,
        width: '100%',
        borderRadius: 10,
        justifyContent: 'center'
    },
    notAllowed: {
        color: '#d51616'
    },
    privacy: {
        width: 50,
        height: 69
    },
    hostButton: {
        alignItems: 'center',
        backgroundColor: '#5eba7d',
        height: 50,
        borderRadius: 10,
        justifyContent: 'center'
    }
});

/**
 * Prejoin Waitng Js.
 *
 * @returns {any}
 */
function InvalidRoom({ isTrueReg = false }) {
    const { pregData } = useSelector(state => state['features/base/mtApi']);
    const { accessToken } = useSelector(state => state['features/base/jwt']);
    const [ backArrow, setBackArrow ] = React.useState(null);
    const { locationURL } = useSelector(state => state['features/base/connection']);
    const [ expanded, setExpanded ] = React.useState(false);
    const defaultUrl = useSelector(state => getDefaultURL(state));
    const [ returnurlParameter, setRetrunurlParameter ] = React.useState(null);
    const roomName = useSelector(state => state['features/base/conference'].room);
    const dispatch = useDispatch();
    const { t } = useTranslation();

    React.useEffect(() => {
        if (locationURL) {
            const returnUrl = parseRETURNURLFromURLParams(locationURL);
            const goBackUrlEncoded = parseURLParams(locationURL, true, 'search')?.goBack;
            const decodedUri = returnUrl ? decodeURIComponent(returnUrl) : null;

            if (goBackUrlEncoded) {
                const goBackUrl = decodeURIComponent(goBackUrlEncoded);

                setBackArrow(goBackUrl);
            }

            if (decodedUri) {
                setRetrunurlParameter(decodedUri);
            }
        }
    }, []);

    // const backAction = () => {
    //     setBackButton();

    //     return false;
    // };

    const backHandler = BackHandler.addEventListener(
        'hardwareBackPress',
        setBackButton
    );


    React.useEffect(() => () => backHandler.remove(), []);

    /**
     *
     * Backbutton gets called.
     *
     * @returns {any}
     */
    function setBackButton() {
        if (backArrow) {
            // console.log('BackButton', backArrow);
            Linking.openURL(`${backArrow}`).catch(err => logger.error("Couldn't load page", err));

        }
        dispatch(updateSettings({
            accessToken: null,
            email: null,
            displayName: null
        }));
        dispatch(appNavigate(undefined));

        return true;

        // backAction();
    }

    let meetingTime;
    const meetingDate = pregData?.meeting_start_time;

    if (meetingDate) {
        meetingTime = moment(pregData?.meeting_start_time).locale('en-gb')
        .format('DD MMM YYYY - h:mm A');
    }

    /**
     * Toggle Expansion.
     *
     * @returns {any}
     */
    function toggleExpansion() {
        setExpanded(!expanded);
    }

    /**
     * Open external Link.
     *
     * @param {string} link - Link.
     * @returns {any}
     */
    function onOpenLink(link) {
        return () => Linking.openURL(`${link}`).catch(err => logger.error("Couldn't load page", err));
    }

    return (
        <>
            <SafeAreaView
                style = {{ flex: 1,
                    backgroundColor: ColorPalette.darkblue,
                    height: 'auto',
                    maxHeight: screenHeight,
                    position: 'absolute',
                    display: 'flex',
                    top: 0,
                    bottom: 0,
                    left: 0,
                    right: 0,
                    zIndex: 5000000,
                    width: '100%'
                }} >

                <View
                    style = {{
                        position: 'absolute',
                        display: 'flex',
                        top: Platform.OS === 'ios' ? '5%' : 0,
                        bottom: 0,
                        left: 0,
                        right: 0,
                        zIndex: 500000,
                        flex: 1,
                        backgroundColor: ColorPalette.darkblue,
                        height: '100%',
                        width: '100%'
                    }} >
                    <View style = { styles.container }>
                        <KeyboardAvoidingView
                            behavior = { Platform.OS === 'ios' ? 'position' : null }
                            keyboardVerticalOffset = { Platform.OS === 'ios' ? 50 : 70 }
                            style = {{ flex: 1 }}>
                            <View
                                style = {{ flexDirection: 'row',
                                    justifyContent: 'space-between' }}>
                                <TouchableOpacity onPress = { setBackButton } >
                                    <Icon
                                        src = { IconArrowBack }
                                        style = {{
                                            color: 'white',
                                            fontSize: 22,
                                            padding: 8,
                                            marginLeft: 5
                                        }} />
                                </TouchableOpacity>
                                <TouchableOpacity
                                    style = {{
                                        marginLeft: 5,
                                        padding: 8
                                    }} >
                                    <Avatar size = { 25 } />
                                </TouchableOpacity>
                            </View>
                            <View
                                style = { [ styles.surface, {
                                    backgroundColor: '#273c61',
                                    width: '75%',
                                    alignSelf: 'center',
                                    padding: 10,
                                    marginBottom: 20,
                                    borderTopLeftRadius: 15,
                                    borderTopRightRadius: 15,
                                    borderBottomRightRadius: 15,
                                    borderBottomLeftRadius: 15
                                } ] }>
                                <TouchableOpacity
                                    onPress = { toggleExpansion }>
                                    <View
                                        style = {{
                                            display: 'flex',
                                            flexDirection: 'row',
                                            alignSelf: 'center'
                                        }}>
                                        <Text
                                            style = {{
                                                alignSelf: 'center',
                                                color: 'white',
                                                fontSize: 16,
                                                fontWeight: '400'
                                            }}>
                                            {t('welcomepage.meetingDetails')}
                                        </Text>
                                        <Icon
                                            src = { expanded ? IconArrowUpWide : IconArrowDownWide }
                                            style = {{
                                                color: '#273c61',
                                                fontSize: 15,
                                                margin: 5
                                            }} />
                                    </View>
                                </TouchableOpacity>
                                <Collapsible collapsed = { !expanded }>
                                    <View
                                        style = {{
                                            paddingTop: 10,
                                            paddingBottom: 10,
                                            width: '100%',
                                            alignSelf: 'center',
                                            display: 'flex',
                                            flexDirection: 'row',
                                            justifyContent: 'space-between'
                                        }}>
                                        <Text
                                            style = {{
                                                color: 'white',
                                                fontSize: 17,
                                                fontWeight: '400',
                                                flex: 1,
                                                flexWrap: 'wrap'
                                            }}>{`${defaultUrl}/${roomName}`}</Text>
                                        <TouchableOpacity
                                            // eslint-disable-next-line react/jsx-no-bind
                                            onPress = { () => Clipboard.setString(`${defaultUrl}/${roomName}`) } >
                                            <Icon
                                                size = { 17 }
                                                src = { IconCopy }
                                                style = {{
                                                    paddingTop: 3,
                                                    alignItems: 'center',
                                                    marginRight: 3
                                                }} />
                                        </TouchableOpacity>
                                    </View>
                                    <View>
                                        {
                                            pregData && pregData?.meeting_start_time && pregData?.timezone && (
                                                <>
                                                    <View
                                                        style = {{
                                                            borderBottomColor: 'black',
                                                            borderBottomWidth: 1
                                                        }} />
                                                    <Text
                                                        style = {{
                                                            marginTop: 5,
                                                            marginBottom: 5,
                                                            fontSize: 15,
                                                            color: '#fff'
                                                        }}>
                                                        {t('welcomepage.meetingDate')}:  { meetingTime }
                                                    </Text>
                                                    <View
                                                        style = {{
                                                            borderBottomColor: 'black',
                                                            borderBottomWidth: 1
                                                        }} />
                                                    <Text
                                                        style = {{
                                                            marginTop: 5,
                                                            marginBottom: 5,
                                                            fontSize: 15,
                                                            color: '#fff'
                                                        }}>
                                                        {t('welcomepage.Timezone')}:   { pregData.timezone }
                                                    </Text>
                                                </>
                                            )
                                        }
                                    </View>
                                </Collapsible>
                            </View>
                            <View
                                style = {{
                                    // position: 'relative',
                                    alignSelf: 'center',
                                    position: 'relative',
                                    top: 50,
                                    left: 0,
                                    bottom: 0,
                                    right: 0,
                                    height: 300,
                                    width: '80%',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    backgroundColor: '#2B3541',
                                    borderRadius: 20
                                }}>

                                <View
                                    style = {{ justifyContent: 'center',
                                        alignItems: 'center' }}>
                                    {
                                        pregData.success && (pregData?.allow_guest === 0 || isTrueReg) ? (
                                            <Image
                                                className = 'img-fluid'
                                                source = { require('../../../../../images/privacy.png') }
                                                style = { styles.privacy } />
                                        )
                                        // eslint-disable-next-line camelcase
                                            : (
                                                <Icon
                                                    className = 'img-fluid'
                                                    size = { 100 }
                                                    src = { IconNotAllowed }
                                                    style = { styles.notAllowed } />
                                            )
                                    }
                                    <Text
                                        style = {{ color: '#fff',
                                            marginTop: 5,
                                            paddingLeft: 10,
                                            paddingRight: 10,
                                            fontSize: 20,
                                            fontWeight: '200',
                                            textAlign: 'center' }}>

                                        {pregData && (pregData?.allow_guest === 0 || pregData.success) && !isTrueReg
                                            ? t('welcomepage.privateMeetingErr')
                                            : pregData.message === 'Invalid Credentials' ? t('welcomepage.oopsDeviceClockorTimezoneErr')
                                                : isTrueReg ? t('welcomepage.preRegistrationMsg')
                                                    : t('welcomepage.invalidMeetingID')}</Text>

                                </View>
                                {
                                    isTrueReg && (<View
                                        style = {{ marginTop: 20,
                                            borderRadius: 10,
                                            height: 50,
                                            width: '80%',
                                            alignSelf: 'center',
                                            marginBottom: 20,
                                            justifyContent: 'center' }}>
                                        <TouchableOpacity
                                            onPress = { onOpenLink(pregData?.registration_url) }
                                            style = { styles.hostButton }>
                                            <Text
                                                style = {{ color: '#fff' }}
                                                textAlign = { 'center' }>{t('welcomepage.registerNow')}</Text>
                                        </TouchableOpacity>
                                    </View>)
                                }
                                {
                                    !_.isNil(returnurlParameter) && _.isNil(accessToken) && !isTrueReg && (<View
                                        style = {{ marginTop: 20,
                                            borderRadius: 10,
                                            height: 50,
                                            width: '80%',
                                            alignSelf: 'center',
                                            marginBottom: 20,
                                            justifyContent: 'center' }}>
                                        <TouchableOpacity
                                            onPress = { onOpenLink(returnurlParameter) }
                                            style = { styles.hostButton }>
                                            <Text
                                                style = {{ color: '#fff' }}
                                                textAlign = { 'center' }>{t('prejoin.signinsignup')}</Text>
                                        </TouchableOpacity>
                                    </View>)
                                }
                            </View>

                        </KeyboardAvoidingView>
                    </View>
                </View>
            </SafeAreaView>
        </>
    );
}


export default InvalidRoom;
