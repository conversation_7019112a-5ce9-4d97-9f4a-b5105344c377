/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react-native/no-color-literals */

import { BrowserDetection } from '@jitsi/js-utils';
import axios from 'axios';
import { sha256 } from 'js-sha256';
import jwtDecode from 'jwt-decode';
import _ from 'lodash';
import moment from 'moment';
import React, { Component } from 'react';
import { Dimensions, SafeAreaView, StyleSheet, View } from 'react-native';
import { connect as reduxConnect } from 'react-redux';
import type { Dispatch } from 'redux';

import logger from '../../../app/logger';
import { PrejoinApi, apiKEY } from '../../../base/config/constants';
import { connect as connectNative } from '../../../base/connection';
// eslint-disable-next-line import/order
import { translate } from '../../../base/i18n';
import { MEDIA_TYPE } from '../../../base/media';
import { ColorPalette } from '../../../base/styles';
import { createDesiredLocalTracks, isLocalTrackMuted } from '../../../base/tracks';
import { isDomainWeborNative } from '../../../base/util/checkOS';
import { Conference } from '../../../conference';
import { showErrorNotification } from '../../../notifications';
import LoadConfigOverlay from '../../../overlay/components/native/LoadConfigOverlay';
import { setPrejoinApiData } from '../../../prejoin/actions';
import { isPrejoinPageEnabled } from '../../../prejoin/functions';


import InvalidRoom from './InvalidRoom';
import PrejoinMobile from './PrejoinMobile';

const screenHeight = Dimensions.get('window').height;
const styles = StyleSheet.create({

    container: {
        flex: 1,
        backgroundColor: ColorPalette.darkblue,
        height: 'auto',
        maxHeight: screenHeight
    }

});

type State = {

    /**
     * Page State.
     */
    _page: boolean,

};

type Props = {

    /**
     * App State.
     */
     _appState: Object,

    /**
     * Conference value.
     */
    _conference: any,

    /**
     * JWT token.
     */
    _jwt: string,

    /**
     * JWT token.
     */
    _jwtData: string,

    /**
     * JWT Stored token.
     */
    _jwtStored: string,

    /**
     * Pre-Registration data.
     */
    _pregData: string,

    /**
     * IsTrueReg Boolean value.
     */
    _isTrueReg: Boolean,

    /**
     * Email of user.
     */
    _email: string,

    /**
     * Display Name of user.
     */
    _displayName: string,

    /**
     * Start with Audio Muted of user.
     */
    _startWithAudioMuted: any,

    /**
     * Start with video Muted of user.
     */
    _startWithVideoMuted: any,

    /**
     * Audio Muted of user.
     */
    _audioMuted: boolean,

    /**
     * Video Muted of user.
     */
    _videoMuted: boolean,

    /**
     * UserDetails.
     */
    _userDetails: any,

    /**
     * Pre-registration data.
     */
    _pregData: any,

    /**
     * Config Settings.
     */
    _confSettings: any,

    /**
     * Meeting Details.
     */
    _meetingDetails: any,

    /**
     * Access Token.
     */
    _accessToken: any,

    /**
     * Room Name.
     */
    _roomName: any,

    /**
     * API_URL_PREFIX.
     */
    _API_URL_PREFIX: any,

    /**
     * API_URL_PREFIX.
     */
    _API_VERSION_PREFIX: any,

    /**
     * MH_CLIENT_ID.
     */
    _MH_CLIENT_ID: any,

    /**
     * Interface Config.
     */
    _interfaceConfig: any,

    /**
     * Redux store dispatch method.
     */
    dispatch: Dispatch<any>,


};

/**
 * Component
 * {@code PrejoinPages} component.
 *
 * @param {Object} state - The Redux state.
 * @private
 * @returns {Props}
 */
class PrejoinPages extends Component<Props, State> implements Prejoin {
    /**
     * Initializes a new LoginDialog instance.
     *
     * @param {Object} props - The read-only properties with which the new
     * instance is to be initialized.
     */
    constructor(props: Props) {
        super(props);

        this.state = {
            _appState: null,
            _page: false
        };

        this.onSetPage = this.onSetPage.bind(this);
        this.joinConferenceDirect = this.joinConferenceDirect.bind(this);

    }

    // eslint-disable-next-line valid-jsdoc, require-jsdoc
    async componentDidMount() {

        // Skipping the prejoin page if PreJoinPageEnabled is disabled.
        const isPrejoinEnabled = await isPrejoinPageEnabled(this.props._appState);
        const { t } = this.props;

        if (isPrejoinEnabled) {
            this.onSetPage(false);
        } else if (!isPrejoinEnabled && this.props._conference) {
            this.onSetPage(false);

            this.props.dispatch(showErrorNotification({
                description: t('prejoin.alreadyOneConferenceIsRunningInBackground'),
                titleKey: t('prejoin.multipleConferenceInitiation')
            }));
        } else {

            this.onSetPage(true); // To show the loader

            const moement = moment().seconds(0)
            .milliseconds(0)
            .format('X');

            const headers = _.isNil(this.props._accessToken) ? {
                'Content-Type': 'application/json'
            } : {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.props._accessToken}`
            };

            const mesh256 = sha256(`${apiKEY}:MH:${moement}`);
            const urlConfg = `${this.props._API_URL_PREFIX}${this.props._API_VERSION_PREFIX}meeting/totalparticipants`;
            const participantCountObject = await axios({
                url: urlConfg,
                data: JSON.stringify({
                    client_id: this.props._MH_CLIENT_ID,
                    credentials: mesh256,
                    ...isDomainWeborNative,
                    meeting_id: this.props._roomName
                }),
                headers,
                method: 'post'
            });

            // eslint-disable-next-line camelcase
            const participantCount = participantCountObject?.data?.data?.total_participant;
            const { allow_to_join = false, allow_meeting_limit = 0, total_running_meeting = 0, subscription_active = false } = participantCountObject?.data?.data ?? {};

            const partObject = {
                allow_to_join,
                allow_meeting_limit,
                total_running_meeting,
                subscription_active
            };

            if (!partObject?.allow_to_join || partObject?.total_running_meeting > partObject?.allow_meeting_limit) {
                const notifications = {
                    description: !partObject?.allow_to_join && !partObject.subscription_active
                        ? t('prejoin.subScriptionInactiveErr') : t('prejoin.parallelMeetingsLicencesErr'),
                    titleKey: t('prejoin.oops')
                };

                this.props.dispatch(showErrorNotification(notifications));
                await this.joinConferenceDirect(false);

                return;
            }

            // This condition is used when we are passing jwt from outside and not from API.
            if (!this.props._jwtData && !this.props._jwtStored) {

                const bowser = new BrowserDetection();
                const browserName = bowser.getName();
                const accessType = 'app';

                let dataJson;

                try {
                    const ClientIP = await axios({
                        url: 'https://api.ipify.org/?format=json',
                        timeout: 8000,
                        method: 'get',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    // eslint-disable-next-line no-negated-condition, max-depth
                    if (typeof ClientIP !== 'undefined') {
                        const ClientIPData = await ClientIP?.data;

                        const urlConfg1 = `${this.props._API_URL_PREFIX}${this.props._API_VERSION_PREFIX}geolocation?api_key=${apiKEY}&ip_address=${ClientIPData?.ip}`;
                        const getData = await axios({
                            url: urlConfg1,
                            timeout: 8000,
                            method: 'get',
                            headers: { 'Content-Type': 'application/json' }
                        });

                        // eslint-disable-next-line max-depth
                        if (getData) {
                            dataJson = await getData?.data;
                        }
                    }
                } catch (errork) {
                    logger.error('Error in fetching geolocation bros', errork);

                }

                const userName = this.props._displayName;
                const userEmail = this.props._email;
                const moement1 = moment().seconds(0)
                .milliseconds(0)
                .format('X');

                const mesh2561 = sha256(`${apiKEY}:MH:${moement1}`);
                const body = {
                    client_id: this.props._MH_CLIENT_ID,
                    credentials: mesh2561,
                    ...isDomainWeborNative,
                    meeting_id: this.props._roomName,
                    name: userName,
                    email_id: userEmail,
                    user_agent: browserName,
                    access_type: accessType,
                    ip: dataJson?.IPv4,
                    geolocation: dataJson

                };
                const prejoinAP = `${this.props._API_URL_PREFIX}${this.props._API_VERSION_PREFIX}${PrejoinApi}`;
                const prejoinResponse = await axios({
                    url: prejoinAP,
                    timeout: 8000,
                    method: 'post',
                    headers: { 'Content-Type': 'application/json' },
                    data: JSON.stringify(body)
                }, JSON.stringify(body)).catch(es => logger.error('Error in fetching prejoin api For guest', es));

                const bodyJSON = await prejoinResponse?.data?.data;

                // eslint-disable-next-line camelcase
                const isTrueGuest = Boolean(this.props._pregData?.allow_guest === 1); // || !this.state.verifyGoogle

                if (!isTrueGuest) {
                    this.props.dispatch(showErrorNotification({
                        description: t('prejoin.userNotAllowedToJoin'),
                        titleKey: t('prejoin.userNotAllowedToJoin')
                    }));

                    await this.joinConferenceDirect(false);

                    return;
                }

                this.props.dispatch(setPrejoinApiData(bodyJSON));


                // eslint-disable-next-line camelcase
                if (bodyJSON?.mt_token) {

                    const tokenGuest = jwtDecode(bodyJSON?.mt_token);
                    const startDate = moment(new Date())?.tz(tokenGuest.meeting?.timezone)
?.locale('en-gb')
?.utc();
                    const givenDate = moment(tokenGuest.meeting?.start_time)?.locale('en-gb')
?.utc();
                    const isAfter = startDate?.isAfter(moment(givenDate));
                    const joinAnyTimeGuest = tokenGuest && tokenGuest.meeting && tokenGuest.meeting.settings.JOIN_ANYTIME ? tokenGuest.meeting.settings.JOIN_ANYTIME : null;

                    if (participantCount >= this.props._meetingDetails?.max_participants) {
                        this.props.dispatch(showErrorNotification({
                            description: t('prejoin.oppsMaximumAllowedParticipantsErr'),
                            titleKey: t('prejoin.oppsMaximumAllowedParticipantsErr')
                        }));
                        await this.joinConferenceDirect(false);

                        return;
                    }

                    if (tokenGuest && ((joinAnyTimeGuest !== 1) && !isAfter)) {

                        const time = moment(givenDate)?.locale('en-gb')
?.local()
?.format('DD-MM-YYYY hh:mm a');

                        this.props.dispatch(showErrorNotification({
                            description: t('prejoin.meetingReminder', { time }),
                            titleKey: t('prejoin.meetingReminder', { time })
                        }));

                        await this.joinConferenceDirect(false);

                        return;
                    }

                    this.joinConferenceDirect(true);

                    return;

                }

                await this.joinConferenceDirect(true);

                return;
            }

            // Below code is for the JWT coming from GenerateJWT API - i.e setting set in Schedule Meeting or Conference UI Settings.

            const joinAnyTime = this.props._confSettings && this.props._confSettings?.JOIN_ANYTIME ? this.props._confSettings?.JOIN_ANYTIME : null;


            if (participantCount > this.props._meetingDetails.max_participants && _.isNil(this.props._jwtData)) {
                this.props.dispatch(showErrorNotification({
                    description: t('prejoin.oppsMaximumAllowedParticipantsErr'),
                    titleKey: t('prejoin.oppsMaximumAllowedParticipantsErr')
                }));
                await this.joinConferenceDirect(false);

                return;
            }

            if (this.props._jwtStored && _.isNil(this.props._jwtData)
            ) {

                const decoded = jwtDecode(this.props._jwtStored);
                const startDates = moment(new Date())?.tz(decoded?.meeting?.timezone)
?.locale('en-gb')
?.utc();
                const givenDate = moment(decoded.meeting?.start_time)?.locale('en-gb')
?.utc();
                const isAfter = startDates?.isAfter(moment(givenDate));

                if (joinAnyTime !== 1 && !isAfter) {
                    const time = moment(givenDate)?.locale('en-gb')
?.local()
?.format('DD-MM-YYYY hh:mm a');

                    this.props.dispatch(showErrorNotification({
                        description: t('prejoin.meetingReminder', { time }),
                        titleKey: t('prejoin.meetingReminder', { time })
                    }));

                    await this.joinConferenceDirect(false);

                    return;
                }
            }

            if (this.props._jwtData || this.props._jwtStored) {
                await this.joinConferenceDirect(true);
            }
        }
    }

    /**
     * Function
     * {@code onSetPage} function.
     *
     * @param {bool} k - The Boolean value.
     * @private
     * @returns {Props}
     */
    onSetPage(k) {
        this.setState({ _page: k });
    }

    /**
     * Function
     * {@code joinConferenceDirect} function.
     *
     * @param {bool} showPrejoin - ShowPrejoin or not.
     * @returns {void}
     */
    async joinConferenceDirect(showPrejoin) {
        if (showPrejoin) {
            await this.props.dispatch(connectNative());
            await this.props.dispatch(createDesiredLocalTracks());
            this.onSetPage(true);
        } else {
            this.onSetPage(false);
        }
    }

    /**
     * Function
     * {@code render} function.
     *
     * @private
     * @returns {View}
     */
    render() {
        const { t } = this.props;

        return (
            <>
                {!this.props._conference && this.state._page && <LoadConfigOverlay onText = { t('presenceStatus.connecting') } />}
                {this.props._pregData !== undefined && (this.props._pregData?.allow_guest === 0 || this.props._pregData?.success === false) && _.isNil(this.props._jwtData)
                    && <View
                        style = {{
                            position: 'absolute',
                            display: 'flex',
                            top: 0,
                            bottom: 0,
                            left: 0,
                            right: 0,
                            zIndex: 500000,
                            flex: 1,
                            backgroundColor: ColorPalette.darkblue,
                            height: '100%',
                            width: '100%'
                        }}>
                        <InvalidRoom />
                    </View>
                }
                {
                    this.props._pregData !== undefined && (this.props._pregData?.success !== false) && _.isNil(this.props._jwtData) && this.props._isTrueReg
                && <View
                    style = {{
                        position: 'absolute',
                        display: 'flex',
                        top: 0,
                        bottom: 0,
                        left: 0,
                        right: 0,
                        zIndex: 500000,
                        flex: 1,
                        backgroundColor: ColorPalette.darkblue,
                        height: '100%',
                        width: '100%'
                    }}>
                    <InvalidRoom isTrueReg = { true } />
                </View>
                }
                { !this.state._page && (
                    <SafeAreaView style = { styles.container } >
                        <PrejoinMobile
                            onhitBar = { this.onSetPage } />
                    </SafeAreaView>)}
                { this.state._page && this.props._conference
                    && <Conference />}

            </>
        );
    }

}

/**
 * Maps (parts of) the Redux state to the associated props for the
 * {@code _mapStatetoProps} component.
 *
 * @param {Object} state - The Redux state.
 * @private
 * @returns {Props}
 */
function _mapStateToProps(state) {
    const appState = state;
    const conference = state['features/base/conference'].conference;
    const jwtData = state['features/base/jwt'].jwtData;

    const { email, displayName, startWithAudioMuted: audioMuted, startWithVideoMuted: videoMuted } = state['features/base/settings'];

    const _audioMuted1 = isLocalTrackMuted(state['features/base/tracks'], MEDIA_TYPE.AUDIO);
    const _videoMuted2 = isLocalTrackMuted(state['features/base/tracks'], MEDIA_TYPE.VIDEO);
    const { userDetails, pregData } = state['features/base/mtApi'];
    const { jwt, jwtData: _jwtStored1, confSettings, meetingDetails, accessToken } = state['features/base/jwt'];
    const { API_URL_PREFIX, API_VERSION_PREFIX, MH_CLIENT_ID } = state['features/base/config'];
    const roomName = state['features/base/conference'].room;
    const { interfaceConfig } = state['features/base/config'];

    let finalJwt = jwt;

    if (jwtData) {
        const decoded = jwtDecode(jwtData);

        if (Array.isArray(decoded.context) && decoded.context.length === 0) {
            finalJwt = null;
        } else {
            finalJwt = jwtData;
        }
    }

    return {
        _appState: appState,
        _conference: conference,
        _jwt: jwt,
        _jwtData: finalJwt,
        _pregData: pregData,
        _isTrueReg: Boolean(pregData?.is_pre_registration),
        _email: email,
        _displayName: displayName,
        _startWithAudioMuted: audioMuted,
        _startWithVideoMuted: videoMuted,
        _audioMuted: _audioMuted1,
        _videoMuted: _videoMuted2,
        _userDetails: userDetails,
        _jwtStored: _jwtStored1,
        _confSettings: confSettings,
        _meetingDetails: meetingDetails,
        _accessToken: accessToken,
        _API_URL_PREFIX: API_URL_PREFIX,
        _API_VERSION_PREFIX: API_VERSION_PREFIX,
        _MH_CLIENT_ID: MH_CLIENT_ID,
        _roomName: roomName,
        _interfaceConfig: interfaceConfig

    };
}

export default translate(reduxConnect(_mapStateToProps)(PrejoinPages));
