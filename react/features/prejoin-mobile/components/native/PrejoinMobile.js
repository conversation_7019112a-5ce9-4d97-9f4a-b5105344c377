/* eslint-disable camelcase */
/* eslint-disable max-len */
/* eslint-disable react/no-multi-comp */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react-native/no-color-literals */
import { BrowserDetection } from '@jitsi/js-utils';
import axios from 'axios';
import i18next from 'i18next';
import { sha256 } from 'js-sha256';
import jwtDecode from 'jwt-decode';
import _, { isNil } from 'lodash';
import moment from 'moment-timezone';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, Animated, BackHandler, Clipboard, Dimensions, Easing, Image, KeyboardAvoidingView, Linking, Platform, SafeAreaView, ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import Collapsible from 'react-native-collapsible';
import { useDispatch, useSelector } from 'react-redux';

import { appNavigate } from '../../../app/actions';
import { getDefaultURL } from '../../../app/functions.native';
import { Avatar } from '../../../base/avatar';
import { PrejoinApi, apiKEY } from '../../../base/config/constants';
import { connect as connectNative } from '../../../base/connection';
import { Icon, IconArrowBack, IconArrowDownWide, IconArrowUpWide, IconCopy, IconMobileMembers, IconMobileMuteOff, IconMobileMuteOn, IconMobileVideoOff, IconMobileVideoOn } from '../../../base/icons';
import { parseRETURNURLFromURLParams } from '../../../base/jwt';
import { MEDIA_TYPE, VideoTrack, setAudioMuted, setVideoMuted } from '../../../base/media';
import { ASPECT_RATIO_NARROW } from '../../../base/responsive-ui/constants';
import { updateSettings } from '../../../base/settings';
import { ColorPalette } from '../../../base/styles';
import { createDesiredLocalTracks, destroyLocalDesktopTrackIfExists, getLocalVideoTrack, isLocalTrackMuted } from '../../../base/tracks';
import { parseURLParams } from '../../../base/util';
import { isDomainWeborNative } from '../../../base/util/checkOS';
import {
    FILMSTRIP_SIZE,
    isFilmstripVisible
} from '../../../filmstrip';
import { openLanguagesModal, showLanguagesListHandle } from '../../../languages-component/actions';
import LanguagesModal from '../../../languages-component/components/LanguagesModal';
import { openDialogs } from '../../../lobby/actions';
import { NotificationsContainer, showErrorNotification, showNotification, showWarningNotification } from '../../../notifications';
import { setPrejoinApiData } from '../../../prejoin/actions';
import { disableEmailOnPrejoin } from '../../../prejoin/functions';
import logger from '../../../prejoin/logger';

import PrejoinWaitng from './PrejoinWaitng';


// import {
//     AudioMuteButton,
//     VideoMuteButton
// } from '../../../toolbox/components';
const screenHeight = Dimensions.get('window').height;

const styles = StyleSheet.create({
    surface: {
        width: '80%',
        alignSelf: 'center',
        justifyContent: 'center',
        alignItems: 'center',
        elevation: 4,
        backgroundColor: '#fff',
        overflow: 'hidden',
        borderBottomWidth: 0,
        borderBottomColor: '#fff'
    },

    // card: {
    //     // borderRadius: 15,
    //     borderBottomStartRadius: 0,
    //     borderTopEndRadius: 15,
    //     borderTopStartRadius: 15,
    //     width: 299,
    //     alignSelf: 'center',
    //     margin: 10,
    //     height: 270
    // },

    scrollView: {
        backgroundColor: 'transparent'

        // backgroundColor: 'red',
    },
    hostButton: {
        alignItems: 'center',
        backgroundColor: '#182436',
        height: 50,
        borderRadius: 10,
        justifyContent: 'center'
    },
    container: {
        flex: 1,
        backgroundColor: 'transparent',
        height: 'auto',
        maxHeight: screenHeight
    }

});


const conditionalStyles = (isNeeded = false) => StyleSheet.create({
    // eslint-disable-next-line react-native/no-unused-styles
    joinButton: {
        alignItems: 'center',
        backgroundColor: isNeeded ? '#808080' : '#1ACB8C',
        height: 50,
        borderRadius: 10,
        justifyContent: 'center'
    }
});

/**
 * Prejoin Page for Native Devices Ios and Android.
 *
 * @param {any} {onSetPage}
 * @returns {any}
 */
function PrejoinMobile({ onhitBar }) {
    const dispatch = useDispatch();
    const { t } = useTranslation();
    const { email, displayName, startWithAudioMuted: audioMuted, startWithVideoMuted: videoMuted } = useSelector(state => state['features/base/settings']);
    const _localVideoTrack = useSelector(state => getLocalVideoTrack(state['features/base/tracks']));
    const [ isJoinDisable, setIsJoinDisable ] = useState(true);

    // const particpants = useSelector(state => state['features/base/participants']);
    const _audioMuted = useSelector(state => isLocalTrackMuted(state['features/base/tracks'], MEDIA_TYPE.AUDIO));
    const _videoMuted = useSelector(state => isLocalTrackMuted(state['features/base/tracks'], MEDIA_TYPE.VIDEO));
    const { userDetails, pregData } = useSelector(state => state['features/base/mtApi']);
    const { jwt, jwtData: _jwtStored, confSettings, meetingDetails, accessToken } = useSelector(state => state['features/base/jwt']);
    const config = useSelector(state => state['features/base/config']);
    const { API_URL_PREFIX, API_VERSION_PREFIX, MH_CLIENT_ID, interfaceConfig } = config;
    const roomName = useSelector(state => state['features/base/conference'].room);
    const disableEmail = disableEmailOnPrejoin(useSelector(state => state));

    // const language = useSelector(state => state['features/subtitles']._language);
    // const languages = translationLanguages
    //     .map((lang) => `${lang}`)
    //     .filter((lang: string) => !(lang === subtitles || languagesHead?.includes(lang)));
    // const listItems = (fixedItems?.includes(subtitles)
    //     ? [ ...fixedItems, ...languages ]
    //     : [ ...fixedItems, subtitles, ...languages ])
    //     .map((lang, index) => {
    //         return {
    //             id: lang + index,
    //             lang,
    //             selected: lang === subtitles
    //         };
    //     });

    const { locationURL } = useSelector(state => state['features/base/connection']);
    const [ conferenceCheck, setConferenceCheck ] = useState(false);
    const { aspectRatio } = useSelector(state => state['features/base/responsive-ui']);
    const animationHeight = React.useRef(new Animated.Value(0)).current;
    const _filmstripVisible = useSelector(state => isFilmstripVisible(state));
    const [ returnurlParameter, setRetrunurlParameter ] = useState(null);
    const [ videoOn, setVideoOn ] = useState(_videoMuted);
    const [ audioOn, setAudioOn ] = useState(_audioMuted);
    const [ partCount, setPartCount ] = useState(0);
    const [ loader, setLoader ] = useState(false);
    const [ expanded, setExpanded ] = React.useState(false);
    const defaultUrl = useSelector(state => getDefaultURL(state));
    const [ partObject, setPartObject ] = React.useState({});
    const [ backArrow, setBackArrow ] = React.useState(null);
    const { _lobbyScreen } = useSelector(state => state['features/lobby']);

    React.useEffect(() => {
        if (locationURL) {
            const returnUrl = parseRETURNURLFromURLParams(locationURL);
            const goBackUrlEncoded = parseURLParams(locationURL, true, 'search')?.goBack;
            const decodedUri = returnUrl ? decodeURIComponent(returnUrl) : null;

            // console.log('goBackUrlEncoded', goBackUrlEncoded);
            if (goBackUrlEncoded) {
                const goBackUrl = decodeURIComponent(goBackUrlEncoded);

                setBackArrow(goBackUrl);
            }

            if (decodedUri) {
                setRetrunurlParameter(decodedUri);
            }
        }
    }, []);

    const checkPart = async (breakLoop = false) => {
        if (breakLoop) {
            // console.log('checkPart || -> breakLoop');

            return null;

        }

        const moement = moment().seconds(0)
            .milliseconds(0)
            .format('X');

        const headers = _.isNil(accessToken) ? {
            'Content-Type': 'application/json'
        } : {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${accessToken}`
        };

        const mesh256 = sha256(`${apiKEY}:MH:${moement}`);
        const urlConfg = `${API_URL_PREFIX}${API_VERSION_PREFIX}meeting/totalparticipants`;
        const participantCountObject = await axios({
            url: urlConfg,
            data: JSON.stringify({
                client_id: MH_CLIENT_ID,
                credentials: mesh256,
                ...isDomainWeborNative,
                meeting_id: roomName
            }),
            headers,
            method: 'post'
        });
        // eslint-disable-next-line camelcase
        const participantCount = participantCountObject?.data?.data?.total_participant;
        const { allow_to_join = false, allow_meeting_limit = 0, total_running_meeting = 0, subscription_active = false } = participantCountObject?.data?.data ?? {};

        setPartObject({
            allow_to_join,
            allow_meeting_limit,
            total_running_meeting,
            subscription_active
        });

        if (isJoinDisable === true && !_.isNil(participantCount)) {
            setIsJoinDisable(false);
        }

        setPartCount(participantCount);
        setTimeout(checkPart, 10000);

    };

    React.useLayoutEffect(() => {
        dispatch(destroyLocalDesktopTrackIfExists());

        // Make sure we don't request the permission for the camera from
        // the start. We will, however, create a video track iff the user
        // already granted the permission.
        navigator.permissions.query({ name: 'camera' }).then(response => {
            (response === 'prompt' || response === 'granted')
                && dispatch(createDesiredLocalTracks(MEDIA_TYPE.VIDEO));

            // console.log('layoutEffected', audioMuted, videoMuted);

            // if (audioMuted === true && audioOn === false) {
            //     dispatch(setAudioMuted(true));

            //     // setTimeout(() => setAudioOn(false), 300);
            // } else {
            //     setTimeout(() => setAudioOn(true), 300);

            // }
            // if (videoMuted === true && videoOn === false) {
            //     dispatch(setVideoMuted(true));

            //     // setTimeout(() => setVideoOn(false), 300);
            // } else {
            //     setTimeout(() => setVideoOn(true), 300);

            // }
        });
        checkPart();

        return () => checkPart(true);
    }, []);

    React.useEffect(() => {
        // eslint-disable-next-line camelcase
        if (partCount <= meetingDetails?.max_participants && conferenceCheck) {
            onhitBar(true);
        }

        return () => {
            if (partCount <= 2 && conferenceCheck) {
                dispatch(connectNative());
                setLoader(false);
                dispatch(createDesiredLocalTracks());
                clearTimeout(checkPart);
                onhitBar(true);

            }
        };
    }, [ partCount ]);


    const renderNotificationContainer = () => {
        const notificationsStyle = {
            position: 'absolute',
            top: 20,
            width: '100%'
        };

        if (_filmstripVisible && aspectRatio !== ASPECT_RATIO_NARROW) {
            notificationsStyle.marginRight = FILMSTRIP_SIZE;
        }

        return (
            React.createElement(NotificationsContainer, {
                style: notificationsStyle
            })
        );
    };


    React.useEffect(() => {
        if (audioMuted === true && audioOn === false) {
            dispatch(setAudioMuted(true));
        } else {
            setTimeout(() => setAudioOn(!audioOn), 300);

        }
        if (videoMuted === true && videoOn === false) {
            dispatch(setVideoMuted(true));
        } else {
            setTimeout(() => setVideoOn(!videoOn), 300);

        }


    }, []);

    /**
     * Toggle Expansion.
     *
     * @returns {any}
     */
    function toggleExpansion() {
        setExpanded(!expanded);
    }

    /**
 * Redirects to another page generated by replacing the path in the original URL
 * with the given path.
 *
 * @param {(string)} pathname - The path to navigate to.
 * @returns {Void}
 */
    // function onShowSideBar() {
    //     dispatch(appNavigate(defaultUrl));
    // }


    // const backAction = () => {
    //     console.log('backActionss');
    //     setBackButton();

    //     return true;
    // };

    const backHandler = BackHandler.addEventListener(
        'hardwareBackPress',
        setBackButton
    );

    React.useEffect(() => () => backHandler.remove(), []);

    /**
     * On and off video Toggle.
     *
     * @returns {any}
     */
    function onOffVideo() {
        dispatch(setVideoMuted(videoOn));
        setVideoOn(!videoOn);
    }

    /**
     * On and off Audio Toggle.
     *
     * @returns {any}
     */
    function onOffAudio() {
        dispatch(setAudioMuted(audioOn));
        setAudioOn(!audioOn);
    }

    /**
     * On and off Audio Toggle.
     *
     * @param {string} params - Text value for displayname.
     * @returns {any}
     */
    function onChangeName(params) {
        dispatch(updateSettings({
            displayName: params
        }));
    }

    /**
     * On and off Audio Toggle.
     *
     * @param {string} params - Text value for email.
     * @returns {any}
     */
    function onChangeEmail(params) {
        dispatch(updateSettings({
            email: params.trim()
        }));
    }

    React.useEffect(() => {
        if (expanded) {
            Animated.timing(animationHeight, {
                duration: 1000,
                toValue: 60,
                easing: Easing.linear,
                useNativeDriver: false
            }).start();
        } else {
            Animated.timing(animationHeight, {
                duration: 1000,
                toValue: 5,
                easing: Easing.linear,
                useNativeDriver: false
            }).start();
        }

    }, [ expanded ]);


    const prejoinWaiting = () => dispatch(openDialogs(PrejoinWaitng, {
        _isLobbyScreenVisible: _lobbyScreen?._isLobbyScreenVisible,
        _isPrejoin: _lobbyScreen?._isPrejoin
    }));

    /**
     * On and off Audio Toggle.
     *
     * @returns {any}
     */
    function onJoinMeet() {
        // dispatch(connectNative())
        setLoader(true);
        onJoinButtonClick();

    }

    /**
     * Handler for the join button.
     *
     * @param {boolean} audiol - The synthetic event.
     * @returns {void}
     */
    async function onJoinButtonClick() {
        if (_.isEmpty(displayName) || (!disableEmail && _.isEmpty(email))) {
            dispatch(showErrorNotification({
                description: _.isEmpty(displayName) ? t('prejoin.pleaseEnterFullName') : t('prejoin.pleaseEnterEmail'),
                titleKey: _.isEmpty(displayName) ? t('prejoin.pleaseEnterFullName') : t('prejoin.pleaseEnterEmail')
            }));
            setLoader(false);

            return;
        }
        const regex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/;

        if (!disableEmail && email && !email.match(regex)) {
            dispatch(showErrorNotification({
                description: t('prejoin.invalidEmail'),
                titleKey: t('prejoin.invalidEmail')
            }));
            setLoader(false);

            return;
        }

        if (!partObject?.allow_to_join || partObject?.total_running_meeting > partObject?.allow_meeting_limit) {
            const notifications = {
                description: !partObject?.allow_to_join && !partObject.subscription_active ? t('prejoin.subScriptionInactiveErr') : t('prejoin.parallelMeetingsLicencesErr'),
                titleKey: t('prejoin.oops')
            };

            setLoader(false);
            dispatch(showErrorNotification(notifications));

            return;
        }


        if (!jwt && !_jwtStored) {

            const bowser = new BrowserDetection();
            const browserName = bowser.getName();
            const accessType = 'app';

            let dataJson;

            try {
                const ClientIP = await axios({
                    url: 'https://api.ipify.org/?format=json',
                    timeout: 8000,
                    method: 'get',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                // eslint-disable-next-line no-negated-condition
                if (typeof ClientIP !== 'undefined') {

                    const ClientIPData = await ClientIP?.data;

                    const urlConfg = `${API_URL_PREFIX}${API_VERSION_PREFIX}geolocation?api_key=${apiKEY}&ip_address=${ClientIPData?.ip}`;
                    const getData = await axios({
                        url: urlConfg,
                        timeout: 8000,
                        method: 'get',
                        headers: { 'Content-Type': 'application/json' }
                    });

                    // eslint-disable-next-line max-depth
                    if (getData) {
                        dataJson = await getData?.data;
                    }
                }
            } catch (errork) {
                logger.error('Error in fetching geolocation bros', errork);

            }


            const userName = displayName;
            const userEmail = email;
            const moement = moment().seconds(0)
                .milliseconds(0)
                .format('X');

            const mesh256 = sha256(`${apiKEY}:MH:${moement}`);
            const body = {
                client_id: MH_CLIENT_ID,
                credentials: mesh256,
                ...isDomainWeborNative,
                meeting_id: roomName,
                name: userName,
                email_id: userEmail,
                user_agent: browserName,
                access_type: accessType,
                ip: dataJson?.IPv4,
                geolocation: dataJson

            };
            const prejoinAP = `${API_URL_PREFIX}${API_VERSION_PREFIX}${PrejoinApi}`;
            const prejoinResponse = await axios({
                url: prejoinAP,
                timeout: 8000,
                method: 'post',
                headers: { 'Content-Type': 'application/json' },
                data: JSON.stringify(body)
            }, JSON.stringify(body)).catch(es => logger.error('Error in fetching prejoin api For guest', es));

            const bodyJSON = await prejoinResponse?.data?.data;

            // eslint-disable-next-line camelcase
            const isTrueGuest = Boolean(pregData?.allow_guest === 1); // || !this.state.verifyGoogle

            if (!isTrueGuest) {
                dispatch(showErrorNotification({
                    description: t('prejoin.userNotAllowedToJoin'),
                    titleKey: t('prejoin.userNotAllowedToJoin')
                }));
                setLoader(false);

                return;
            }

            dispatch(setPrejoinApiData(bodyJSON));


            // eslint-disable-next-line camelcase
            if (bodyJSON?.mt_token) {

                const tokenGuest = jwtDecode(bodyJSON?.mt_token);
                const startDate = moment(new Date())?.tz(tokenGuest.meeting?.timezone)
?.locale('en-gb')
?.utc();
                const givenDate = moment(tokenGuest.meeting?.start_time)?.locale('en-gb')
?.utc();
                const isAfter = startDate?.isAfter(moment(givenDate));
                const joinAnyTimeGuest = tokenGuest && tokenGuest.meeting && tokenGuest.meeting.settings.JOIN_ANYTIME ? tokenGuest.meeting.settings.JOIN_ANYTIME : null;

                if (partCount >= meetingDetails?.max_participants) {
                    dispatch(showErrorNotification({
                        description: t('prejoin.oppsMaximumAllowedParticipantsErr'),
                        titleKey: t('prejoin.oppsMaximumAllowedParticipantsErr')
                    }));
                    setConferenceCheck(true);

                    return;
                }

                if (tokenGuest && ((joinAnyTimeGuest !== 1) && !isAfter)) {

                    const time = moment(givenDate)?.locale('en-gb')
?.local()
?.format('DD-MM-YYYY hh:mm a');


                    dispatch(showErrorNotification({
                        description: t('prejoin.meetingReminder', { time }),
                        titleKey: t('prejoin.meetingReminder', { time })
                    }));
                    setLoader(false);

                    return;
                }

                // eslint-disable-next-line radix
                if (!(parseInt(partCount) >= 1) && isNil(jwt)) {

                    showWarningNotification({
                        description: t('prejoin.waitForModeratorMsgDynamic', { Moderator: interfaceConfig?.CHANGE_MODERATOR_NAME ? interfaceConfig?.CHANGE_MODERATOR_NAME : 'Moderator' }),
                        titleKey: t('prejoin.waitForModeratorMsgDynamic', { Moderator: interfaceConfig?.CHANGE_MODERATOR_NAME ? interfaceConfig?.CHANGE_MODERATOR_NAME : 'Moderator' })
                    });

                    onhitBar(true);
                    setLoader(false);
                    clearTimeout(checkPart);

                    prejoinWaiting();

                }

                // .fetchJwtStorage(bodyJSON?.mt_token);
                // if(!._videoMuted){
                //     ._appSetMuted(true)
                //     ._setVideoMuted(!._videoMuted)
                // }

                // connect();
                dispatch(connectNative());
                setLoader(false);
                dispatch(createDesiredLocalTracks());
                clearTimeout(checkPart);
                onhitBar(true);

                return;

                // dispatch(createDesiredLocalTracks());

            }


            // this.setState({ isLoading: false });
            // .addLoader(false);

            return;
        }

        const joinAnyTime = confSettings && confSettings?.JOIN_ANYTIME ? confSettings?.JOIN_ANYTIME : null;


        if (partCount > meetingDetails.max_participants && _.isNil(jwt)) {
            dispatch(showErrorNotification({
                description: t('prejoin.oppsMaximumAllowedParticipantsErr'),
                titleKey: t('prejoin.oppsMaximumAllowedParticipantsErr')
            }));
            setConferenceCheck(true);

            return;
        }
        if (_jwtStored && _.isNil(jwt)
        ) {

            const decoded = jwtDecode(_jwtStored);
            const startDates = moment(new Date())?.tz(decoded?.meeting?.timezone)
?.locale('en-gb')
?.utc();
            const givenDate = moment(decoded.meeting?.start_time)?.locale('en-gb')
?.utc();
            const isAfter = startDates?.isAfter(moment(givenDate));

            if (joinAnyTime !== 1 && !isAfter) {
                const time = moment(givenDate)?.locale('en-gb')
?.local()
?.format('DD-MM-YYYY hh:mm a');

                dispatch(showErrorNotification({
                    description: t('prejoin.meetingReminder', { time }),
                    titleKey: t('prejoin.meetingReminder', { time })
                }));
                setLoader(false);

                return;
            }
        }

        // eslint-disable-next-line radix
        if (!(parseInt(partCount) >= 1) && isNil(jwt)) {

            showWarningNotification({
                description: t('prejoin.waitForModeratorMsg'),
                titleKey: t('prejoin.waitForModeratorMsg')
            });

            onhitBar(true);
            setLoader(false);
            clearTimeout(checkPart);

            prejoinWaiting();

            return;
        }


        // if(!._videoMuted){
        //     ._appSetMuted(true)
        //     ._setVideoMuted(!._videoMuted)
        // }
        if (jwt || _jwtStored) {
            dispatch(connectNative());
            setLoader(false);
            dispatch(createDesiredLocalTracks());
            clearTimeout(checkPart);
            onhitBar(true);

            return;
        }

    }

    let meetingTime;
    const meetingDate = pregData?.meeting_start_time;

    if (meetingDate) {
        meetingTime = moment(pregData?.meeting_start_time).locale('en-gb')
            .format('DD MMM YYYY - h:mm A');
    }

    /**
     * Open external Link.
     *
     * @returns {any}
     */
    function onOpenLink() {
        Linking.openURL(`${returnurlParameter}`).catch(err => logger.error("Couldn't load page", err));
    }

    /**
     *
     * Backbutton gets called.
     *
     * @returns {any}
     */
    function setBackButton() {
        if (backArrow) {
            Linking.openURL(`${backArrow}`).catch(err => logger.error("Couldn't load page", err));

        }
        dispatch(updateSettings({
            accessToken: null,
            email: null,
            displayName: null
        }));

        dispatch(appNavigate(undefined));

        if (backArrow) {
            BackHandler.exitApp();
        }

        return true;
    }

    return (
        <>
            <SafeAreaView
                style = {{
                    flex: 1,
                    backgroundColor: interfaceConfig?.applyMeetingSettings === true ? interfaceConfig?.DEFAULT_BACKGROUND : ColorPalette.darkblue,
                    height: 'auto',
                    maxHeight: screenHeight
                }} >
                <View style = { styles.container }>
                    <KeyboardAvoidingView
                        behavior = { Platform.OS === 'ios' ? 'position' : null }
                        keyboardVerticalOffset = { Platform.OS === 'ios' ? 20 : 70 }
                        style = {{ flex: 1 }}>
                        <ScrollView
                            contentContainerStyle = {{ flexGrow: 1 }}
                            keyboardShouldPersistTaps = 'handled'
                            style = { styles.scrollView }>

                            <View
                                style = {{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    marginTop: Platform.OS === 'ios' ? 10 : null,
                                    opacity: 1
                                }}>
                                {
                                    <TouchableOpacity
                                        onPress = { setBackButton } >
                                        <Icon
                                            src = { IconArrowBack }
                                            style = {{
                                                color: 'white',
                                                fontSize: 22,
                                                padding: 8,
                                                marginLeft: 5
                                            }} />
                                    </TouchableOpacity>
                                }
                                <View

                                    style = {{
                                        marginLeft: 5,
                                        padding: 8,
                                        flexDirection: 'row',
                                        justifyContent: 'space-between',
                                        alignItems: 'center'
                                    }} >
                                    <TouchableOpacity
                                        onPress = { () => {
                                            dispatch(openLanguagesModal());
                                            dispatch(showLanguagesListHandle());
                                        } }
                                        style = {{
                                            marginLeft: 5,
                                            padding: 8,
                                            backgroundColor: '#273c61',
                                            marginRight: 5,
                                            borderRadius: 6,
                                            display: 'flex',
                                            flexDirection: 'row',
                                            alignItems: 'center'
                                        }} >
                                        <Text
                                            style = {{ color: 'white',
                                                marginRight: 3 }}>{t(`languages:${i18next.language}`)}</Text>
                                        <Icon
                                            src = { IconArrowDownWide }
                                            style = {{
                                                color: '#273c61',
                                                fontSize: 10
                                            }} />
                                    </TouchableOpacity>
                                    <Avatar
                                        displayName = { displayName }
                                        dynamicColor = { true }
                                        participantId = 'local'
                                        size = { 30 }
                                        url = { userDetails ? userDetails?.picture : null } />
                                </View>

                            </View>
                            { typeof config.disableInviteFunctions !== 'undefined' && config.disableInviteFunctions === true ? <></> : <>
                                <Text
                                    style = {{
                                        alignSelf: 'center',
                                        color: 'white',
                                        fontWeight: '400',
                                        padding: 8,
                                        fontSize: 20,
                                        marginLeft: 10,
                                        marginBottom: 10
                                    }} >
                                    { t('welcomepage.meetingIsReady') }
                                </Text>
                                <View
                                    style = { [ styles.surface, {
                                        backgroundColor: '#273c61',
                                        padding: 5,
                                        marginBottom: 20,
                                        borderTopLeftRadius: 15,
                                        borderTopRightRadius: 15,
                                        borderBottomRightRadius: 15,
                                        borderBottomLeftRadius: 15
                                    } ] }>
                                    <TouchableOpacity
                                        onPress = { toggleExpansion }>
                                        <View
                                            style = {{
                                                display: 'flex',
                                                flexDirection: 'row'
                                            }}>
                                            <Text
                                                style = {{
                                                    alignSelf: 'center',
                                                    color: 'white',
                                                    fontSize: 16,
                                                    fontWeight: '400'
                                                }}>
                                                { t('welcomepage.meetingDetails') }
                                            </Text>
                                            <Icon
                                                src = { expanded ? IconArrowUpWide : IconArrowDownWide }
                                                style = {{
                                                    color: '#273c61',
                                                    fontSize: 15,
                                                    margin: 5
                                                }} />
                                        </View>
                                    </TouchableOpacity>
                                    <Collapsible collapsed = { !expanded }>
                                        <View
                                            style = {{
                                                paddingTop: 10,
                                                paddingBottom: 10,
                                                width: 245,
                                                display: 'flex',
                                                flexDirection: 'row',
                                                justifyContent: 'space-between'
                                            }}>
                                            <Text
                                                style = {{
                                                    color: 'white',
                                                    fontSize: 17,
                                                    fontWeight: '400'
                                                }}>{ `${defaultUrl}/${roomName}` }</Text>
                                            <TouchableOpacity
                                                // eslint-disable-next-line react/jsx-no-bind
                                                onPress = { () => {
                                                    Clipboard.setString(`${defaultUrl}/${roomName}`);
                                                    dispatch(showNotification({
                                                        description: 'Meeting Url Copied',
                                                        titleKey: 'Meeting Url Copied'
                                                    }));
                                                } } >
                                                <Icon
                                                    size = { 17 }
                                                    src = { IconCopy }
                                                    style = {{
                                                        paddingTop: 3,
                                                        alignItems: 'center',
                                                        marginRight: 3
                                                    }} />
                                            </TouchableOpacity>
                                        </View>
                                        <View>
                                            {
                                                pregData && pregData?.meeting_start_time && pregData?.timezone && (
                                                    <>
                                                        <View
                                                            style = {{
                                                                borderBottomColor: 'black',
                                                                borderBottomWidth: 1
                                                            }} />
                                                        <Text
                                                            style = {{
                                                                marginTop: 5,
                                                                marginBottom: 5,
                                                                fontSize: 15,
                                                                color: '#fff'
                                                            }}>
                                                            { t('welcomepage.meetingDate') }:  { meetingTime }
                                                        </Text>
                                                        <View
                                                            style = {{
                                                                borderBottomColor: 'black',
                                                                borderBottomWidth: 1
                                                            }} />
                                                        <Text
                                                            style = {{
                                                                marginTop: 5,
                                                                marginBottom: 5,
                                                                fontSize: 15,
                                                                color: '#fff'
                                                            }}>
                                                            { t('welcomepage.timezone') }:   { pregData.timezone }
                                                        </Text>
                                                    </>
                                                )
                                            }
                                        </View>
                                    </Collapsible>
                                </View>
                            </>
                            }

                            <View
                                style = {{
                                    backgroundColor: '#030811',
                                    width: '80%',
                                    height: 50,
                                    alignSelf: 'center',
                                    color: '#fff',
                                    borderTopLeftRadius: 15,
                                    borderTopRightRadius: 15,
                                    justifyContent: 'center'
                                }}>
                                <Text
                                    style = {{
                                        alignSelf: 'center',
                                        color: '#fff',
                                        fontWeight: '300'
                                    }}>
                                    { displayName && typeof displayName !== 'object' ? displayName : t('Guest User') }
                                </Text>
                            </View>
                            <View>

                                <View
                                    style = {{
                                        // position: 'relative',
                                        alignSelf: 'center',
                                        position: 'relative',
                                        top: 0,
                                        left: 0,
                                        bottom: 0,
                                        right: 0,
                                        height: 235,
                                        width: '80%',
                                        justifyContent: videoOn === false ? 'center' : null,
                                        alignItems: videoOn === false ? 'center' : null,
                                        backgroundColor: videoOn === false ? '#2B3541' : null
                                    }}>
                                    { videoOn ? <VideoTrack videoTrack = { _localVideoTrack } />
                                        : <Avatar
                                            displayName = { displayName }
                                            dynamicColor = { true }
                                            participantId = 'local'
                                            size = { 100 }
                                            url = { userDetails ? userDetails?.picture : null } /> }
                                </View>
                                <View
                                    style = {{
                                        position: 'relative',
                                        top: 0,
                                        left: 0,
                                        bottom: 0,
                                        right: 0,
                                        flexDirection: 'row',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        alignSelf: 'center',
                                        backgroundColor: '#2B3541',
                                        height: 60,
                                        width: '80%',
                                        marginLeft: 1,
                                        borderBottomLeftRadius: 15,
                                        borderBottomRightRadius: 15
                                    }}>
                                    <View
                                        style = {{
                                            margin: 5,
                                            backgroundColor: '#030811',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            opacity: 0.9,
                                            height: 45,
                                            width: 45,
                                            borderRadius: 5
                                        }}>
                                        <TouchableOpacity onPress = { onOffAudio }>
                                            <Icon
                                                size = { 20 }
                                                src = { audioOn ? IconMobileMuteOn : IconMobileMuteOff }
                                                style = {{
                                                    color: 'white'

                                                }} />
                                        </TouchableOpacity>
                                    </View>
                                    <View
                                        style = {{
                                            margin: 5,
                                            backgroundColor: '#030811',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            opacity: 0.9,
                                            height: 45,
                                            width: 45,
                                            borderRadius: 5
                                        }}>
                                        <TouchableOpacity onPress = { onOffVideo }>
                                            <Icon
                                                size = { 20 }
                                                src = { videoOn ? IconMobileVideoOn : IconMobileVideoOff }
                                                style = {{
                                                    color: 'white'

                                                }} />

                                        </TouchableOpacity>
                                    </View>
                                </View>
                                {
                                    loader ? (<View
                                        style = {{
                                            justifyContent: 'center',
                                            alignItems: 'center'
                                        }}>
                                        { /* <Icon
                                    src = { IconLoader2 }
                                    size = {120 }
                                    /> */ }
                                        <Image
                                            source = { require('../../../../../images/loading2.gif') }
                                            style = {{
                                                width: 120,
                                                height: 120
                                            }} />
                                        <Text
                                            style = {{
                                                color: '#fff',
                                                marginTop: 10,
                                                fontSize: 20,
                                                textAlign: 'center',
                                                fontWeight: 'bold'
                                            }}>{ partCount > meetingDetails?.max_participants ? t('prejoin.oppsMaximumAllowedParticipantsErr') : t('presenceStatus.connecting') }</Text>
                                    </View>) : (<View
                                        style = {{
                                            backgroundColor: 'transparent',
                                            marginTop: 10
                                        }}>
                                        <Text
                                            style = {{
                                                alignSelf: 'center',
                                                color: 'white',
                                                fontWeight: '400',
                                                padding: 8,
                                                fontSize: 20,
                                                marginLeft: 10,
                                                marginBottom: 10
                                            }} >
                                            { t('prejoin.readyToJoin') }
                                        </Text>
                                        <View
                                            style = {{
                                                top: 10,
                                                flexDirection: 'row',
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                alignSelf: 'center',
                                                width: 200,
                                                backgroundColor: '#2B3541',
                                                height: 30,
                                                marginLeft: 1,
                                                borderRadius: 10
                                            }}>
                                            <Icon
                                                size = { 15 }
                                                src = { IconMobileMembers }
                                                style = {{
                                                    color: 'white'
                                                }} />
                                            <Text
                                                style = {{
                                                    color: '#fff',
                                                    paddingLeft: 5
                                                }}>{ partCount ? partCount : 0 } { t('prejoin.peopleInTheCall') }</Text>
                                        </View>
                                        { userDetails ? (<View
                                            style = {{
                                                marginTop: 20,
                                                borderRadius: 10,
                                                justifyContent: 'center',
                                                alignItems: 'center'
                                            }}>
                                            <Text
                                                style = {{
                                                    color: '#fff',
                                                    paddingLeft: 5,
                                                    fontSize: 20,
                                                    fontWeight: 'bold'
                                                }}>{ displayName }</Text>
                                        </View>) : (<View
                                            style = {{
                                                marginTop: 20,
                                                backgroundColor: '#fff',
                                                width: '80%',
                                                alignSelf: 'center',
                                                borderRadius: 10,
                                                height: 50,
                                                justifyContent: 'center'
                                            }}>
                                            <TextInput
                                                onChangeText = { onChangeName }
                                                placeholder = { t('dialog.enterDisplayName') }
                                                placeholderTextColor = '#666565'
                                                style = {{
                                                    color: '#000'
                                                }}
                                                textAlign = { 'center' }
                                                value = { displayName } />
                                        </View>) }
                                        { userDetails ? <View
                                            style = {{
                                                marginTop: 20,
                                                borderRadius: 10,
                                                justifyContent: 'center',
                                                alignItems: 'center'
                                            }}>
                                            <Text
                                                style = {{
                                                    color: '#fff',
                                                    paddingLeft: 5,
                                                    fontSize: 20,
                                                    fontWeight: 'bold'
                                                }}>{ email }</Text>
                                        </View> : disableEmail ? <></> : <View
                                            style = {{
                                                marginTop: 20,
                                                width: '80%',
                                                alignSelf: 'center',
                                                backgroundColor: '#fff',
                                                borderRadius: 10,
                                                height: 50,
                                                justifyContent: 'center'
                                            }}>
                                            <TextInput
                                                onChangeText = { onChangeEmail }
                                                placeholder = { t('dialog.enterDisplayEmail') }
                                                placeholderTextColor = '#666565'
                                                style = {{
                                                    color: '#000'
                                                }}
                                                textAlign = { 'center' }
                                                value = { email } />
                                        </View> }
                                        <View
                                            style = {{
                                                marginTop: 20,
                                                width: '80%',
                                                alignSelf: 'center',
                                                backgroundColor: 'transparent',
                                                borderRadius: 10,
                                                height: 50,
                                                justifyContent: 'center'
                                            }}>
                                            <TouchableOpacity

                                                disabled = { isJoinDisable }
                                                onPress = { onJoinMeet }
                                                style = { conditionalStyles().joinButton }>
                                                {
                                                    isJoinDisable ? <ActivityIndicator />
                                                        : <Text
                                                            style = {{ color: '#fff' }}
                                                            textAlign = { 'center' }>{ t('lobby.joinTitle') }</Text>
                                                }
                                            </TouchableOpacity>
                                        </View>
                                        {
                                            !_.isNil(returnurlParameter) && _.isNil(accessToken) && (<View
                                                style = {{
                                                    top: 10,
                                                    flexDirection: 'row',
                                                    justifyContent: 'center',
                                                    alignItems: 'center',
                                                    height: 30,
                                                    alignSelf: 'center',
                                                    width: '80%',
                                                    marginLeft: 1,
                                                    borderRadius: 10
                                                }}>
                                                <View
                                                    style = {{
                                                        flex: 1,
                                                        width: '100%',
                                                        height: 1,
                                                        backgroundColor: 'black'
                                                    }} />
                                                <View
                                                    style = {{
                                                        marginLeft: 5,
                                                        marginRight: 5,
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        alignSelf: 'center'
                                                    }}>
                                                    <Text style = {{ color: '#fff' }}>{ t('prejoin.or') }</Text>
                                                </View>
                                                <View
                                                    style = {{
                                                        flex: 1,
                                                        width: '100%',
                                                        height: 1,
                                                        backgroundColor: 'black'
                                                    }} />
                                            </View>)
                                        }
                                        {
                                            !_.isNil(returnurlParameter) && _.isNil(accessToken) && (<View
                                                style = {{
                                                    marginTop: 20,
                                                    backgroundColor: '#182436',
                                                    borderRadius: 10,
                                                    height: 50,
                                                    width: '80%',
                                                    alignSelf: 'center',
                                                    marginBottom: 20,
                                                    justifyContent: 'center'
                                                }}>
                                                <TouchableOpacity
                                                    onPress = { onOpenLink }
                                                    style = { styles.hostButton }>
                                                    <Text
                                                        style = {{ color: '#fff' }}
                                                        textAlign = { 'center' }>{ t('prejoin.signinsignup') }</Text>
                                                </TouchableOpacity>
                                            </View>)
                                        }
                                    </View>)
                                }
                            </View>
                        </ScrollView>

                    </KeyboardAvoidingView>
                    <LanguagesModal key = 'languageModal' />
                    { renderNotificationContainer() }
                </View>
            </SafeAreaView>
        </>
    );
}

export default PrejoinMobile;
