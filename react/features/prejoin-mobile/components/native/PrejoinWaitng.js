/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react-native/no-color-literals */
import axios from 'axios';
import { sha256 } from 'js-sha256';
import _ from 'lodash';
import { ReactInterval } from 'meet-hour-react-interval-hook';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { BackHandler, Dimensions, Image, KeyboardAvoidingView,
    Platform, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Collapsible from 'react-native-collapsible';
import { useDispatch, useSelector } from 'react-redux';

import { getDefaultURL } from '../../../app/functions';
import { cancelWaitForOwner } from '../../../authentication/actions';
import { Avatar } from '../../../base/avatar';
import { apiKEY } from '../../../base/config/constants';
import { connect as connectNative } from '../../../base/connection';
import { AudioOffMute, AudioOn, Icon, IconArrowBack, IconArrowDownWide, IconArrowUpWide, IconCopy } from '../../../base/icons';
import { updateSettings } from '../../../base/settings';
import { ColorPalette } from '../../../base/styles';
import { createDesiredLocalTracks } from '../../../base/tracks';
import { isDomainWeborNative } from '../../../base/util/checkOS';
import { StartSound as startSound } from '../../../lobby/actions.any';
import { openLobbyScreen, startKnocking } from '../../../lobby/actions.web';
import { NotificationsContainer } from '../../../notifications';

const screenHeight = Dimensions.get('window').height;

const styles = StyleSheet.create({
    // eslint-disable-next-line react-native/no-unused-styles

    container: {
        flex: 1,
        backgroundColor: ColorPalette.darkblue,
        height: 'auto',
        maxHeight: screenHeight,
        position: 'absolute',
        display: 'flex',
        top: Platform.OS === 'ios' ? '5%' : 0,
        bottom: 0,
        left: 0,
        right: 0,
        marginTop: Platform.OS === 'ios' ? 10 : null,
        zIndex: 5000000,
        width: '100%'
    },
    // eslint-disable-next-line react-native/no-unused-styles
    input: {
        height: 45,
        width: '100%',
        backgroundColor: '#fff',
        borderRadius: 10
    }
});

/**
 * Prejoin Waitng Js.
 *
 * @returns {any}
 */
export default function PrejoinWaitng({ _isLobbyScreenVisible, _isPrejoin }) {
    const [ audioOn, setAudioOn ] = useState(true);
    const dispatch = useDispatch();
    const { t } = useTranslation();
    const { displayName } = useSelector(state => state['features/base/settings']);
    const { userDetails } = useSelector(state => state['features/base/mtApi']);
    const { soundBool: _soundBool } = useSelector(state => state['features/lobby']);
    const [ partCount, setPartCount ] = useState(null);
    const { accessToken } = useSelector(state => state['features/base/jwt']);
    const { API_URL_PREFIX, API_VERSION_PREFIX, MH_CLIENT_ID } = useSelector(state => state['features/base/config']);
    const { interfaceConfig } = useSelector(state => state['features/base/config']);
    const roomName = useSelector(state => state['features/base/conference'].room);
    const defaultUrl = useSelector(state => getDefaultURL(state));
    const intervalRef = React.useRef(null);
    const { pregData } = useSelector(state => state['features/base/mtApi']);
    const [ expanded, setExpanded ] = React.useState(false);
    const config = useSelector(state => state['features/base/config']);

    let meetingTime;
    const meetingDate = pregData?.meeting_start_time;

    if (meetingDate) {
        meetingTime = moment(pregData?.meeting_start_time).locale('en-gb')
            .format('DD MMM YYYY - h:mm A');
    }

    useEffect(() => {
        if (_isLobbyScreenVisible && !_isPrejoin) {
            dispatch(startKnocking());
        }
        if (_isLobbyScreenVisible && _isPrejoin) {
            dispatch(openLobbyScreen(_isPrejoin));
            dispatch(startSound(true));
            setAudioOn(true);

        }

        if (_soundBool === false && audioOn === false) {
            dispatch(startSound(!_soundBool));
            setAudioOn(!audioOn);
        }
    }, [ _isLobbyScreenVisible, _isPrejoin ]);

    /** .........
     * 描述.
     *
     *
     * @returns {any}
     */
    function soundManager() {
        setAudioOn(!audioOn);
        dispatch(startSound(!_soundBool));
    }

    /**
     * Toggle Expansion.
     *
     * @returns {any}
     */
    function toggleExpansion() {
        setExpanded(!expanded);
    }

    const backHandler = BackHandler.addEventListener(
        'hardwareBackPress',
        onShowSideBar
    );


    React.useEffect(() => () => backHandler.remove(), []);


    /**
        * Redirects to another page generated by replacing the path in the original URL
        * with the given path.
        *
        * @param {(string)} pathname - The path to navigate to.
        * @returns {Void}
    */
    function onShowSideBar() {
        dispatch(updateSettings({
            accessToken: null,
            email: null,
            displayName: null
        }));
        dispatch(cancelWaitForOwner());

        return true;
    }

    React.useEffect(() => {
        if (_isPrejoin && _isLobbyScreenVisible && partCount
        ) {
            if (partCount >= 1) {
                typeof intervalRef.current.stop === 'function' && intervalRef.current.stop();
                dispatch(connectNative());
                dispatch(createDesiredLocalTracks());

            }

        }

    }, [ partCount ]);

    const renderNotificationContainer = () => {
        const notificationsStyle = {
            position: 'absolute',
            top: 20,
            width: '100%'
        };


        return (
            React.createElement(NotificationsContainer, {
                style: notificationsStyle
            })
        );
    };

    const someInterval = async () => {
        const moement = moment().seconds(0)
        .milliseconds(0)
        .format('X');

        const headers = _.isNil(accessToken) ? {
            'Content-Type': 'application/json'
        } : {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${accessToken}`
        };

        const mesh256 = sha256(`${apiKEY}:MH:${moement}`);
        const urlConfg = `${API_URL_PREFIX}${API_VERSION_PREFIX}meeting/totalparticipants`;
        const participantCountObject = await axios({
            url: urlConfg,
            data: JSON.stringify({
                client_id: MH_CLIENT_ID,
                credentials: mesh256,
                ...isDomainWeborNative,
                meeting_id: roomName
            }),
            headers,
            method: 'post'
        });
        // eslint-disable-next-line camelcase
        const participantCount = participantCountObject?.data?.data?.total_participant;


        setPartCount(participantCount);


    };


    return (
        <>
            <View style = { styles.container }>
                <ReactInterval
                    // eslint-disable-next-line react/jsx-no-bind
                    callback = { someInterval }
                    enabled = { _isPrejoin && _isLobbyScreenVisible }
                    ref = { intervalRef }
                    timeout = { 8000 } />
                <KeyboardAvoidingView
                    behavior = { Platform.OS === 'ios' ? 'position' : null }
                    keyboardVerticalOffset = { Platform.OS === 'ios' ? 50 : 70 }
                    style = {{ flex: 1 }}>
                    <View
                        style = {{
                            flexDirection: 'row',
                            justifyContent: 'space-between' }}>
                        <TouchableOpacity onPress = { onShowSideBar } >
                            <Icon
                                src = { IconArrowBack }
                                style = {{
                                    color: 'white',
                                    fontSize: 22,
                                    padding: 8,
                                    marginLeft: 5
                                }} />
                        </TouchableOpacity>
                        <TouchableOpacity
                            style = {{
                                marginLeft: 5,
                                padding: 8
                            }} >
                            <Avatar
                                displayName = { displayName }
                                dynamicColor = { true }
                                participantId = 'local'
                                size = { 30 }
                                url = { userDetails ? userDetails?.picture : null } />
                        </TouchableOpacity>
                    </View>
                    { typeof config.disableInviteFunctions !== 'undefined' && config.disableInviteFunctions === true ? <></> : <>
                        <View
                            style = { [ styles.surface, {
                                backgroundColor: '#273c61',
                                width: '75%',
                                alignSelf: 'center',
                                padding: 10,
                                borderTopLeftRadius: 15,
                                borderTopRightRadius: 15,
                                borderBottomRightRadius: 15,
                                borderBottomLeftRadius: 15
                            } ] }>
                            <TouchableOpacity
                                onPress = { toggleExpansion }>
                                <View
                                    style = {{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        alignSelf: 'center'
                                    }}>
                                    <Text
                                        style = {{
                                            alignSelf: 'center',
                                            color: 'white',
                                            fontSize: 16,
                                            fontWeight: '400'
                                        }}>
                                        {t('welcomepage.meetingDetails')}
                                    </Text>
                                    <Icon
                                        src = { expanded ? IconArrowUpWide : IconArrowDownWide }
                                        style = {{
                                            color: '#273c61',
                                            fontSize: 15,
                                            margin: 5
                                        }} />
                                </View>
                            </TouchableOpacity>
                            <Collapsible collapsed = { !expanded }>
                                <View
                                    style = {{
                                        paddingTop: 10,
                                        paddingBottom: 10,
                                        width: '100%',
                                        alignSelf: 'center',
                                        display: 'flex',
                                        flexDirection: 'row',
                                        justifyContent: 'space-between'
                                    }}>
                                    <Text
                                        style = {{
                                            color: 'white',
                                            fontSize: 17,
                                            fontWeight: '400',
                                            flex: 1,
                                            flexWrap: 'wrap'
                                        }}>{`${defaultUrl}/${roomName}`}</Text>
                                    <TouchableOpacity
                                    // eslint-disable-next-line react/jsx-no-bind
                                        onPress = { () => Clipboard.setString(`${defaultUrl}/${roomName}`) } >
                                        <Icon
                                            size = { 17 }
                                            src = { IconCopy }
                                            style = {{
                                                paddingTop: 3,
                                                alignItems: 'center',
                                                marginRight: 3
                                            }} />
                                    </TouchableOpacity>
                                </View>
                                <View>
                                    {
                                        pregData && pregData?.meeting_start_time && pregData?.timezone && (
                                            <>
                                                <View
                                                    style = {{
                                                        borderBottomColor: 'black',
                                                        borderBottomWidth: 1
                                                    }} />
                                                <Text
                                                    style = {{
                                                        marginTop: 5,
                                                        marginBottom: 5,
                                                        fontSize: 15,
                                                        color: '#fff'
                                                    }}>
                                                    {t('welcomepage.meetingDate')}:  { meetingTime }
                                                </Text>
                                                <View
                                                    style = {{
                                                        borderBottomColor: 'black',
                                                        borderBottomWidth: 1
                                                    }} />
                                                <Text
                                                    style = {{
                                                        marginTop: 5,
                                                        marginBottom: 5,
                                                        fontSize: 15,
                                                        color: '#fff'
                                                    }}>
                                                    {t('welcomepage.Timezone')}:   { pregData.timezone }
                                                </Text>
                                            </>
                                        )
                                    }
                                </View>
                            </Collapsible>
                        </View></> }
                    <View
                        style = {{
                            // position: 'relative',
                            alignSelf: 'center',
                            position: 'relative',
                            top: 50,
                            left: 0,
                            bottom: 0,
                            right: 0,
                            height: 300,
                            width: '80%',
                            justifyContent: 'center',
                            alignItems: 'center',
                            backgroundColor: '#2B3541',
                            borderRadius: 20
                        }}>
                        <View
                            style = {
                                { width: '100%',
                                    flexDirection: 'row',
                                    justifyContent: 'flex-end',
                                    marginRight: '10%' }
                            }>
                            <View>
                                <TouchableOpacity onPress = { soundManager } >
                                    <Icon
                                        size = { 30 }
                                        src = { audioOn ? AudioOn : AudioOffMute }
                                        style = {{
                                            color: '#fff',
                                            backgroundColor: '#fff',
                                            fontSize: 22,
                                            padding: 8,
                                            marginLeft: 5,
                                            borderRadius: 10
                                        }} />
                                </TouchableOpacity>
                            </View>
                        </View>
                        <View
                            style = {{ justifyContent: 'center',
                                alignItems: 'center' }}>
                            <Image
                                source = { require('../../../../../images/loading2.gif') }
                                style = {{ width: 120,
                                    height: 120 }} />
                            <Text
                                style = {{ color: '#fff',
                                    marginTop: 10,
                                    paddingLeft: 10,
                                    paddingRight: 10,
                                    fontSize: 20,
                                    fontWeight: '400',
                                    textAlign: 'center' }}>
                                {_isLobbyScreenVisible && !_isPrejoin
                                    ? t('welcomepage.waitingInLobby', { moderator: interfaceConfig.CHANGE_MODERATOR_NAME ? interfaceConfig.CHANGE_MODERATOR_NAME : 'Moderator' })
                                    : t('welcomepage.pleaseWaitForTheStartMeeting', { moderator: interfaceConfig.CHANGE_MODERATOR_NAME ? interfaceConfig.CHANGE_MODERATOR_NAME : 'moderator' })}</Text>
                        </View>
                    </View>
                </KeyboardAvoidingView>
                { renderNotificationContainer() }
            </View>
        </>
    );
}
