// @flow

import { Linking } from 'react-native';
import type { Dispatch } from 'redux';

import { appNavigate } from '../app/actions';
import logger from '../app/logger';
import {
    AlertDialog,
    openDialog
} from '../base/dialog';
import { getParticipantDisplayName } from '../base/participants';
import { parseURLParams } from '../base/util';

/**
 * Notify that we've been kicked out of the conference.
 *
 * @param {MHParticipant} participant - The {@link MHParticipant}
 * instance which initiated the kick event.
 * @param {?Function} submit - The function to execute after submiting the dialog.
 * @param {?string} reason - Reson for removing the user.
 * @returns {Function}
 */
export function notifyKickedOut(participant: Object, submit: ?Function, reason: ?string = undefined) {
    return (dispatch: Dispatch<any>, getState: Function) => {
        dispatch(openDialog(AlertDialog, {
            contentKey: {
                key: reason ? reason : 'dialog.kickTitle',
                params: {
                    participantDisplayName: getParticipantDisplayName(getState, participant.getId())
                }
            },
            onSubmit: submit,
            onCancel: submit
        }));
    };
}


/**
 * Leavee=ing conference in native.
 *
 * @returns {any}
 */
export function leaveConference() {
    return (dispatch, getState) => {
        const state = getState();
        const { locationURL } = state['features/base/connection'];

        if (locationURL) {
            const goBackUrlEncoded = parseURLParams(locationURL, true, 'search')?.goBack;

            if (goBackUrlEncoded) {
                const goBackUrl = decodeURIComponent(goBackUrlEncoded);

                dispatch(appNavigate(undefined));
                Linking.openURL(goBackUrl).catch(ers => logger.log('Error in Leave Conference', ers));

            } else {
                dispatch(appNavigate(undefined));
            }
        } else {
            dispatch(appNavigate(undefined));
        }

    };
}
