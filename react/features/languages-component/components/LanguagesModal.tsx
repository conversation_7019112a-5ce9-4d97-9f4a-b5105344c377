// @flow

import React, { PureComponent } from "react";
import type { Dispatch } from "redux";

import { DEFAULT_LANGUAGE, i18next, LANGUAGES, translate } from "../../base/i18n";
import { MHModal } from "../../base/modal";
import { connect } from "../../base/redux";

import { closeLanguagesModal, hideLanguagesListHandle, showLanguagesListHandle } from "../actions";
import { LANGUAGES_MODAL_ID } from "../actionTypes";
import { Text, TextStyle, TouchableOpacity, View, ViewStyle } from "react-native";
import { Icon, IconArrowRight } from "../../base/icons";
import styles from "./styles";
import LanguageSelectView from "./LanguageSelectView";

/**
 * The type of the React {@code Component} props of {@code ShareDocument}.
 */
type Props = {
    /**
     * True if the chat window should be rendered.
     */
    _isOpen: boolean;

    /**
     * True if the chat window should be rendered.
     */
    _showLanguagesList: boolean;

    accessToken: string;


    /**
     * The Redux dispatch function.
     */
    dispatch: Dispatch<any>;

    /**
     * Function to be used to translate i18n labels.
     */
    t: Function;
};

/**
 * Implements a React native component that renders the shared document window.
 */
class LanguageModal extends PureComponent<Props> {
    /**
     * Instantiates a new instance.
     *
     * @inheritdoc
     */
    constructor(props: Props) {
        super(props);

        this._onClose = this._onClose.bind(this);
    }

    /**
     * Implements React's {@link Component#render()}.
     *
     * @inheritdoc
     */
    render() {
        const { t, _currentLanguage } = this.props;
        return (
            <MHModal
                headerProps={{
                    headerLabelKey: "settings.languageSettings",
                }}
                modalId={LANGUAGES_MODAL_ID}
                onClose={this._onClose}
            ><View style={{padding: 20}}>
                <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center'}}>
                    <Text style = { styles.languageText as TextStyle }> {t('settings.language')}</Text>
                <View style = { styles.languageButtonContainer as ViewStyle }>
                <TouchableOpacity
                onPress = { () => {
                    this._clickedToLanguageSelect(this.props._showLanguagesList, this.props.dispatch)
                } }>
                        <View style = { styles.languageButton as ViewStyle }>
                            <Text
                                style = { styles.languageText as TextStyle }>{t(`languages:${_currentLanguage}`)}</Text>
                            <Icon
                                size = { 24 }
                                src = { IconArrowRight } />
                        </View>
                    {/* </TouchableHighlight> */}
                    </TouchableOpacity>
                </View>
                </View>

                {this.props._showLanguagesList ? <LanguageSelectView accessToken={this.props.accessToken}/> : null}
            </View>
            </MHModal>
        );
    }

    _onClose: () => boolean;

    /**
     * Closes the window.
     *
     * @returns {boolean}
     */
    _onClose() {
        const { _isOpen, dispatch } = this.props;

        if (_isOpen) {
            dispatch(closeLanguagesModal());

            return true;
        }

        return false;
    }

    _clickedToLanguageSelect: (show:boolean, dispatch: Dispatch<any>) => boolean;

    /**
     * hide the languages list.
     *
     * @returns {boolean}
     */
    _clickedToLanguageSelect(show, dispatch) {
        if (!show) {
            dispatch(showLanguagesListHandle());
        } else {
            dispatch(hideLanguagesListHandle());
        }

        return true;
    }
}

/**
 *
 * @param {Object} state - The redux store/state.
 * @private
 * @returns {Object}
 */
export function _mapStateToProps(state: Object) {
    const { isLanguagesModalOpen, showLanguagesList } = state["features/language-component"];
    const { language: currentLanguage = DEFAULT_LANGUAGE } = i18next;
    const { accessToken } = state['features/base/jwt'];

    return {
        _isOpen: isLanguagesModalOpen,
        _showLanguagesList: showLanguagesList,
        _currentLanguage: currentLanguage,
        accessToken
    };
}

export default translate(connect(_mapStateToProps)(LanguageModal));
