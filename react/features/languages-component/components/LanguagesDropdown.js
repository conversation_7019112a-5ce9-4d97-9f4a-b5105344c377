/* @flow */
/* global APP */

import DropdownMenu, {
    DropdownItem,
    DropdownItemGroup
} from '@atlaskit/dropdown-menu';
import { jitsiLocalStorage } from '@jitsi/js-utils';
import React, { Component } from 'react';

import logger from '../../analytics/logger';
import { DEFAULT_LANGUAGE, LANGUAGES, i18next } from '../../base/i18n';
import { translate } from '../../base/i18n/functions';
import { fetchAxiosApi } from '../../base/mtApi/functions';
import { connect } from '../../base/redux';

/**
 * The type of the React {@code Component} props of {@link LanguageSelector}.
 */
type Props = {

    /**
     * MediaDeviceInfos used for display in the select element.
     */
    languages: Array<Object>,

    /**
     * If true, will render the selector disabled with a default selection.
     */
    isDisabled: boolean,

    /**
     * The default language to display as selected.
     */
    selectedLanguage: string,

    /**
     * is it mobile View
     */
    isMobileView: Boolean,

    /**
     * Invoked to obtain translated strings.
     */
    t: Function
};

/**
 * React component for selecting a language from a select element. Wraps
 * AKDropdownMenu with language selection specific logic.
 *
 * @augments Component
 */
class LanguageSelector extends Component<Props> {
    /**
     * Initializes a new LanguageSelector instance.
     *
     * @param {Object} props - The read-only React Component props with which
     * the new instance is to be initialized.
     */
    constructor(props) {
        super(props);

        this._onSelect = this._onSelect.bind(this);
        this._createDropdownItem = this._createDropdownItem.bind(this);
    }

    /**
     * Implements React's {@link Component#render()}.
     *
     * @inheritdoc
     * @returns {ReactElement}
     */
    render() {
        if (!this.props.languages || !this.props.languages.length) {
            return this._renderNoLanguages();
        }

        const items = this.props.languages.map(this._createDropdownItem);
        const defaultSelected = this.props.languages.find(item =>
            item === this.props.selectedLanguage
        );

        return this._createDropdown({
            defaultSelected,
            isDisabled: this.props.isDisabled,
            items,
            placeholder: this.props.t('setting.selectLanguage')
        });
    }

    /**
     *
     * @param {string} triggerText - The text to display within the element.
     * @private
     * @returns {ReactElement}
     */
    _createDropdownTrigger(triggerText) {
        return (
            <div className = 'language-selector-trigger'>
                <span className = 'language-selector-trigger-text'>
                    { triggerText }
                </span>
            </div>
        );
    }

    _createDropdownItem: (Object) => void;

    /**
     * Creates an object in the format expected by AKDropdownMenu for an option.
     *
     * @param {MediaDeviceInfo} language - An object with a label and a deviceId.
     * @private
     * @returns {Object} The passed in media language description converted to a
     * format recognized as a valid AKDropdownMenu item.
     */
    _createDropdownItem(language) {
        return (
            <DropdownItem
                className = { language === this.props.selectedLanguage ? 'selected-option' : '' }
                isSelected = { true }
                key = { language }
                onClick = {
                    e => {
                        e.stopPropagation();
                        this._onSelect(language);
                    }
                }
                // eslint-disable-next-line react/jsx-no-bind
                shouldAllowMultiline = { true } >
                {this.props.t(`languages:${language}`)}
            </DropdownItem>
        );
    }

    /**
     * Creates a AKDropdownMenu Component using passed in props and options. If
     * the dropdown needs to be disabled, then only the AKDropdownMenu trigger
     * element is returned to simulate a disabled state.
     *
     * @param {Object} options - Additional configuration for display.
     * @param {Object} options.defaultSelected - The option that should be set
     * as currently chosen.
     * @param {boolean} options.isDisabled - If true, only the AKDropdownMenu
     * trigger component will be returned to simulate a disabled dropdown.
     * @param {Array} options.items - All the selectable options to display.
     * @param {string} options.placeholder - The translation key to display when
     * no selection has been made.
     * @private
     * @returns {ReactElement}
     */
    _createDropdown(options) {
        const triggerText
            = <div className = 'language-selector-trigger-container'>
                {this.props.isMobileView ? null : <img src = { './images/globe_white.svg' } />}
                <span >{this.props.t(`languages:${options.defaultSelected}`)}</span>
            </div>
                || options.placeholder;
        const trigger = this._createDropdownTrigger(triggerText);

        if (options.isDisabled || !options.items.length) {
            return (
                <div className = 'language-selector-trigger-disabled'>
                    { trigger }
                </div>
            );
        }

        return (
            <div
                className = { `language-dropdown-menu ${this.props.isMobileView ? 'd-flex justify-content-between align-items-center mw-100 pr-1' : ''}` }>
                <DropdownMenu
                    shouldAllowMultilineItems = { true }
                    shouldFitContainer = { false }

                    trigger = { triggerText }
                    triggerButtonProps = {{
                        shouldFitContainer: true
                    }}>
                    <DropdownItemGroup>
                        { options.items }
                    </DropdownItemGroup>
                </DropdownMenu>
                {this.props.isMobileView ? <img src = { './images/globe_black.svg' } /> : null}
            </div>
        );
    }

    _onSelect: (Object) => void;

    /**
     * Invokes the passed in callback to notify of selection changes.
     *
     * @param {Object} newLanguage - Selected language from DropdownMenu option.
     * @private
     * @returns {void}
     */
    async _onSelect(newLanguage) {
        if (this.props.selectedLanguage !== newLanguage) {
            i18next.changeLanguage(newLanguage);

            const state1 = APP.store.getState()['features/base/jwt'];
            const usersAccessToken = jitsiLocalStorage.getItem('accessToken');
            const isAccessToken = state1.accessToken ?? usersAccessToken === 'null' ? null : usersAccessToken;

            const headers = isAccessToken ? {
                'Content-Type': 'application/json',
                'Authorization': `${'Bearer '}${isAccessToken}`
            } : {
                'Content-Type': 'application/json'
            };

            const languageData = {
                'language': newLanguage
            };

            if (isAccessToken) {
                const response = await fetchAxiosApi('customer/save_language_preference', 'POST', headers, languageData);

                if (response && typeof response.success !== 'undefined' && response.success === true) {
                    // No need to show anything to customer
                } else {
                    logger.error('Error saving language preference:', response);
                }

            }

        }
    }

    /**
     * Creates a Select Component that is disabled and has a placeholder
     * indicating there are no languages to select.
     *
     * @private
     * @returns {ReactElement}
     */
    _renderNoLanguages() {
        return this._createDropdown({
            isDisabled: true,
            placeholder: this.props.t('No languages')
        });
    }
}

/**
 *
 * @param {Object} state - The redux store/state.
 * @private
 * @returns {Object}
 */
export function _mapStateToProps() {
    const { language: currentLanguage = DEFAULT_LANGUAGE } = i18next;

    return {
        isDisabled: false,
        languages: LANGUAGES,
        selectedLanguage: currentLanguage
    };
}

export default translate(connect(_mapStateToProps)(LanguageSelector));
