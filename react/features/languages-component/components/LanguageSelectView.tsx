import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView, Text, View, ViewStyle, TouchableOpacity, TextStyle, Alert } from 'react-native';

import i18next, { DEFAULT_LANGUAGE, LANGUAGES } from '../../base/i18n/i18next';

import styles from './styles';
import { useDispatch } from 'react-redux';
import { hideLanguagesListHandle } from '../actions';
import { showNotification } from '../../notifications';
import { fetchAxiosApi } from '../../base/mtApi';

const LanguageSelectView = ( props: any ) => {
    const { t } = useTranslation();
    const dispatch = useDispatch();
    const { language: currentLanguage = DEFAULT_LANGUAGE } = i18next;

    const setLanguage = useCallback((language: string) => async () => {
        i18next.changeLanguage(language);
        dispatch(hideLanguagesListHandle());
        if (props.accessToken) {
            const headers =  {
                'Content-Type': 'application/json',
                'Authorization': `${'Bearer '}${props.accessToken}`
            };
            const languageData = {
                'language': language
            };
            const response = await fetchAxiosApi('customer/save_language_preference', 'POST', headers, languageData)

            if (response && typeof response.success !== 'undefined' && response.success === true) {
               // No need to show anything to customer
            } else {
                Alert.alert(t('dialog.error'), t('info.genericError'))
            }
        }
    }, [ i18next ]);

    return (
       
            <ScrollView
                contentContainerStyle = { styles.profileView as ViewStyle }>
                {
                    LANGUAGES.map(language => (
                        <TouchableOpacity
                            disabled = { currentLanguage === language }
                            key = { language }
                            onPress = { setLanguage(language) }
                            >
                            <View
                                style = { styles.languageOption as ViewStyle }>
                                <Text
                                    style = { [
                                        styles.text,
                                        styles.fieldLabelText,
                                        currentLanguage === language && styles.selectedLanguage ] as TextStyle }>
                                    {t(`languages:${language}`)}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    ))
                }
            </ScrollView>
    );
};

export default LanguageSelectView;
