// @flow

import { jitsiLocalStorage } from '@jitsi/js-utils';
import Logger from 'meet-hour-logger';

import { setFollowMe, setStartMutedPolicy } from '../base/conference';
import { openDialog } from '../base/dialog';
import { i18next } from '../base/i18n';
import { fetchAxiosApi } from '../base/mtApi';
import { updateSettings } from '../base/settings';
import { NOTIFICATION_TIMEOUT_TYPE, showNotification } from '../notifications';
import { setPrejoinPageVisibility } from '../prejoin/actions';
import MEETHOUR_TO_BCP47_MAP from '../transcribing/meethour-bcp47-map.json';

import {
    DELETE_CACHE,
    DISABLE_REACTION_SOUND,
    SET_AUDIO_SETTINGS_VISIBILITY,
    SET_HANGUP_SETTINGS_VISIBILITY,
    SET_VIDEO_SETTINGS_VISIBILITY
} from './actionTypes';
import { SettingsDialog } from './components';
import { getMoreTabProps, getProfileTabProps } from './functions';

declare var APP: Object;

const logger = Logger.getLogger(__filename);

/**
 * Saves language preference to the server.
 *
 * @param {string} language - The language code to save.
 * @param {string} accessToken - The user's access token.
 * @returns {Promise<boolean>} - Returns true if successful, false otherwise.
 */
export async function saveLanguagePreference(language: string, accessToken: string): Promise<boolean> {
    if (!accessToken) {
        return false;
    }

    try {
        const headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${accessToken}`
        };

        const languageData = {
            'language': language
        };

        const response = await fetchAxiosApi('customer/save_language_preference', 'POST', headers, languageData);

        return response && typeof response.success !== 'undefined' && response.success === true;
    } catch (error) {
        logger.error('Error saving language preference:', error);

        return false;
    }
}

/**
 * Opens {@code SettingsDialog}.
 *
 * @param {string} defaultTab - The tab in {@code SettingsDialog} that should be
 * displayed initially.
 * @returns {Function}
 */
export function openSettingsDialog(defaultTab: string) {
    return openDialog(SettingsDialog, { defaultTab });
}

/**
 * Sets the visiblity of the audio settings.
 *
 * @param {boolean} value - The new value.
 * @returns {Function}
 */
function setAudioSettingsVisibility(value: boolean) {
    return {
        type: SET_AUDIO_SETTINGS_VISIBILITY,
        value
    };
}

/**
 * Sets the visiblity of the video settings.
 *
 * @param {boolean} value - The new value.
 * @returns {Function}
 */
function setVideoSettingsVisibility(value: boolean) {
    return {
        type: SET_VIDEO_SETTINGS_VISIBILITY,
        value
    };
}

/**
 * Sets the visiblity of the video settings.
 *
 * @param {boolean} value - The new value.
 * @returns {Function}
 */
function setHangupSettingsVisibility(value: boolean) {
    return {
        type: SET_HANGUP_SETTINGS_VISIBILITY,
        value
    };
}

/**
 * Submits the settings from the "More" tab of the settings dialog.
 *
 * @param {Object} newState - The new settings.
 * @returns {Function}
 */
export function submitMoreTab(newState: Object): Function {
    return (dispatch, getState) => {
        const currentState = getMoreTabProps(getState());

        if (newState.followMeEnabled !== currentState.followMeEnabled) {
            dispatch(setFollowMe(newState.followMeEnabled));
        }

        const showPrejoinPage = newState.showPrejoinPage;

        if (showPrejoinPage !== currentState.showPrejoinPage) {
            // The 'showPrejoin' flag starts as 'true' on every new session.
            // This prevents displaying the prejoin page when the user re-enables it.
            if (showPrejoinPage && getState()['features/prejoin']?.showPrejoin) {
                dispatch(setPrejoinPageVisibility(false));
            }
            dispatch(updateSettings({
                userSelectedSkipPrejoin: !showPrejoinPage
            }));
        }

        if (newState.startAudioMuted !== currentState.startAudioMuted
            || newState.startVideoMuted !== currentState.startVideoMuted) {
            dispatch(setStartMutedPolicy(
                newState.startAudioMuted, newState.startVideoMuted));
        }

        const { _language } = getState()['features/subtitles'];
        const OngoingTranscriptionLanguage = getState()['features/base/conference']?.conference?.options?.config?.transcriptionLanguage;
        const transGModelType = getState()['features/base/conference']?.conference?.options?.config?.transcription.transGModel;


        if (newState.currentLanguage !== currentState.currentLanguage) {
            i18next.changeLanguage(newState.currentLanguage);

            // Save language preference to server
            const { accessToken } = getState()['features/base/jwt'] || null;

            if (accessToken) {
                saveLanguagePreference(newState.currentLanguage, accessToken)
                    .then(success => {
                        if (!success) {
                            dispatch(showNotification({
                                titleKey: 'dialog.error',
                                descriptionKey: 'info.genericError'
                            }, NOTIFICATION_TIMEOUT_TYPE.MEDIUM));
                        }
                    })
                    .catch(error => {
                        logger.error('Failed to save language preference:', error);
                        dispatch(showNotification({
                            titleKey: 'dialog.error',
                            descriptionKey: 'info.genericError'
                        }, NOTIFICATION_TIMEOUT_TYPE.MEDIUM));
                    });
            }

            if (_language !== null && OngoingTranscriptionLanguage !== MEETHOUR_TO_BCP47_MAP[transGModelType][i18next.language]) {
                dispatch(showNotification({
                    titleKey: 'transcribing.TranscriptionLanguageCantChange',
                    descriptionKey: 'transcribing.TranscriptionLanguageCannotBeChangedOngoingCall',
                    concatText: true,
                    maxLines: 2
                }, NOTIFICATION_TIMEOUT_TYPE.MEDIUM));
            }
        }

        if (newState.deleteCache !== currentState.deleteCache) {
            dispatch(deleteCache(newState.deleteCache));
            jitsiLocalStorage.clear();
        }
    };
}

/**
 * Submits the settings from the "Profile" tab of the settings dialog.
 *
 * @param {Object} newState - The new settings.
 * @returns {Function}
 */
export function submitProfileTab(newState: Object): Function {
    return (dispatch, getState) => {
        const currentState = getProfileTabProps(getState());

        if (newState.displayName !== currentState.displayName) {
            APP.conference.changeLocalDisplayName(newState.displayName);
        }

        if (newState.email !== currentState.email) {
            APP.conference.changeLocalEmail(newState.email);
        }
    };
}

/**
 * Toggles the visiblity of the audio settings.
 *
 * @returns {void}
 */
export function toggleAudioSettings() {
    return (dispatch: Function, getState: Function) => {
        const value = getState()['features/settings'].audioSettingsVisible;

        dispatch(setAudioSettingsVisibility(!value));
    };
}

/**
 * Toggles the visiblity of the video settings.
 *
 * @returns {void}
 */
export function toggleVideoSettings() {
    return (dispatch: Function, getState: Function) => {
        const value = getState()['features/settings'].videoSettingsVisible;

        dispatch(setVideoSettingsVisibility(!value));
    };
}

/**
 * Toggles the visiblity of the video settings.
 *
 * @returns {void}
 */
export function toggleHangUpSettings() {
    return (dispatch: Function, getState: Function) => {
        const value = getState()['features/settings'].hangupSettingsVisible;

        dispatch(setHangupSettingsVisibility(!value));
    };
}


/**
 * Delete Cache for the desktop.
 *
 * @param {string} cache - Parameter to delete the cache.
 * @returns {Function}
 */
export function deleteCache(cache: boolean) {
    return {
        type: DELETE_CACHE,
        cache
    };
}

/**
 * Disable Sounds .
 *
 * @param {boolean} soundsReactions - Parameter to delete the cache.
 * @returns {Function}
 */
export function disableReactionSounds(soundsReactions: boolean) {
    return {
        type: DISABLE_REACTION_SOUND,
        soundsReactions
    };
}


