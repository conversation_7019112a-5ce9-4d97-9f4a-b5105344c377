/**
 * The type of (redux) action which sets the visibility of the audio settings popup.
 */
export const SET_AUDIO_SETTINGS_VISIBILITY = 'SET_AUDIO_SETTINGS_VISIBILITY';

/**
 * The type of (redux) action which sets the visibility of the video settings popup.
 */
export const SET_VIDEO_SETTINGS_VISIBILITY = 'SET_VIDEO_SETTINGS_VISIBILITY';

/**
 * The type of (redux) action which sets the visibility of the hangup button settings popup.
 */
export const SET_HANGUP_SETTINGS_VISIBILITY = 'SET_HANGUP_SETTINGS_VISIBILITY';

/**
 * Delete cache .
 */
export const DELETE_CACHE = 'DELETE_CACHE';

/**
 * Disable Sound when user clicks on reactions disable sound.
 */
export const DISABLE_REACTION_SOUND = 'DISABLE_REACTION_SOUND';

