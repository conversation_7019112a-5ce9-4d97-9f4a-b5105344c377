// @flow

import { ReducerRegistry } from '../base/redux';

import {
    DELETE_CACHE,
    DISABLE_REACTION_SOUND,
    SET_AUDIO_SETTINGS_VISIBILITY,
    SET_HANGUP_SETTINGS_VISIBILITY,
    SET_VIDEO_SETTINGS_VISIBILITY
} from './actionTypes';


export const DEFAULT_STATE = {
    deleteCache: false,
    soundsReactions: true
};

ReducerRegistry.register('features/settings', (state = DEFAULT_STATE, action) => {
    switch (action.type) {
    case SET_AUDIO_SETTINGS_VISIBILITY:
        return {
            ...state,
            audioSettingsVisible: action.value
        };
    case SET_VIDEO_SETTINGS_VISIBILITY:
        return {
            ...state,
            videoSettingsVisible: action.value
        };
    case SET_HANGUP_SETTINGS_VISIBILITY:
        return {
            ...state,
            hangupSettingsVisible: action.value
        };
    case DELETE_CACHE:
        return {
            ...state,
            deleteCache: action.cache
        };

    case DISABLE_REACTION_SOUND:
        return {
            ...state,
            soundsReactions: action.soundsReactions
        };
    }


    return state;
});
