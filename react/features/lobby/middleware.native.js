// @flow

import axios from 'axios';
import { sha256 } from 'js-sha256';
import moment from 'moment';

import { appNavigate } from '../app/actions';
import { getDefaultURL } from '../app/functions';
import logger from '../app/logger';
import { APP_WILL_MOUNT, APP_WILL_UNMOUNT } from '../base/app';
import { CONFERENCE_FAILED, CONFERENCE_JOINED } from '../base/conference';
import { apiKEY } from '../base/config/constants';
import { hideDialog } from '../base/dialog';
import { MHConferenceErrors, MHConferenceEvents } from '../base/lib-meet-hour';
import { fetchApi } from '../base/mtApi';
import { getFirstLoadableAvatarUrl, getParticipantDisplayName } from '../base/participants';
import { USER_WAITING_REGISTER } from '../base/premeeting/components/web/constants';
import { MiddlewareRegistry, StateListenerRegistry } from '../base/redux';
import { playSound,
    registerSound,
    stopSound,
    unregisterSound
} from '../base/sounds';
import { getDisabledSounds } from '../base/sounds/functions.any';
import { isTestModeEnabled } from '../base/testing';
import { checkIfReactNative, isDomainWeborNative } from '../base/util/checkOS';
import { NOTIFICATION_TYPE, joinLeaveNotificationsDisabled, showNotification } from '../notifications';
import { PrejoinWaitng } from '../prejoin-mobile/components';
import { shouldAutoKnock } from '../prejoin/functions';
import { LIVE_STREAMING_OFF_SOUND_ID, LIVE_STREAMING_ON_SOUND_ID, RECORDING_OFF_SOUND_ID, RECORDING_ON_SOUND_ID } from '../recording/constants';
import { LIVE_STREAMING_OFF_SOUND_FILE, LIVE_STREAMING_ON_SOUND_FILE, RECORDING_OFF_SOUND_FILE, RECORDING_ON_SOUND_FILE } from '../recording/sounds';

import { HANDLE_SOUND, KNOCKING_PARTICIPANT_ARRIVED_OR_UPDATED, OPEN_LOBBY } from './actionTypes';
import {
    StartSound,
    hideLobbyScreen,
    knockingParticipantLeft,
    openDialogs,
    participantIsKnockingOrUpdated,
    setLobbyModeEnabled,
    setPasswordJoinFailed, startKnocking } from './actions';
import { INCOMING_USER_REQ_SOUND_ID } from './components/constants';
import { INCOMING_USER_KNOCKING_SOUND_FILE } from './components/sounds';

/**
 * This is a work around as the component is in a
 * loop state which resets the sound functionality and hits the api constantly.
 */
export const confg = {
    runFetch: false,
    nativeAudio: false
};

MiddlewareRegistry.register(store => next => action => {
    switch (action.type) {

    case APP_WILL_MOUNT: {
        const dispatch = store.dispatch;

        store.dispatch(registerSound(INCOMING_USER_REQ_SOUND_ID, INCOMING_USER_KNOCKING_SOUND_FILE));

        // store.dispatch(registerSound(USER_WAITING_REGISTER, USER_WAITING_SOUND, { loop: true }));
        store.dispatch(registerSound(
            RECORDING_OFF_SOUND_ID,
            RECORDING_OFF_SOUND_FILE));
        dispatch(registerSound(
                LIVE_STREAMING_OFF_SOUND_ID,
                LIVE_STREAMING_OFF_SOUND_FILE));

        dispatch(registerSound(
                LIVE_STREAMING_ON_SOUND_ID,
                LIVE_STREAMING_ON_SOUND_FILE));

        dispatch(registerSound(
                    RECORDING_ON_SOUND_ID,
                    RECORDING_ON_SOUND_FILE));
        break;
    }
    case APP_WILL_UNMOUNT: {
        store.dispatch(unregisterSound(INCOMING_USER_REQ_SOUND_ID));
        store.dispatch(unregisterSound(USER_WAITING_REGISTER));
        break;
    }
    case CONFERENCE_FAILED:
        return _conferenceFailed(store, next, action);
    case CONFERENCE_JOINED:
        return _conferenceJoined(store, next, action);

    case OPEN_LOBBY:
    {
        const { dispatch, getState } = store;

        if (action.lobbyscreen._isLobbyScreenVisible) {
            if (!confg.isSound && checkIfReactNative()) {
                confg.isSound = true;
                // eslint-disable-next-line new-cap
                dispatch(StartSound(true));
            }
            const { API_URL_PREFIX, API_VERSION_PREFIX, MH_CLIENT_ID } = getState()['features/base/config'];

            if (action.lobbyscreen?._isPrejoin && !confg.runFetch) {
                confg.runFetch = true;

                const moement = moment().seconds(0)
                .milliseconds(0)
                .format('X');

                const mesh256 = sha256(`${apiKEY}:MH:${moement}`);

                const fett = JSON.stringify({
                    client_id: MH_CLIENT_ID,
                    credentials: mesh256,
                    ...isDomainWeborNative,
                    'meeting_id': getState()['features/base/conference']?.room,
                    'is_owner': 1,
                    'participant_name': getState()['features/base/settings']?.displayName,
                    'participant_email': getState()['features/base/settings']?.email

                });


                // eslint-disable-next-line no-negated-condition
                if (navigator.product !== 'ReactNative') {
                    fetchApi('notify_waiting', 'post', {
                        'Content-Type': 'application/json'
                    }, fett).then(() => {
                        confg.runFetch = true;
                    })
                        .catch(errs => logger.error('Lobby API Error', errs));
                } else {

                    const urlConfg = `${API_URL_PREFIX}${API_VERSION_PREFIX}notify_waiting`;

                    axios({
                        method: 'post',
                        url: urlConfg,
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        data: fett
                    })
                    .then(res => logger.log('CONFERT', res))
                    .catch(errs => lgoger.error('errs user APi', errs));
                }


            }

        }


        return next(action);
    }

    case HANDLE_SOUND: {
        const { dispatch } = store;
        const state = store.getState();
        let disabledSounds = [];

        getDisabledSounds(state)
        .then(options => {
            disabledSounds = options;

            if (action.soundBool) {
                if (!disabledSounds.includes('USER_WAITING_REGISTER')) {
                    dispatch(playSound(USER_WAITING_REGISTER));
                }
            } else if (!disabledSounds.includes('USER_WAITING_REGISTER')) {
                dispatch(stopSound(USER_WAITING_REGISTER));
            }
        });

        return next(action);
    }

    case KNOCKING_PARTICIPANT_ARRIVED_OR_UPDATED: {
        // We need the full update result to be in the store already
        const result = next(action);

        _findLoadableAvatarForKnockingParticipant(store, action.participant);

        return result;
    }
    }

    return next(action);
});

/**
 * Registers a change handler for state['features/base/conference'].conference to
 * set the event listeners needed for the lobby feature to operate.
 */
StateListenerRegistry.register(
    state => state['features/base/conference'].conference,
    (conference, { dispatch, getState }, previousConference) => {
        if (conference && !previousConference) {
            conference.on(MHConferenceEvents.MEMBERS_ONLY_CHANGED, enabled => {
                dispatch(setLobbyModeEnabled(enabled));
            });

            conference.on(MHConferenceEvents.LOBBY_USER_JOINED, (id, name) => {
                dispatch(participantIsKnockingOrUpdated({
                    id,
                    name
                }));
                const state = getState();

                let disabledSounds = [];

                getDisabledSounds(state)
                .then(options => {
                    disabledSounds = options;

                    if (!disabledSounds.includes('INCOMING_USER_REQ_SOUND_ID')) {
                        dispatch(playSound(INCOMING_USER_REQ_SOUND_ID));
                    }
                });
            });

            conference.on(MHConferenceEvents.LOBBY_USER_UPDATED, (id, participant) => {
                dispatch(participantIsKnockingOrUpdated({
                    ...participant,
                    id
                }));
            });

            conference.on(MHConferenceEvents.LOBBY_USER_LEFT, id => {
                dispatch(knockingParticipantLeft(id));
            });

            conference.on(MHConferenceEvents.ENDPOINT_MESSAGE_RECEIVED, (origin, sender) =>
                _maybeSendLobbyNotification(origin, sender, {
                    dispatch,
                    getState
                })
            );
        }
    });

/**
 * Function to handle the conference failed event and navigate the user to the lobby screen
 * based on the failure reason.
 *
 * @param {Object} store - The Redux store.
 * @param {Function} next - The Redux next function.
 * @param {Object} action - The Redux action.
 * @returns {Object}
 */
function _conferenceFailed({ dispatch, getState }, next, action) {
    const { error } = action;
    const state = getState();
    const nonFirstFailure = Boolean(state['features/base/conference'].membersOnly);
    const { _lobbyScreen } = state['features/lobby'];

    if (error.name === MHConferenceErrors.AUTHENTICATION_REQUIRED) {
        if (typeof error.recoverable === 'undefined') {
            error.recoverable = true;
        }

        const result = next(action);

        dispatch(openDialogs(PrejoinWaitng, {
            _isLobbyScreenVisible: _lobbyScreen?._isLobbyScreenVisible,
            _isPrejoin: _lobbyScreen?._isPrejoin
        }));
        // eslint-disable-next-line new-cap
        dispatch(StartSound(true));


        // if (shouldAutoKnock(state)) {
        //     dispatch(startKnocking());
        // }
        // dispatch(setPasswordJoinFailed(nonFirstFailure));

        return result;

    }

    if (error.name === MHConferenceErrors.MEMBERS_ONLY_ERROR) {
        if (typeof error.recoverable === 'undefined') {
            error.recoverable = true;
        }

        const result = next(action);

        dispatch(openDialogs(PrejoinWaitng, {
            _isLobbyScreenVisible: true,
            _isPrejoin: false
        }));

        // eslint-disable-next-line new-cap
        dispatch(StartSound(true));


        if (shouldAutoKnock(state)) {
            dispatch(startKnocking());
        }

        dispatch(setPasswordJoinFailed(nonFirstFailure));

        return result;
    }


    if (error.name === MHConferenceErrors.CONFERENCE_ACCESS_DENIED) {
        dispatch(showNotification({
            appearance: NOTIFICATION_TYPE.ERROR,
            hideErrorSupportLink: true,
            titleKey: 'lobby.joinRejectedMessage'
        }));
        setTimeout(() => {
            dispatch(hideDialog(PrejoinWaitng));
            dispatch(appNavigate(getDefaultURL(state)));
            if (state['features/lobby']?.soundBool) {
                // eslint-disable-next-line new-cap
                dispatch(StartSound(false));
            }
        }, 5000);


    }

    return next(action);
}

/**
 * Handles cleanup of lobby state when a conference is joined.
 *
 * @param {Object} store - The Redux store.
 * @param {Function} next - The Redux next function.
 * @param {Object} action - The Redux action.
 * @returns {Object}
 */
function _conferenceJoined({ dispatch }, next, action) {
    confg.runFetch = false;
    dispatch(hideLobbyScreen());
    dispatch(stopSound(USER_WAITING_REGISTER));
    dispatch(unregisterSound(USER_WAITING_REGISTER));

    return next(action);
}

/**
 * Finds the loadable avatar URL and updates the participant accordingly.
 *
 * @param {Object} store - The Redux store.
 * @param {Object} participant - The knocking participant.
 * @returns {void}
 */
function _findLoadableAvatarForKnockingParticipant(store, { id }) {
    const { dispatch, getState } = store;
    const updatedParticipant = getState()['features/lobby'].knockingParticipants.find(p => p.id === id);
    const { disableThirdPartyRequests } = getState()['features/base/config'];

    if (!disableThirdPartyRequests && updatedParticipant && !updatedParticipant.loadableAvatarUrl) {
        getFirstLoadableAvatarUrl(updatedParticipant, store).then(loadableAvatarUrl => {
            if (loadableAvatarUrl) {
                dispatch(participantIsKnockingOrUpdated({
                    loadableAvatarUrl,
                    id
                }));
            }
        });
    }
}

/**
 * Check the endpoint message that arrived through the conference and
 * sends a lobby notification, if the message belongs to the feature.
 *
 * @param {Object} origin - The origin (initiator) of the message.
 * @param {Object} message - The actual message.
 * @param {Object} store - The Redux store.
 * @returns {void}
 */
function _maybeSendLobbyNotification(origin, message, { dispatch, getState }) {
    if (!origin?._id || message?.type !== 'lobby-notify') {
        return;
    }

    const notificationProps: any = {
        descriptionArguments: {
            originParticipantName: getParticipantDisplayName(getState, origin._id),
            targetParticipantName: message.name
        },
        titleKey: 'lobby.notificationTitle'
    };

    switch (message.event) {
    case 'LOBBY-ENABLED':
        notificationProps.descriptionKey = `lobby.notificationLobby${message.value ? 'En' : 'Dis'}abled`;
        break;
    case 'LOBBY-ACCESS-GRANTED':
        notificationProps.descriptionKey = 'lobby.notificationLobbyAccessGranted';
        break;
    case 'LOBBY-ACCESS-DENIED':
        notificationProps.descriptionKey = 'lobby.notificationLobbyAccessDenied';
        break;
    }

    if (joinLeaveNotificationsDisabled(getState()) === false) {
        dispatch(showNotification(notificationProps, isTestModeEnabled(getState()) ? undefined : 5000));
    }
}
