// @flow

import React, { Component } from 'react';
import { BackHandler, Linking, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import type { Dispatch } from 'redux';

import { appNavigate } from '../../app/actions';
import { getDefaultURL } from '../../app/functions';
import logger from '../../app/logger';
import { ColorSchemeRegistry } from '../../base/color-scheme';
import { hideDialog } from '../../base/dialog';
import { translate } from '../../base/i18n';
import { LoadingIndicator } from '../../base/react';
import { connect } from '../../base/redux';
import { StyleType } from '../../base/styles';
import { destroyLocalTracks } from '../../base/tracks';
import { parseURLParams } from '../../base/util';
import { PrejoinWaitng } from '../../prejoin-mobile/components';

import styles from './styles';

/**
 * The type of React {@code Component} props of {@link BlankPage}.
 */
type Props = {

    /**
     * The color schemed style of the component.
     */
    _styles: StyleType,

    dispatch: Dispatch<any>,

    locationURL: ?string,
};

const $coolGreen = '#5bcc8c';
const $coolWhite = '#ffff';


const stylesOverlay = StyleSheet.create({
    ViewForBack: {
        marginTop: 20,
        borderRadius: 0,
        height: 50,
        width: '50%',
        alignSelf: 'center',
        marginBottom: 20,
        justifyContent: 'center'
    },
    stylesForBack: {
        alignItems: 'center',
        backgroundColor: $coolGreen,
        height: 50,
        borderRadius: 10,
        justifyContent: 'center'
    },
    colorPallete: {
        color: $coolWhite
    }
});

/**
 * The React {@code Component} displayed by {@code AbstractApp} when it has no
 * {@code Route} to render. Renders a progress indicator when there are ongoing
 * network requests.
 */
class BlankPage extends Component<Props> {
    /**
     * Destroys the local tracks (if any) since no media is desired when this
     * component is rendered.
     *
     * @inheritdoc
     * @returns {void}
     */
    constructor() {
        super();
        this.state = {
            backArrowUrl: null,
            showButton: false
        };
        this._onOpenUrl = this._onOpenUrl.bind(this);
        this.backHandler = null;
    }


    /**
     * Destroys the local tracks (if any) since no media is desired when this
     * component is rendered.
     *
     * @inheritdoc
     * @returns {void}
     */
    componentDidMount() {
        this.props.dispatch(destroyLocalTracks());
        const { locationURL } = this.props;

        if (locationURL) {
            const goBackUrlEncoded = parseURLParams(locationURL, true, 'search')?.goBack;

            if (goBackUrlEncoded) {
                const goBackUrl = decodeURIComponent(goBackUrlEncoded);

                // eslint-disable-next-line react/no-did-mount-set-state
                this.setState({ backArrowUrl: goBackUrl });
            }

        }

        setTimeout(() => this.setState({
            showButton: true
        }), 10000);

        this.backHandler = BackHandler.addEventListener(
            'hardwareBackPress',
            this._onOpenUrl
        );
    }

    // eslint-disable-next-line require-jsdoc
    componentWillUnmount() {
        this.backHandler.remove();
    }

    /**
     * Open Url.
     *
     * @returns {any}
     */
    _onOpenUrl() {

        if (this.state.backArrowUrl) {
            Linking.openURL(this.state.backArrowUrl).catch(e => logger.error('Error opening', e));

            return false;
        }
        BackHandler.exitApp();

        this.props.dispatch(hideDialog(PrejoinWaitng));
        this.props.dispatch(appNavigate(getDefaultURL(this.state)));

        return true;

        //
    }

    /**
     * Implements React's {@link Component#render()}.
     *
     * @inheritdoc
     * @returns {ReactElement}
     */
    render() {
        const { _styles, t } = this.props;


        return (
            <View
                style = { [
                    styles.blankPageWrapper,
                    _styles.loadingOverlayWrapper
                ] }>
                <LoadingIndicator
                    color = { _styles.indicatorColor }
                    size = 'large' />
                {
                    this.state.showButton
                && <View style = { stylesOverlay.ViewForBack }>
                    <TouchableOpacity
                        onPress = { this._onOpenUrl }
                        style = { stylesOverlay.stylesForBack }>
                        <Text
                            style = {
                                stylesOverlay.colorPallete
                            }>
                            {t('dialog.WaitforModeratorOk')}
                        </Text>
                    </TouchableOpacity>
                </View>
                }

            </View>
        );
    }
}

/**
 * Maps part of the Redux state to the props of this component.
 *
 * @param {Object} state - The Redux state.
 * @returns {Props}
 */
function _mapStateToProps(state) {
    const { interfaceConfig } = state['features/base/config'];
    const LoaderColorPallete = ColorSchemeRegistry.get(state, 'LoadConfigOverlay');

    let newLoaderColorPallete;

    if (interfaceConfig !== 'undefined' && interfaceConfig?.applyMeetingSettings === true) {

        newLoaderColorPallete = {
            ...LoaderColorPallete, // Include any other styles you want to keep
            loadingOverlayWrapper: {
                backgroundColor: interfaceConfig?.DEFAULT_BACKGROUND
            }
        };
    } else {
        newLoaderColorPallete = LoaderColorPallete;
    }

    return {
        locationURL: state['features/base/connection'].locationURL,
        _styles: newLoaderColorPallete
    };
}

export default translate(connect(_mapStateToProps)(BlankPage));
