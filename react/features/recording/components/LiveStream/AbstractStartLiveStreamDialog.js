// @flow

import { jitsiLocalStorage } from '@jitsi/js-utils';
import axios from 'axios';
import { sha256 } from 'js-sha256';
import _ from 'lodash';
import moment from 'moment';
import { Component } from 'react';

import {
    createLiveStreamingDialogEvent,
    sendAnalytics
} from '../../../analytics';
import logger from '../../../base/app/logger';
import { apiKEY } from '../../../base/config/constants';
import { MHRecordingConstants } from '../../../base/lib-meet-hour';
import { IS_RECORDING_AND_LIVESTREAMING_ON, recordingandLivestreamingOn } from '../../../base/mtApi';
import { ULR_PATHS } from '../../../base/mtApi/constants';
import { checkIfNotReactNative, isDomainWeborNative } from '../../../base/util/checkOS';
import {
    getDropboxData,
    getNewAccessToken,
    isEnabled as isDropboxEnabled,
    updateDropboxToken
} from '../../../dropbox';
import { showErrorNotification } from '../../../notifications';
import { toggleRequestingSubtitles } from '../../../subtitles/actions';
import { RECORDING_TYPES } from '../../constants';


/**
 * The type of the React {@code Component} props of
 * {@link AbstractStartLiveStreamDialog}.
 */
export type Props = {

    /**
     * The {@code MHConference} for the current conference.
     */
    _conference: Object,

    /**
     * The current state of interactions with the Google API. Determines what
     * Google related UI should display.
     */
    _googleAPIState: number,

    /**
     * The email of the user currently logged in to the Google web client
     * application.
     */
    _googleProfileEmail: string,

    /**
     * The live stream key that was used before.
     */
    _streamKey: string,

    /**
     * The app key for the dropbox authentication.
     */
    _appKey: string,

    /**
    * Whether to show file recordings service, even if integrations
    * are enabled.
    */
    _fileRecordingsServiceEnabled: boolean,

    /**
    * Whether to show the possibility to share file recording with other people (e.g. Meeting participants),
    *  based on the actual implementation on the backend.
    */
    _fileRecordingsServiceSharingEnabled: boolean,

    /**
    * If true the dropbox integration is enabled, otherwise - disabled.
    */
    _isDropboxEnabled: boolean,

    /**
    * The dropbox access token.
    */
     token: ?string,

     /**
      * Toekn.
      */
    _token: ?string,
    _userId: ?Object,


    /**
     * The Redux dispatch function.
     */
    dispatch: Function,

    /**
     * The dropbox refresh token.
     */
    _rToken: string,

    /**
     * Access token's expiration date as UNIX timestamp.
     */
    _tokenExpireDate?: number,

    /**
     * Recording & Live Streaming ON & OFF State.
     */
    _isRecordingandLivestreamingOn?: Boolean,

    /**
     * Invoked to obtain translated strings.
     */
    t: Function,

    _liveStream: ?Array<Object>,

    _recording: ?Array<Object>,
    _roomName: ?string
}

/**
 * The type of the React {@code Component} state of
 * {@link AbstractStartLiveStreamDialog}.
 */
export type State = {

    /**
     * Details about the broadcasts available for use for the logged in Google
     * user's YouTube account.
     */
    broadcasts: ?Array<Object>,

    /**
     * The error type, as provided by Google, for the most recent error
     * encountered by the Google API.
     */
    errorType: ?string,

    /**
     * The boundStreamID of the broadcast currently selected in the broadcast
     * dropdown.
     */
    selectedBoundStreamID: ?string,

    /**
     * <tt>true</tt> if we have valid oauth token.
     */
    isTokenValid: boolean,

     /**
      * <tt>true</tt> if we are in process of validating the oauth token.
      */
    isValidating: boolean,

     /**
      * The currently selected recording service of type: RECORDING_TYPES.
      */
    selectedRecordingService: ?string,

     /**
      * True if the user requested the service to share the recording with others.
      */
    sharingEnabled: boolean,

     /**
      * Number of MiB of available space in user's Dropbox account.
      */
    spaceLeft: ?number,

    /**
      * The display name of the user's Dropbox account.
    */
    userName: ?string,

    /**
     * If recording is also enabled or not.
     */
    recordingEnabled: boolean,

    /**
     * Record with DropBox.
     */
    recStreamEnabled: boolean,

    /**
      * Display the Dialog Box.
    */
    dialogOpen: boolean,

    /**
     * LiveStream data from the interface_config.
     */
    liveStreamTools: ?Array<Object>,

    /**
     * Recording Data from the interface_config.
     */
    recTools: ?Array<Object>,

    /**
     * StreamKeys could be anything from facebook to youtube.
     */
    streamKeys: Object,

    /**
     * Recording is enabled or not.
     */
    recordingO: ?boolean,

    /**
     * Streaming keys that are enabled or not.
     */
    streamEnabledMobile: Object,

    /**
     * Steaming enalbe in the browser environment.
    */
    streamsEnabled: Object,

    /**
     * APi url version.
     */
    apiN: Boolean,

    /**
     * Acces Token.
     */
     _accessToken: ?string
};

const DEFAULT_RTMPS_STATE = {
    'Vimeo': 'rtmps://rtmp-global.cloud.vimeo.com:443/live',
    'Custom RTMPS': '', // Custom RTMPS
    'Custom RTMP': '', // Custom RTMPS
    'Instagram': 'rtmps://live-upload.instagram.com:443/rtmp/', // Instagram RTMPS
    'LinkedIn': 'rtmps://12c7101ae60e40ca9597078681ee5fc3.channel.media.azure.net:2935/live/', // LinkedIn RTMP
    'Twitch': 'rtmp://jfk.contribute.live-video.net/app/', // Twitch RTMP
    'Youtube': 'rtmp://a.rtmp.youtube.com/live2/', // Youtube RTMP
    'Facebook': 'rtmps://live-api-s.facebook.com:443/rtmp/' // Facebook RTMPS
};

/**
 * Implements an abstract class for the StartLiveStreamDialog on both platforms.
 *
 * NOTE: Google log-in is not supported for mobile yet for later implementation
 * but the abstraction of its properties are already present in this abstract
 * class.
 */
export default class AbstractStartLiveStreamDialog<P: Props>
    extends Component<P, State> {
    _isMounted: boolean;

    /**
     * Constructor of the component.
     *
     * @inheritdoc
     */
    constructor(props: P) {
        super(props);

        let selectedRecordingService;

        if (this.props._fileRecordingsServiceEnabled
            || !this._areIntegrationsEnabled()) {
            selectedRecordingService = RECORDING_TYPES.JITSI_REC_SERVICE;
        } else if (this._areIntegrationsEnabled()) {
            selectedRecordingService = RECORDING_TYPES.DROPBOX;
        }

        this.state = {
            broadcasts: undefined,
            errorType: undefined,
            selectedBoundStreamID: undefined,
            streamEnabledMobile: typeof jitsiLocalStorage.getItem('streamEnabledMobile') === 'string' ? JSON.parse(jitsiLocalStorage.getItem('streamEnabledMobile')) : [],
            streamEnabledWeb: typeof jitsiLocalStorage.getItem('streamEnabledWeb') === 'string' ? JSON.parse(jitsiLocalStorage.getItem('streamEnabledWeb')) : [],
            streamsEnabled: JSON.parse(jitsiLocalStorage.getItem('liveStreamDetails'))?.streamsEnable ?? {},
            isTokenValid: false,
            isValidating: false,
            userName: undefined,
            openDialog: [],
            openDialog0: false,
            openDialog1: false,
            openDialog2: false,
            streamKeys: JSON.parse(jitsiLocalStorage.getItem('liveStreamDetails'))?.streamKeys ?? {
                streamKey1: null,
                streamKey2: null,
                streamKey3: null
            },
            closeDialog: false,
            recordingO: JSON.parse(jitsiLocalStorage.getItem('liveStreamDetails'))?.recording || false,
            sharingEnabled: true,
            spaceLeft: undefined,
            selectedRecordingService,
            recordingEnabled: false,
            recStreamEnabled: false,
            dialogOpen: false,
            liveStreamTools: props._liveStream,
            vap: false,
            recTools: props._recording,
            datatitle: null,
            recTypeData: {},
            isAws: false,
            isMobile: false,
            isLoaded: true,
            dropLoaded: false
        };

        /**
         * Instance variable used to flag whether the component is or is not
         * mounted. Used as a hack to avoid setting state on an unmounted
         * component.
         *
         * @private
         * @type {boolean}
         */
        // this._isMounted = false;

        this._onCancel = this._onCancel.bind(this);
        this._onDisable = this._onDisable.bind(this);
        this._onSelectedRecordingServiceChanged
        = this._onSelectedRecordingServiceChanged.bind(this);
        this._onSharingSettingChanged = this._onSharingSettingChanged.bind(this);
        this._onSubmit = this._onSubmit.bind(this);
        this._onSubmitNative = this._onSubmitNative.bind(this);
    }

    /**
     * Implements {@link Component#componentDidMount()}. Invoked immediately
     * after this component is mounted.
     *
     * @inheritdoc
     * @returns {void}
     */
    async componentDidMount() {
        // this._isMounted = true;
        if (typeof this.props.token !== 'undefined') {
            this._onTokenUpdated();
        }
        const { apiN: { API_URL_PREFIX, API_VERSION_PREFIX, MH_CLIENT_ID } } = this.props;
        const prejoinAP = `${API_URL_PREFIX}${API_VERSION_PREFIX}${ULR_PATHS.AUTO_REC_STREAM}`;
        const moement = moment().seconds(0)
        .milliseconds(0)
        .format('X');

        const mesh256 = sha256(`${apiKEY}:MH:${moement}`);

        axios({
            url: prejoinAP,
            method: 'post',
            headers: { 'Content-Type': 'application/json'
            },
            data: {
                client_id: MH_CLIENT_ID,
                meeting_id: this.props._roomName,
                credentials: mesh256,
                ...isDomainWeborNative
            }
        }).then(redordingResponse => {
            if (redordingResponse?.data?.success === false) {
                this.setState(prevState => {
                    return {
                        ...prevState,
                        dropLoaded: !prevState.dropLoaded,
                        isLoaded: false
                    };
                });

            }
            if (redordingResponse?.data?.success === true) {
                const recData = redordingResponse.data;

                // console.log('recData', recData?.default_recording_storage);
                if (recData?.default_recording_storage === 'Dropbox') {
                    this.setState(prevState => {
                        return {
                            ...prevState,
                            dropLoaded: !prevState.dropLoaded
                        };
                    });
                    this.props.dispatch(updateDropboxToken(recData?.settings?.dropbox_access_token?.access_token,
                        recData?.settings?.dropbox_access_token?.refresh_token, recData?.settings?.dropbox_access_token?.expires));
                }
                this.setState(prevState => {
                    return {
                        ...prevState,
                        recTypeData: { settings: recData.settings,
                            storage: recData.default_recording_storage,
                            s3_free_space: recData?.s3_free_space ? recData?.s3_free_space : null,
                            s3_used_space: recData?.s3_used_space ? recData?.s3_used_space : null
                        },
                        isAws: Boolean(recData?.default_recording_storage !== 'Dropbox' && !_.isNil(recData?.default_recording_storage)),
                        isLoaded: false
                    };
                });

            }
        })
.catch(es => {
    this.setState(prevState => {
        return {
            ...prevState,
            isLoaded: false
        };
    });
    logger.error('Error in fetching prejoin api For guest', es);
});


    }

    /**
     * Implements React's {@link Component#componentWillUnmount()}. Invoked
     * immediately before this component is unmounted and destroyed.
     *
     * @inheritdoc
     */
    componentWillUnmount() {
        // this._isMounted = false;
    }

    /**
     * Validates the oauth access token.
     *
     * @inheritdoc
     * @returns {void}
     */
    componentDidUpdate(prevProps: Props) {
        if (this.props.token !== prevProps.token) {
            this._onTokenUpdated();
        }
    }

    _areIntegrationsEnabled: () => boolean;

    /**
     * Returns true if the integrations with third party services are enabled
     * and false otherwise.
     *
     * @returns {boolean} - True if the integrations with third party services
     * are enabled and false otherwise.
     */
    _areIntegrationsEnabled() {
        return this.props._isDropboxEnabled;
    }

    _onSharingSettingChanged: () => void;

    /**
     * Callback to handle sharing setting change from the dialog.
     *
     * @returns {void}
     */
    _onSharingSettingChanged() {
        this.setState({
            sharingEnabled: !this.state.sharingEnabled
        });
    }

    _onSelectedRecordingServiceChanged: (string) => void;

    /**
     * Handles selected recording service changes.
     *
     * @param {string} selectedRecordingService - The new selected recording
     * service.
     * @returns {void}
     */
    _onSelectedRecordingServiceChanged(selectedRecordingService) {
        this.setState({ selectedRecordingService });
    }


    /**
     * Validates the dropbox access token and fetches account information.
     *
     * @returns {void}
     */
    _onTokenUpdated() {
        const { _appKey, _isDropboxEnabled, token, _accessToken, _tokenExpireDate, _rToken, dispatch } = this.props;

        if (!_isDropboxEnabled) {
            return;
        }
        if (typeof token === 'undefined') {
            this.setState({
                isTokenValid: false,
                isValidating: false
            });
        } else {

            if (_tokenExpireDate && Date.now() > new Date(_tokenExpireDate) && checkIfNotReactNative() === true) {
                getNewAccessToken(_appKey, _rToken)
                    .then(resp => dispatch(updateDropboxToken(resp.token, resp.rToken, resp.expireDate)));

                return;
            }
            if (!this.state.dropLoaded && !_accessToken) {
                const { apiN: { API_URL_PREFIX, API_VERSION_PREFIX } } = this.props;
                const prejoinAP = `${API_URL_PREFIX}${API_VERSION_PREFIX}customer/savedropboxtoken`;

                axios({
                    url: prejoinAP,
                    method: 'post',
                    headers: { 'Content-Type': 'application/json',
                        'Authorization': `Bearer ${_accessToken}`
                    },
                    data: {
                        meeting_id: this.props._roomName,
                        dropbox_token: token,
                        refresh_token: _rToken,
                        expires: `${_tokenExpireDate}`
                    }
                }).then(() => {
                    // console.log('AccessTkn', response);
                })
                .catch(e => logger.error('eee34', e));
                this.setState({
                    isTokenValid: false,
                    isValidating: true
                });
            }
            this.setState({
                isTokenValid: false,
                isValidating: true
            });
            getDropboxData(token, _appKey).then(data => {
                if (typeof data === 'undefined') {
                    this.setState({
                        isTokenValid: false,
                        isValidating: false
                    });
                } else {
                    this.setState({
                        isTokenValid: true,
                        isValidating: false,
                        ...data
                    });
                }
            });
        }
    }
    _onCancel: () => boolean;

    /**
     * Invokes the passed in {@link onCancel} callback and closes
     * {@code StartLiveStreamDialog}.
     *
     * @private
     * @returns {boolean} True is returned to close the modal.
     */
    _onCancel() {
        sendAnalytics(createLiveStreamingDialogEvent('start', 'cancel.button'));

        return true;
    }

    _onDisable: () => Boolean;

    /**
     * Invokes the passed in {@link _onDisable} callback and closes
     * {@code StartLiveStreamDialog}.
     *
     * @param {boolean} webValue - WEb.
     * @private
     * @returns {boolean} True is returned to close the modal.
     */
    _onDisable(webValue = false) {
        if (webValue) {

            return !(this.state.streamEnabledWeb.length > 0 && this.state.streamEnabledWeb.every(it => it?.value !== undefined || (it?.isRec === true)));
        }
        const keyState = this.state.streamKeys;

        if ((!keyState.streamKey1 && !keyState.streamKey2 && !keyState.streamKey3 && !this.state.recordingO)
         || ((keyState.streamKey1 === null && !keyState.streamKey2 === null && !keyState.streamKey3 === null
            && !this.state.recordingO))) {
            return true;
        }

        return false;

    }

    /**
     * Asks the user to sign in, if not already signed in, and then requests a
     * list of the user's YouTube broadcasts.
     *
     * NOTE: To be implemented by platforms.
     *
     * @private
     * @returns {Promise}
     */
    _onGetYouTubeBroadcasts: () => Promise<*>;

    _onSubmit: () => boolean;

    /**
     * Invokes the passed in {@link onSubmit} callback with the entered stream
     * key, and then closes {@code StartLiveStreamDialog}.
     *
     * @private
     * @returns {boolean} False if no stream key is entered to preventing
     * closing, true to close the modal.
     */
    _onSubmit() {
        const { broadcasts, selectedBoundStreamID } = this.state;
        const { token, _isDropboxEnabled, apiN: { API_URL_PREFIX, API_VERSION_PREFIX, MH_CLIENT_ID },
            _accessToken, _roomName, _autoCaptionOnRecord, _confSettings, _requestingSubtitles } = this.props;

        let appData;
        const attributes = {};
        let selectedBroadcastID = null;

        if (selectedBoundStreamID) {
            const selectedBroadcast = broadcasts && broadcasts.find(
                broadcast => broadcast.boundStreamID === selectedBoundStreamID);

            selectedBroadcastID = selectedBroadcast && selectedBroadcast.id;
        }

        const streamKeys = { ...this.state.streamKeys };

        const streamsEnable = { ...this.state.streamsEnabled };
        const streamWeb = JSON.stringify(this.state.streamEnabledWeb);
        const streams = {
            streamKeys,

            // streamEnable,
            streamsEnable,
            recording: this.state.recordingO
        };
        const storageAssign = JSON.stringify(streams);

        jitsiLocalStorage.setItem('streamEnabledWeb', streamWeb);
        jitsiLocalStorage.setItem('liveStreamDetails', storageAssign);
        sendAnalytics(
            createLiveStreamingDialogEvent('start', 'confirm.button'));

        if (_isDropboxEnabled && ((this.state.streamsEnabled['DropBox Recording']
                 && token) || this.state.recTypeData || this.state.isAws)) {
            const serviceData = this.state.isAws ? this.state.recTypeData : {
                'token': token
            };
            const recordURL = `${API_URL_PREFIX}${API_VERSION_PREFIX}customer/saverecordings`;
            const decryptUrl = `${API_URL_PREFIX}${API_VERSION_PREFIX}customer/recordsettings_decrypt`;

            appData = JSON.stringify({
                'file_recording_metadata': {
                    'upload_credentials': {
                        'service_name': this.state.isAws ? 'awss3' : RECORDING_TYPES.DROPBOX,
                        'recordingType': this.state.recTypeData.storage,
                        ...serviceData,
                        'userid': this.props._userId?.id,
                        _accessToken,
                        client_id: MH_CLIENT_ID,
                        api_key: apiKEY,
                        decryptUrl,
                        ...isDomainWeborNative,
                        'urlSaved': recordURL,
                        'startTiming': new Date().getTime(),
                        // eslint-disable-next-line camelcase
                        'meetingid': _roomName
                    }
                }
            });
            attributes.type = RECORDING_TYPES.DROPBOX;
        } else {
            appData = JSON.stringify({
                'file_recording_metadata': {
                    'share': this.state.sharingEnabled
                }
            });
            attributes.type = RECORDING_TYPES.JITSI_REC_SERVICE;
        }

        const Mode = this.state.recordingO ? MHRecordingConstants.mode.FILE : MHRecordingConstants.mode.STREAM;

        // const stateKeys = this.state.streamKeys;
        const stateEnabled = this.state.streamEnabledWeb;


        const streamEntries = Array.isArray(stateEnabled) ? stateEnabled.map(itm => {
            let rtmpUrl;


            if (DEFAULT_RTMPS_STATE.hasOwnProperty(itm.title) && typeof itm?.value !== 'undefined') {
                const rtmpLink = DEFAULT_RTMPS_STATE[`${itm.title}`];

                if (itm?.value?.match(/^((rtmps|rtmp):\/\/.*\/.*)?$/)) {
                    rtmpUrl = itm.value;
                } else {
                    rtmpUrl = `${rtmpLink}${itm?.value}`;

                }

            } else {
                rtmpUrl = null;
            }


            return rtmpUrl;
        }).filter(it => it !== null) : [];

        // const streamObject = Object.fromEntries(streamEntries);
        const arrayLength = streamEntries.length;
        const checIFRecordingisOn = stateEnabled.some(it => it.isRec === true);


        if (this.state.recordingO || checIFRecordingisOn) {

            this.props.dispatch(recordingandLivestreamingOn(true));
            this.props._conference.sendCommand(IS_RECORDING_AND_LIVESTREAMING_ON, {
                value: true
            });

            // eslint-disable-next-line no-negated-condition

            this.props._conference.startRecording({
                broadcastId: selectedBroadcastID,
                mode: Mode,
                appData,
                streamId: arrayLength >= 1 ? streamEntries[0] : null,
                fbstreamId: arrayLength > 1 ? streamEntries[1] : null
            });


        } else {
            this.props._conference.startRecording({
                broadcastId: selectedBroadcastID,
                mode: Mode,
                streamId: arrayLength >= 1 ? streamEntries[0] : null,
                fbstreamId: arrayLength > 1 ? streamEntries[1] : null,
                igStreamid: arrayLength >= 2 ? streamEntries[2] : null
            });
        }

        if (_confSettings?.AUTO_CAPTION_ON_RECORDING ?? _autoCaptionOnRecord) {
            if (_confSettings?.TRANSCRIPTION_QUOTA_EXCEEDED === 1 ?? false) {
                this.props.dispatch(showErrorNotification(
                    {
                        descriptionKey: 'transcribing.transcriptionQuotaExceeded',
                        titleKey: 'transcribing.transcriptionQuotaExceededTitle'
                    }));
            } else if (_requestingSubtitles === true) {
                // no action
            } else {
                this.props.dispatch(toggleRequestingSubtitles());
            }
        }

        return true;
    }

    _onSubmitNative: () => boolean;


    /**
     * Invokes the passed in {@link onSubmit} callback with the entered stream
     * key, and then closes {@code StartLiveStreamDialog}.
     *
     * @private
     * @returns {boolean} False if no stream key is entered to preventing
     * closing, true to close the modal.
     */
    _onSubmitNative() {
        const { broadcasts, selectedBoundStreamID } = this.state;
        const { token, _isDropboxEnabled, apiN: { API_URL_PREFIX, API_VERSION_PREFIX, MH_CLIENT_ID },
            _accessToken, _roomName, _confSettings, _requestingSubtitles } = this.props;

        let appData;
        const attributes = {};
        let selectedBroadcastID = null;

        if (selectedBoundStreamID) {
            const selectedBroadcast = broadcasts && broadcasts.find(
                    broadcast => broadcast.boundStreamID === selectedBoundStreamID);

            selectedBroadcastID = selectedBroadcast && selectedBroadcast.id;
        }

        const streamKeys = { ...this.state.streamKeys };

        const streamMobile = JSON.stringify(this.state.streamEnabledMobile);
        const streamsEnable = { ...this.state.streamsEnabled };
        const streams = {
            streamKeys,
            streamsEnable,
            recording: this.state.recordingO
        };
        const storageAssign = JSON.stringify(streams);

        jitsiLocalStorage.setItem('streamEnabledMobile', streamMobile);
        jitsiLocalStorage.setItem('liveStreamDetails', storageAssign);
        sendAnalytics(
                createLiveStreamingDialogEvent('start', 'confirm.button'));

        if (_isDropboxEnabled && ((this.state.streamsEnabled['DropBox Recording']
                && token) || this.state.recTypeData || this.state.isAws)) {
            const serviceData = this.state.isAws ? this.state.recTypeData : {
                'token': token
            };

            const recordURL = `${API_URL_PREFIX}${API_VERSION_PREFIX}customer/saverecordings`;
            const decryptUrl = `${API_URL_PREFIX}${API_VERSION_PREFIX}customer/recordsettings_decrypt`;

            appData = JSON.stringify({
                'file_recording_metadata': {
                    'upload_credentials': {
                        'service_name': this.state.isAws ? 'awss3' : RECORDING_TYPES.DROPBOX,
                        'recordingType': this.state.recTypeData.storage,
                        ...serviceData,
                        'userid': this.props._userId?.id,
                        _accessToken,
                        client_id: MH_CLIENT_ID,
                        api_key: apiKEY,
                        decryptUrl,
                        ...isDomainWeborNative,
                        'urlSaved': recordURL,
                        'startTiming': new Date().getTime(),
                        // eslint-disable-next-line camelcase
                        'meetingid': _roomName
                    }
                }
            });
            attributes.type = RECORDING_TYPES.DROPBOX;
        } else {
            appData = JSON.stringify({
                'file_recording_metadata': {
                    'share': this.state.sharingEnabled
                }
            });
            attributes.type = RECORDING_TYPES.JITSI_REC_SERVICE;
        }

        const Mode = this.state.recordingO ? MHRecordingConstants.mode.FILE : MHRecordingConstants.mode.STREAM;

        // const stateKeys = this.state.streamKeys;
        const stateEnabled = this.state.streamEnabledMobile;


        const streamEntries = Array.isArray(stateEnabled) ? stateEnabled.map(itm => {
            let rtmpUrl;


            if (DEFAULT_RTMPS_STATE.hasOwnProperty(itm.title) && typeof itm?.value !== 'undefined') {
                const rtmpLink = DEFAULT_RTMPS_STATE[`${itm.title}`];

                if (itm?.value?.match(/^((rtmps|rtmp):\/\/.*\/.*)?$/)) {
                    rtmpUrl = itm.value;
                } else {
                    rtmpUrl = `${rtmpLink}${itm?.value}`;

                }

            } else {
                rtmpUrl = null;
            }


            return rtmpUrl;
        }).filter(it => it !== null) : [];

        const arrayLength = streamEntries.length;
        const checIFRecordingisOn = stateEnabled.some(it => it.isRec === true);

        if (this.state.recordingO || checIFRecordingisOn) {
            this.props._conference.sendCommand(IS_RECORDING_AND_LIVESTREAMING_ON, {
                value: true
            });

            this.props._conference.startRecording({
                broadcastId: selectedBroadcastID,
                mode: Mode,
                appData,
                streamId: arrayLength >= 1 ? streamEntries[0] : null,
                fbstreamId: arrayLength > 1 ? streamEntries[1] : null
            });
        } else {
            this.props._conference.startRecording({
                broadcastId: selectedBroadcastID,
                mode: Mode,
                streamId: arrayLength >= 1 ? streamEntries[0] : null,
                fbstreamId: arrayLength > 1 ? streamEntries[1] : null,
                igStreamid: arrayLength >= 2 ? streamEntries[2] : null
            });
        }

        if (_confSettings?.AUTO_CAPTION_ON_RECORDING ?? this.props._autoCaptionOnRecord) {
            if (_confSettings?.TRANSCRIPTION_QUOTA_EXCEEDED === 1 ?? false) {
                this.props.dispatch(showErrorNotification(
                    {
                        descriptionKey: 'transcribing.transcriptionQuotaExceeded',
                        titleKey: 'transcribing.transcriptionQuotaExceededTitle'
                    }));
            } else if (_requestingSubtitles === true) {
                // no action
            } else {
                this.props.dispatch(toggleRequestingSubtitles());
            }
        }

        return true;
    }


}

/**
 * Maps part of the Redux state to the component's props.
 *
 * @param {Object} state - The Redux state.
 * @returns {{
 *     _conference: Object,
 *     _googleAPIState: number,
 *     _googleProfileEmail: string,
 *     _streamKey: string
 * }}
 */
export function _mapStateToProps(state: Object) {

    const {
        transcription,
        fileRecordingsServiceEnabled = false,
        fileRecordingsServiceSharingEnabled = false,
        dropbox = {}
    } = state['features/base/config'];
    const { LiveStream, Recording } = state['features/recording'];
    const { _requestingSubtitles } = state['features/subtitles'];
    const { interfaceConfig: _interfaceConfig, API_URL_PREFIX, API_VERSION_PREFIX, MH_CLIENT_ID } = state['features/base/config'];
    const apiN = { API_URL_PREFIX,
        API_VERSION_PREFIX,
        MH_CLIENT_ID };

    const { isRecordingandLivestreamingOn = false } = state['features/base/mtApi'];


    return {
        _conference: state['features/base/conference'].conference,
        _googleAPIState: state['features/google-api'].googleAPIState,
        _googleProfileEmail: state['features/google-api'].profileEmail,
        _streamKey: state['features/recording'].streamKey,
        _appKey: dropbox.appKey,
        _autoCaptionOnRecord: transcription?.autoCaptionOnRecord ?? false,
        _fileRecordingsServiceEnabled: fileRecordingsServiceEnabled,
        _fileRecordingsServiceSharingEnabled: fileRecordingsServiceSharingEnabled,
        _isDropboxEnabled: isDropboxEnabled(state),
        token: state['features/dropbox'].token,
        _liveStream: LiveStream,
        _recording: Recording,
        _roomName: state['features/base/conference']?.room,
        _userId: state['features/base/jwt']?.meetingDetails,
        _interfaceConfig,
        _accessToken: state['features/base/jwt']?.accessToken,
        apiN,
        _rToken: state['features/dropbox'].rToken,
        _tokenExpireDate: state['features/dropbox'].expireDate,
        _isRecordingandLivestreamingOn: isRecordingandLivestreamingOn,
        _confSettings: state['features/base/jwt']?.confSettings,
        _requestingSubtitles
    };
}
