// @flow

import axios from 'axios';
import { sha256 } from 'js-sha256';
import _ from 'lodash';
import moment from 'moment';
import { Component } from 'react';

import {
    createRecordingDialogEvent,
    sendAnalytics
} from '../../../analytics';
import logger from '../../../app/logger';
import { apiKEY } from '../../../base/config/constants';
import { MHRecordingConstants } from '../../../base/lib-meet-hour';
import { ULR_PATHS } from '../../../base/mtApi/constants';
import { getLocalParticipant, recordingStartedByUser, showRecordingConsentModal } from '../../../base/participants';
import { checkIfNotReactNative, isDomainWeborNative } from '../../../base/util/checkOS';
import {
    getDropboxData,
    getNewAccessToken,
    isEnabled as isDropboxEnabled,
    updateDropboxToken
} from '../../../dropbox';
import { showErrorNotification } from '../../../notifications';
import { toggleRequestingSubtitles } from '../../../subtitles/actions';
import { RECORDING_TYPES } from '../../constants';

type Props = {

    /**
     * Requests subtitles when recording is turned on.
     */
    _autoCaptionOnRecord: boolean,

    /**
     * The {@code MHConference} for the current conference.
     */
    _conference: Object,

    /**
     * The app key for the dropbox authentication.
     */
    _appKey: string,

    /**
     * Whether to show file recordings service, even if integrations
     * are enabled.
     */
    _fileRecordingsServiceEnabled: boolean,

    /**
     * The dropbox refresh token.
     */
    _rToken: string,

    /**
     * Whether to show the possibility to share file recording with other people (e.g. Meeting participants), based on
     * the actual implementation on the backend.
     */
    _fileRecordingsServiceSharingEnabled: boolean,

    /**
     * If true the dropbox integration is enabled, otherwise - disabled.
     */
    _isDropboxEnabled: boolean,

    /**
     * The dropbox access token.
     */
    _token: string,

    /**
     * The redux dispatch function.
     */
    dispatch: Function,

    /**
     * Access token's expiration date as UNIX timestamp.
     */
    _tokenExpireDate?: number,

    /**
     * Invoked to obtain translated strings.
     */
    t: Function,
    apiN: ?Object,
    _accessToken: ?Object,
    _userId: ?string,
    _roomName: ?string,

    /**
     * The list of all participants in the conference.
     */
    _participants: Array<Object>
}

type State = {

    /**
     * <tt>true</tt> if we have valid oauth token.
     */
    isTokenValid: boolean,

    /**
     * <tt>true</tt> if we are in process of validating the oauth token.
     */
    isValidating: boolean,

    /**
     * The currently selected recording service of type: RECORDING_TYPES.
     */
    selectedRecordingService: ?string,

    /**
     * True if the user requested the service to share the recording with others.
     */
    sharingEnabled: boolean,

    /**
     * Number of MiB of available space in user's Dropbox account.
     */
    spaceLeft: ?number,


    /**
     * The display name of the user's Dropbox account.
     */
    userName: ?string
};

/**
 * Component for the recording start dialog.
 */
class AbstractStartRecordingDialog extends Component<Props, State> {
    /**
     * Initializes a new {@code StartRecordingDialog} instance.
     *
     * @inheritdoc
     */
    constructor(props: Props) {
        super(props);

        // Bind event handler so it is only bound once for every instance.
        this._onSubmit = this._onSubmit.bind(this);
        this._onSelectedRecordingServiceChanged
            = this._onSelectedRecordingServiceChanged.bind(this);
        this._onSharingSettingChanged = this._onSharingSettingChanged.bind(this);

        let selectedRecordingService;

        // TODO: Potentially check if we need to handle changes of
        // _fileRecordingsServiceEnabled and _areIntegrationsEnabled()
        if (this.props._fileRecordingsServiceEnabled
                || !this._areIntegrationsEnabled()) {
            selectedRecordingService = RECORDING_TYPES.JITSI_REC_SERVICE;
        } else if (this._areIntegrationsEnabled()) {
            selectedRecordingService = RECORDING_TYPES.DROPBOX;
        }

        this.state = {
            isTokenValid: false,
            isValidating: false,
            userName: undefined,
            sharingEnabled: true,
            spaceLeft: undefined,
            selectedRecordingService,
            recTypeData: {},
            isAws: false,
            isLoaded: true,
            dropLoaded: false
        };
    }

    /**
     * Validates the oauth access token.
     *
     * @inheritdoc
     * @returns {void}
     */
    componentDidMount() {
        if (typeof this.props._token !== 'undefined') {
            this._onTokenUpdated();
        }

        const { apiN: { API_URL_PREFIX, API_VERSION_PREFIX, MH_CLIENT_ID } } = this.props;
        const prejoinAP = `${API_URL_PREFIX}${API_VERSION_PREFIX}${ULR_PATHS.AUTO_REC_STREAM}`;
        const moement = moment().seconds(0)
        .milliseconds(0)
        .format('X');

        const mesh256 = sha256(`${apiKEY}:MH:${moement}`);

        axios({
            url: prejoinAP,
            method: 'post',
            headers: { 'Content-Type': 'application/json'
            },
            data: {
                client_id: MH_CLIENT_ID,
                meeting_id: this.props._roomName,
                credentials: mesh256,
                ...isDomainWeborNative
            }
        }).then(redordingResponse => {
            if (redordingResponse?.data?.success === false) {
                this.setState(prevState => {
                    return {
                        ...prevState,
                        dropLoaded: !prevState.dropLoaded,
                        isLoaded: false
                    };
                });

            }
            if (redordingResponse?.data?.success === true) {
                const recData = redordingResponse.data;


                if (recData?.default_recording_storage === 'Dropbox') {

                    this.setState(prevState => {
                        return {
                            ...prevState,
                            dropLoaded: !prevState.dropLoaded
                        };
                    });
                    this.props.dispatch(updateDropboxToken(recData?.settings?.dropbox_access_token?.access_token,
                        recData?.settings?.dropbox_access_token?.refresh_token, recData?.settings?.dropbox_access_token?.expires));
                }
                this.setState(prevState => {
                    return {
                        ...prevState,
                        recTypeData: { settings: recData.settings,
                            storage: recData.default_recording_storage,
                            s3_free_space: recData?.s3_free_space ? recData?.s3_free_space : null,
                            s3_used_space: recData?.s3_used_space ? recData?.s3_used_space : null
                        },
                        isAws: Boolean(recData?.default_recording_storage !== 'Dropbox' && !_.isNil(recData?.default_recording_storage)),
                        isLoaded: false
                    };

                    // , () => console.log('AbstractStartRecordingDialog', redordingResponse.data)
                });

            }
        })
.catch(es => {
    this.setState(prevState => {
        return {
            ...prevState,
            isLoaded: false
        };
    });
    logger.error('Error in fetching prejoin api For guest', es);
});

    }

    /**
     * Validates the oauth access token.
     *
     * @inheritdoc
     * @returns {void}
     */
    componentDidUpdate(prevProps: Props) {
        if (this.props._token !== prevProps._token) {
            this._onTokenUpdated();
        }
    }

    _areIntegrationsEnabled: () => boolean;

    /**
     * Returns true if the integrations with third party services are enabled
     * and false otherwise.
     *
     * @returns {boolean} - True if the integrations with third party services
     * are enabled and false otherwise.
     */
    _areIntegrationsEnabled() {
        return this.props._isDropboxEnabled;
    }

    _onSharingSettingChanged: () => void;

    /**
     * Callback to handle sharing setting change from the dialog.
     *
     * @returns {void}
     */
    _onSharingSettingChanged() {
        this.setState({
            sharingEnabled: !this.state.sharingEnabled
        });
    }

    _onSelectedRecordingServiceChanged: (string) => void;

    /**
     * Handles selected recording service changes.
     *
     * @param {string} selectedRecordingService - The new selected recording
     * service.
     * @returns {void}
     */
    _onSelectedRecordingServiceChanged(selectedRecordingService) {
        this.setState({ selectedRecordingService });
    }

    /**
     * Validates the dropbox access token and fetches account information.
     *
     * @returns {void}
     */
    _onTokenUpdated() {
        const { _appKey, _isDropboxEnabled, _token, _accessToken, _rToken, _tokenExpireDate, dispatch } = this.props;

        if (!_isDropboxEnabled) {
            return;
        }

        if (typeof _token === 'undefined') {
            this.setState({
                isTokenValid: false,
                isValidating: false
            });
        } else {

            if (_tokenExpireDate && Date.now() > new Date(_tokenExpireDate) && checkIfNotReactNative() === true) {
                getNewAccessToken(_appKey, _rToken)
                    .then(resp => dispatch(updateDropboxToken(resp.token, resp.rToken, resp.expireDate)));

                return;
            }

            // console.log('methour', this.state.dropLoaded && _accessToken, this.state.dropLoaded, _accessToken);
            if (this.state.dropLoaded && _accessToken) {
                const { apiN: { API_URL_PREFIX, API_VERSION_PREFIX } } = this.props;
                const prejoinAP = `${API_URL_PREFIX}${API_VERSION_PREFIX}customer/savedropboxtoken`;

                axios({
                    url: prejoinAP,
                    method: 'post',
                    headers: { 'Content-Type': 'application/json',
                        'Authorization': `Bearer ${_accessToken}`
                    },
                    data: {
                        meeting_id: this.props._roomName,
                        dropbox_token: _token,
                        refresh_token: _rToken,
                        expires: `${_tokenExpireDate}`

                    }
                }).then(() => {
                    // logger.info('AccessTkn', response);
                    // console.log('AccessTkn', response);
                })
                .catch(e => logger.error('eee34', e));
                this.setState({
                    isTokenValid: false,
                    isValidating: true
                });
            }

            getDropboxData(_token, _appKey).then(data => {
                // console.log('getDropboxData', data, _appKey);
                if (typeof data === 'undefined') {
                    this.setState({
                        isTokenValid: false,
                        isValidating: false
                    });
                } else {
                    this.setState({
                        isTokenValid: true,
                        isValidating: false,
                        ...data
                    });
                }
            })
            .catch(error => logger.log('getDropboxDataError', error));
        }
    }

    _onSubmit: () => boolean;

    /**
     * Starts a file recording session.
     *
     * @private
     * @returns {boolean} - True (to note that the modal should be closed).
     */
    _onSubmit() {
        const { _autoCaptionOnRecord, _conference, _isDropboxEnabled,
            _token, dispatch, _accessToken, apiN: { API_URL_PREFIX, API_VERSION_PREFIX, MH_CLIENT_ID },
            _rToken, _appKey, _roomName, _confSettings, _participants, _localparticipant, _requestingSubtitles } = this.props;
        let appData;
        const attributes = {};

        if (_isDropboxEnabled
                && (_token || !_.isNil(this.state.recTypeData?.settings))
                && this.state.selectedRecordingService
                    === RECORDING_TYPES.DROPBOX) {
            const serviceData = this.state.isAws ? this.state.recTypeData : {
                'token': _token
            };
            const recordURL = `${API_URL_PREFIX}${API_VERSION_PREFIX}customer/saverecordings`;
            const decryptUrl = `${API_URL_PREFIX}${API_VERSION_PREFIX}customer/recordsettings_decrypt`;

            appData = JSON.stringify({
                'file_recording_metadata': {
                    'upload_credentials': {
                        'service_name': this.state.isAws ? 'awss3' : RECORDING_TYPES.DROPBOX,
                        'recordingType': this.state.recTypeData.storage,
                        ...serviceData,
                        'userid': this.props._userId?.id,
                        _accessToken,
                        'r_token': _rToken,
                        'app_key': _appKey,
                        'urlSaved': recordURL,
                        decryptUrl,
                        client_id: MH_CLIENT_ID,
                        api_key: apiKEY,
                        ...isDomainWeborNative,
                        'startTiming': new Date().getTime(),
                        // eslint-disable-next-line camelcase
                        'meetingid': _roomName
                    }
                }
            });
            attributes.type = RECORDING_TYPES.DROPBOX;
        } else {
            appData = JSON.stringify({
                'file_recording_metadata': {
                    'share': this.state.sharingEnabled
                }
            });
            attributes.type = RECORDING_TYPES.JITSI_REC_SERVICE;
        }

        sendAnalytics(
            createRecordingDialogEvent('start', 'confirm.button', attributes)
        );

        // Show recording consent modal to guest participants before starting recording
        if (_participants && Array.isArray(_participants)) {
            _participants.forEach(participant => {
                // Show consent modal only to guest participants (non-moderators)

                if (participant.role !== 'moderator') {
                    dispatch(showRecordingConsentModal(participant.id));
                }
            });
        }

        _conference.startRecording({
            mode: MHRecordingConstants.mode.FILE,
            appData
        });

        if (_confSettings?.AUTO_CAPTION_ON_RECORDING ?? _autoCaptionOnRecord) {
            if (_confSettings?.TRANSCRIPTION_QUOTA_EXCEEDED === 1 ?? false) {
                dispatch(showErrorNotification(
                    {
                        descriptionKey: 'transcribing.transcriptionQuotaExceeded',
                        titleKey: 'transcribing.transcriptionQuotaExceededTitle'
                    }));
            } else if (_requestingSubtitles === true) {
                // no action
            } else {
                dispatch(toggleRequestingSubtitles());
            }
        }

        // Setting the Participant as the one who started the recording.
        dispatch(recordingStartedByUser(_localparticipant?.id));

        return true;
    }

    /**
     * Renders the platform specific dialog content.
     *
     * @protected
     * @returns {React$Component}
     */
    _renderDialogContent: () => React$Component<*>;
}

/**
 * Maps (parts of) the Redux state to the associated props for the
 * {@code StartRecordingDialog} component.
 *
 * @param {Object} state - The Redux state.
 * @private
 * @returns {{
 *     _appKey: string,
 *     _autoCaptionOnRecord: boolean,
 *     _conference: MHConference,
 *     _fileRecordingsServiceEnabled: boolean,
 *     _fileRecordingsServiceSharingEnabled: boolean,
 *     _isDropboxEnabled: boolean,
 *     _token: string
 * }}
 */
export function mapStateToProps(state: Object) {
    const {
        transcription,
        fileRecordingsServiceEnabled = false,
        fileRecordingsServiceSharingEnabled = false,
        dropbox = {}
    } = state['features/base/config'];
    const { _requestingSubtitles } = state['features/subtitles'];
    const { API_URL_PREFIX, API_VERSION_PREFIX, MH_CLIENT_ID } = state['features/base/config'];
    const apiN = { API_URL_PREFIX,
        API_VERSION_PREFIX,
        MH_CLIENT_ID };

    return {
        _appKey: dropbox.appKey,
        _autoCaptionOnRecord: transcription?.autoCaptionOnRecord ?? false,
        _conference: state['features/base/conference'].conference,
        _fileRecordingsServiceEnabled: fileRecordingsServiceEnabled,
        _fileRecordingsServiceSharingEnabled: fileRecordingsServiceSharingEnabled,
        _isDropboxEnabled: isDropboxEnabled(state),
        _token: state['features/dropbox'].token,
        _accessToken: state['features/base/jwt']?.accessToken,
        _userId: state['features/base/jwt']?.meetingDetails,
        _roomName: state['features/base/conference']?.room,
        _participants: state['features/base/participants'],
        apiN,
        _rToken: state['features/dropbox'].rToken,
        _tokenExpireDate: state['features/dropbox'].expireDate,
        _confSettings: state['features/base/jwt']?.confSettings,
        _localparticipant: getLocalParticipant(state['features/base/participants']),
        _requestingSubtitles
    };
}

export default AbstractStartRecordingDialog;
