// @flow

import { Component } from 'react';

import { disconnect } from '../../../base/connection';
import { hideRecordingConsentModal } from '../../../base/participants';

type Props = {

    /**
     * The redux dispatch function.
     */
    dispatch: Function,

    /**
     * The local participant object.
     */
    _localParticipant: Object,

    /**
     * Invoked to obtain translated strings.
     */
    t: Function
}

/**
 * Component for the recording consent dialog.
 */
class AbstractRecordingConsentDialog extends Component<Props> {
    /**
     * Initializes a new {@code AbstractRecordingConsentDialog} instance.
     *
     * @inheritdoc
     */
    constructor(props: Props) {
        super(props);

        // Bind event handlers so they are only bound once for every instance.
        this._onAccept = this._onAccept.bind(this);
        this._onDecline = this._onDecline.bind(this);
        this._onClose = this._onClose.bind(this);
    }

    _onAccept: () => boolean;

    /**
     * Handles the accept button click.
     *
     * @private
     * @returns {boolean} - True (to note that the modal should be closed).
     */
    _onAccept() {
        const { dispatch, _localParticipant } = this.props;

        // Hide the consent modal
        if (_localParticipant?.id) {
            dispatch(hideRecordingConsentModal(_localParticipant.id));
        }

        // Here you can add logic to send consent response to the server
        // or store the consent status in Redux state

        return true;
    }

    _onDecline: () => boolean;

    /**
     * Handles the decline button click.
     *
     * @private
     * @returns {boolean} - True (to note that the modal should be closed).
     */
    _onDecline() {
        const { dispatch, _localParticipant } = this.props;

        // Hide the consent modal
        if (_localParticipant?.id) {
            dispatch(disconnect(false));
        }

        // Here you can add logic to handle declined consent
        // For example, you might want to leave the meeting or show a warning

        return true;
    }

    _onClose: () => boolean;

    /**
     * Handles the modal close (X button).
     * Note: Backdrop clicks are disabled for this modal.
     *
     * @private
     * @returns {boolean} - True (to note that the modal should be closed).
     */
    _onClose() {
        const { dispatch, _localParticipant } = this.props;

        // Hide the consent modal without disconnecting
        if (_localParticipant?.id) {
            dispatch(disconnect(false));
        }

        return true;
    }

    /**
     * Renders the platform specific dialog content.
     *
     * @protected
     * @returns {React$Component}
     */
    _renderDialogContent: () => React$Component<*>;
}

/**
 * Maps (parts of) the Redux state to the associated props for the
 * {@code AbstractRecordingConsentDialog} component.
 *
 * @param {Object} state - The Redux state.
 * @private
 * @returns {{
 *     _localParticipant: Object
 * }}
 */
export function mapStateToProps(state: Object) {
    const participants = state['features/base/participants'];
    const _localParticipant = participants.find(p => p.local);

    return {
        _localParticipant
    };
}

export default AbstractRecordingConsentDialog;
