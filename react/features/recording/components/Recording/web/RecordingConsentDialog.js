// @flow

import React from 'react';

import { Dialog } from '../../../../base/dialog';
import { translate } from '../../../../base/i18n';
import { connect } from '../../../../base/redux';
import AbstractRecordingConsentDialog, {
    mapStateToProps
} from '../AbstractRecordingConsentDialog';

const styles = {
    container: {
        padding: '16px 0'
    },
    message: {
        marginBottom: '16px',
        fontSize: '14px',
        lineHeight: '1.5'
    },
    disclaimer: {
        marginBottom: '0',
        fontSize: '12px',
        color: '#666',
        lineHeight: '1.4'
    }
};

/**
 * React Component for getting consent from participants before recording starts.
 *
 * @augments Component
 */
class RecordingConsentDialog extends AbstractRecordingConsentDialog {
    /**
     * Implements React's {@link Component#render()}.
     *
     * @inheritdoc
     * @returns {ReactElement}
     */
    render() {
        const { t } = this.props;

        return (
            <Dialog
                cancelKey = 'recording.consentDialog.leaveMeeting'
                customHeader = { this._renderCustomHeader }
                disableBlanketClickDismiss = { true }
                okKey = 'recording.consentDialog.accept'
                onCancel = { this._onDecline }
                onSubmit = { this._onAccept }
                titleKey = 'recording.consentDialog.title'
                width = 'small'>
                <div style = { styles.container }>
                    <p style = { styles.message }>
                        {t('recording.consentDialog.message')}
                    </p>
                    <p style = { styles.disclaimer }>
                        {t('recording.consentDialog.disclaimer')}
                    </p>
                </div>
            </Dialog>
        );
    }

    /**
     * Renders a custom header without the close icon.
     *
     * @private
     * @returns {ReactElement}
     */
    _renderCustomHeader() {
        const { t } = this.props;

        return (
            <div style={{
                padding: '20px 20px 0 20px',
                borderBottom: '1px solid #e0e0e0',
                marginBottom: '0'
            }}>
                <h1 style={{
                    margin: 0,
                    fontSize: '20px',
                    fontWeight: 'bold',
                    color: '#333'
                }}>
                    {t('recording.consentDialog.title')}
                </h1>
            </div>
        );
    }

    _onAccept: () => boolean;
    _onDecline: () => boolean;
}

export default translate(connect(mapStateToProps)(RecordingConsentDialog));
