// @flow

import axios from 'axios';
import { sha256 } from 'js-sha256';
import moment from 'moment';

import logger from '../app/logger';
import { apiKEY } from '../config/constants';
import getRoomName from '../config/getRoomName';
import { isDomainWeborNative } from '../util/checkOS';

declare var config: Object;


/**
 * Selector for retrieving the disabled sounds array.
 *
 * @param {Object} state - The Redux state.
 * @returns {Array<string>} - The disabled sound id's array.
 */
export function getDisabledSounds(state: Object) {
    const { confSettings } = state['features/base/jwt'];

    if (typeof confSettings?.disabledSounds !== 'undefined') {
        // Return immediately if confSettings exists
        return Promise.resolve(
            confSettings?.disabledSounds ?? state['features/base/config']?.disabledSounds ?? []
        );
    }

    // Handle async operation with getModeratorOptions
    return getModeratorOptions(state).then(moderatorOptions => {
        if (moderatorOptions !== 'undefined' && !moderatorOptions?.disabledSounds) {
            const optionsDisabledSounds = moderatorOptions?.disabledSounds === 'undefined'
                ? []
                : moderatorOptions?.disabledSounds;

            return (
                confSettings?.disabledSounds
            ?? state['features/base/config']?.disabledSounds
            ?? optionsDisabledSounds
            ?? []
            );
        }
    })
    .catch(error => {
        logger.error('Error fetching disabled sounds:', error);

        return [];
    });
}


/**
 * Selector for retrieving the disabled sounds array.
 *
 * @param {Object} state - The Redux state.
 * @returns {Object<any>} - Returns MeetingSetting Object
 */
export async function getModeratorOptions(state: Object) {

    const { API_URL_PREFIX, API_VERSION_PREFIX, MH_CLIENT_ID } = state['features/base/config'] ?? config;
    const roomName = state['features/base/conference']?.room ?? getRoomName();

    if (API_URL_PREFIX && API_VERSION_PREFIX && MH_CLIENT_ID) {
        try {

            const moderatorOptions = `${API_URL_PREFIX}${API_VERSION_PREFIX}meeting/moderatoroptions`;
            const moements = moment().seconds(0)
            .milliseconds(0)
            .format('X');

            const mesh256z = sha256(`${apiKEY}:MH:${moements}`);

            const response = await axios({

                url: moderatorOptions,
                method: 'post',
                headers: {
                    'Content-Type': 'application/json'
                },
                data: {
                    client_id: MH_CLIENT_ID,
                    credentials: mesh256z,
                    ...isDomainWeborNative,
                    meeting_id: roomName
                }
            }).catch(e => logger.error('Error in calling meeting/Moderatoroptions API', e?.status))
            .then(getUserData => {
                const settingData = getUserData?.data;

                if (typeof settingData !== 'undefined' && settingData?.settings) {
                    const { settings } = settingData;

                    return settings;
                }

                return {};

            });

            return response;

        } catch (e) {
            logger.error('catch', e);

            return {};
        }
    } else {
        return {};
    }
}

/**
 * getsoundsPath in our project
 *
 */
export function getSoundsPath() {
    return 'sounds';
}
