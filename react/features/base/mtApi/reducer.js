// @flow

import { ReducerRegistry } from '../redux';

import { AUTO_RECORD_STOP, INVALID_MEETING_JSON, IS_RECORDING_AND_LIVESTREAMING_ON,
    PRE_REG_API, SET_ACCESS_TOKEN_NATIVE, SET_AUTO_PASSWORD, SET_DESKTOP_APP, SET_IS_SWITCHING_BREAKOUT_ROOM, SET_OCCURANCE_ID, SET_PASSWORD_FORM_NATIVE,
    SET_USER_DETAILS,
    SET_VOICE_COMMAND_STRING } from './actionTypes';

const MT_API = 'features/base/mtApi';

const ApiData = {
    pregData: null,
    userDetails: null,
    occurenceId: null,
    accessToken: '',
    passwordForm: {
        on: false,
        conference: null
    },
    desktopapp: 0,
    voiceCommandString: null,
    invalidData: null,
    md5password: null,
    stopAutoRecord: false,
    isSwitchingBreakOutRoom: false,
    isRecordingandLivestreamingOn: false
};


/**
 * Reduces the Redux actions of the feature base/connection.
 */
ReducerRegistry.register(
    MT_API,
    (state: Object = ApiData, action: Object) => {

        switch (action.type) {
        case PRE_REG_API: {
            return {
                ...state,
                pregData: action.preRegData || null
            };
        }
        case SET_USER_DETAILS: {

            return {
                ...state,
                userDetails: action.userd
            };
        }

        case SET_AUTO_PASSWORD: {
            return {
                ...state,
                md5password: action.md5password
            };
        }

        case AUTO_RECORD_STOP: {
            return {
                ...state,
                stopAutoRecord: action.stopAutoRecord
            };
        }

        case IS_RECORDING_AND_LIVESTREAMING_ON: {
            return {
                ...state,
                isRecordingandLivestreamingOn: action.isRecordingandLivestreamingOn
            };
        }

        case INVALID_MEETING_JSON: {
            return {
                ...state,
                invalidData: action.data
            };
        }

        case SET_OCCURANCE_ID: {
            return {
                ...state,
                occurenceId: action.occurrenceId
            };
        }

        case SET_ACCESS_TOKEN_NATIVE: {
            return {
                ...state,
                accessToken: action.tokn
            };

        }

        case SET_DESKTOP_APP: {
            return {
                ...state,
                desktopapp: action.desktopapp
            };
        }

        case SET_PASSWORD_FORM_NATIVE: {

            return {
                ...state,
                passwordForm: action.formBool
            };
        }


        case SET_VOICE_COMMAND_STRING: {
            return {
                ...state,
                voiceCommandString: action.vsString
            };
        }
        case SET_IS_SWITCHING_BREAKOUT_ROOM : {
            return {
                ...state,
                isSwitchingBreakOutRoom: action.isSwitchingBreakOutRoom
            };
        }
        }

        return state;
    });
