/* eslint-disable no-async-promise-executor */
// @flow

import axios from 'axios';

import logger from '../app/logger';

declare var config: Object;


// eslint-disable-next-line max-params
export const fetchApi = async (url: string, methodtype: ?string, header: ?Object, body1: ?Object, timeout = 8000) => {

    // Create a new AbortController instance and extract its signal
    const controller = new AbortController();
    const { signal } = controller;

    // Create a timeout promise
    const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
            controller.abort();
            reject(new Error('Request timed out'));
        }, timeout);
    });

    try {

        const urlConfig = `${config.API_URL_PREFIX}${config.API_VERSION_PREFIX}${url}`;
        // eslint-disable-next-line max-len
        const res = (/(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%.+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_+.~#?&//=]*)/g).test(url);

        return new Promise(async (resolve, reject) => {
            try {

                // Race the fetch call against the timeout promise
                const response = await Promise.race([

                    fetch(res ? url : urlConfig, {
                        method: methodtype,
                        headers: header,
                        body: body1,
                        signal
                    }),
                    timeoutPromise
                ]);

                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }

                resolve(response.json());
            } catch (e) {
                if (e.name === 'AbortError') {
                    logger.error('Fetch request aborted due to timeout');
                } else {
                    logger.error(`Fetch request failed: ${e.message}`);
                }
                reject(e);
            }
        });
    } catch (error) {
        if (error.name === 'AbortError') {
            logger.error('Fetch request aborted due to timeout');
        } else {
            logger.error(`Fetch request failed: ${error.message}`);
        }

        return new Promise(reject => reject(error));
    }
};

// eslint-disable-next-line max-params
export const fetchUserApi = async (url: string, methodtype: ?string, header: ?Object, body1: ?Object, timeout = 8000) => {

    // Create a new AbortController instance and extract its signal
    const controller = new AbortController();
    const { signal } = controller;

    // Create a timeout promise
    const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
            controller.abort();
            reject(new Error('Request timed out'));
        }, timeout);
    });

    try {

        const urlConfig = `${config.API_URL_PREFIX}${config.API_VERSION_PREFIX}${url}`;
        // eslint-disable-next-line max-len
        const res = (/(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%.+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_+.~#?&//=]*)/g).test(url);

        // Race the fetch call against the timeout promise

        return new Promise(async (resolve, reject) => {
            try {
                const response = await Promise.race([

                    fetch(res ? url : urlConfig, {
                        method: methodtype,
                        headers: header,
                        body: body1,
                        signal
                    }),
                    timeoutPromise
                ]);

                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }

                resolve(response.json());
            } catch (e) {
                if (e.name === 'AbortError') {
                    logger.error('Fetch request aborted due to timeout');
                } else {
                    logger.error(`Fetch request failed: ${e.message}`);
                }
                reject(e);
            }
        });
    } catch (error) {
        if (error.name === 'AbortError') {
            logger.error('Fetch request aborted due to timeout');
        } else {
            logger.error(`Unexpected error: ${error.message}`);
        }
        throw error;
    }

};


// eslint-disable-next-line max-params
export const fetchAxiosApi = async (url: string, methodtype: ?string, header: ?Object, body1: ?Object, confg = undefined, timeout = 8000) => {

    const urlConfig = confg ? `${confg.API_URL_PREFIX}${confg.API_VERSION_PREFIX}${url}` : `${config.API_URL_PREFIX}${config.API_VERSION_PREFIX}${url}`;
    // eslint-disable-next-line max-len
    const res = (/(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%.+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_+.~#?&//=]*)/g).test(url);

    try {
        const data = await axios(
             {
                 url: res ? url : urlConfig,
                 timeout,
                 method: methodtype,
                 headers: header,
                 data: body1
             });

        return data.data;
    } catch (e) {
        logger.error(`Axios request failed: ${e.message}`);

        return e;
    }
};


