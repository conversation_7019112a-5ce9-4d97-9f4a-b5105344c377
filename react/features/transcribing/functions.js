/* global APP */
import i18next from 'i18next';

import { isLocalParticipantModerator } from '../base/participants/functions';
import { setDefaultTranscriptionLang } from '../subtitles/actions';

import logger from './logger';
import MEETHOUR_TO_BCP47_MAP from './meethour-bcp47-map.json';
import TRANSCRIBER_LANGS from './transcriber-langs.json';
import WHISPER_TRANSCRIBER_LANGS from './whisper-supported-langs.json';
import WHISPER_LANGS from './whisper.json';
const DEFAULT_TRANSCRIBER_LANG = 'en-US';
const DEFAULT_WHISPER_TRANSCRIBER_LANG = 'en';

/**
 * Determine which language to use for transcribing.
 *
 * @param {*} config - Application config.
 * @returns {string}
 */
export function determineTranscriptionLanguage(config) {
    const { transcription } = config;

    // if transcriptions are not enabled nothing to determine
    if (!transcription?.enabled) {
        return undefined;
    }

    const modelData = transcription.transGModel;
    const JigasiTranscriptionTech = transcription.type;

    // Depending on the config either use the language that the app automatically detected or the hardcoded
    // config BCP47 value.
    // MEETHOUR language detections uses custom language tags, but the transcriber expects BCP-47 compliant tags,
    // we use a mapping file to convert them.
    const bcp47Locale = JigasiTranscriptionTech === 1
        ? transcription?.useAppLanguage
            ? MEETHOUR_TO_BCP47_MAP[modelData][i18next.language]
            : transcription?.preferredLanguage
        : transcription?.useAppLanguage
            ? WHISPER_LANGS[i18next.language]
            : transcription?.preferredLanguage;

    // Check if the obtained language is supported by the transcriber
    let safeBCP47Locale = JigasiTranscriptionTech === 1 ? TRANSCRIBER_LANGS[bcp47Locale] && bcp47Locale : WHISPER_TRANSCRIBER_LANGS[bcp47Locale] && bcp47Locale;


    if (!safeBCP47Locale) {
        // dispatch the setDefaultTranscriptionLanguage
        APP.store.dispatch(setDefaultTranscriptionLang(true));
        safeBCP47Locale = JigasiTranscriptionTech === 1 ? DEFAULT_TRANSCRIBER_LANG : DEFAULT_WHISPER_TRANSCRIBER_LANG;
        logger.warn(`Transcriber language ${bcp47Locale} is not supported, using default ${DEFAULT_TRANSCRIBER_LANG}`);
    }

    logger.info(`Transcriber language set to ${safeBCP47Locale}`);

    return safeBCP47Locale;
}

/**
 * Returns whether there is transcribing.
 *
 * @param {Object} state - The redux state to search in.
 * @returns {boolean}
 */
export function isTranscribing(state: Object) {

    return state['features/transcribing'].isTranscribing;
}

/**
 * Returns true if there is a recorder transcription session running.
 * NOTE: If only the subtitles are running this function will return false.
 *
 * @param {Object} state - The redux state to search in.
 * @returns {boolean}
 */
export function isRecorderTranscriptionsRunning(state: Object) {
    const { metadata } = state['features/base/conference'];

    return isTranscribing(state) && Boolean(metadata?.recording?.isTranscribingEnabled);
}

/**
 * Checks whether the participant can start the transcription.
 *
 * @param {Object} state - The redux state.
 * @returns {boolean} - True if the participant can start the transcription.
 */
export function canAddTranscriber(state: Object) {
    const { transcription } = state['features/base/config'];
    const { confSettings } = state['features/base/jwt'];

    const isModerator = isLocalParticipantModerator(state);

    return Boolean(transcription?.enabled) && isModerator && Boolean(confSettings?.ENABLE_TRANSCRIPTION_MODE);
}
